import FacebookIcon from "../assets/media/Icons/facebook.svg";
import GoogleAdsIcon from "../assets/media/Icons/googleads.svg";
import SnapChatIcon from "../assets/media/Icons/snapchat.svg";
import TikTokIcon from "../assets/media/Icons/tiktok.svg";
import Instagram from "../assets/media/Icons/instagram.svg";
import LinkedInIcon from "../assets/media/Icons/linkedin.svg";
import phone from "../assets/media/Icons/phone.svg";
import whatsapp from "../assets/media/Icons/whatsapp.svg";
import GlobeIcon from "../assets/media/Icons/globe.svg";
import MessengerIcon from "../assets/media/Icons/meta-messenger.svg";
import InstagramIcon from "../assets/media/Icons/instagram-dm.svg";
// Using the same whatsapp icon for WhatsApp C and WhatsApp IN
import whatsappC from "../assets/media/Icons/whatsapp2.svg";
import webformArb from "../assets/media/Icons/webform.svg";
import whatsappIN from "../assets/media/Icons/whatsapp3.svg";
import { isUserExcluded } from "../config/packageVisibility";

// Base sources available to all users
const baseSources = {
    1: FacebookIcon,
    2: GoogleAdsIcon,
    3: SnapChatIcon,
    4: TikTokIcon,
    5: Instagram,
    6: LinkedInIcon,
    7: phone,
    8: whatsapp,
    9: GlobeIcon,
    10: MessengerIcon,
    11: InstagramIcon,
};

// Special sources only available to excluded users (IDs 423 and 1)
const specialSources = {
    12: whatsappC,     // WhatsApp C
    13: webformArb,    // Webform-Arb
    14: whatsappIN     // WhatsApp IN
};

// All sources combined
export const sourceToIcon = {
    ...baseSources,
    ...specialSources
};

// Base ordered keys for regular users
const baseOrderedKeys = [1, 10, 11, 2, 3, 4, 5, 6, 7, 8, 9];

// Special ordered keys only for excluded users
const specialOrderedKeys = [12, 13, 14];

// All ordered keys combined
const allOrderedKeys = [...baseOrderedKeys, ...specialOrderedKeys];

/**
 * Get sources available to a specific user
 * @param {number|string} userId - The user ID to check
 * @returns {Object} - Object mapping source IDs to icons
 */
export const getAvailableSources = (userId) => {
    if (isUserExcluded(userId)) {
        // Special users get all sources
        return sourceToIcon;
    }
    // Regular users get only base sources
    return baseSources;
};

/**
 * Get ordered source keys available to a specific user
 * @param {number|string} userId - The user ID to check
 * @returns {Array} - Array of source IDs in display order
 */
export const getOrderedSourceKeys = (userId) => {
    if (isUserExcluded(userId)) {
        // Special users get all sources
        return allOrderedKeys;
    }
    // Regular users get only base sources
    return baseOrderedKeys;
};

// Default export for backward compatibility (shows all sources)
export const orderedSourceKeys = allOrderedKeys;
