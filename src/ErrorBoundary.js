import { Navigate } from 'react-router-dom';
import React from 'react';

class ErrorBoundary extends React.Component {
    state = { error: null };

    static getDerivedStateFromError(error) {
        return { error };
    }

    componentDidCatch(error) {
        if (error.response?.status === 401) {
            this.props.setShowExpiredSessionModal(true);
        }
    }

    render() {
        const { error } = this.state;
        return error ? <Navigate to="/" /> : this.props.children;
    }
}

export default ErrorBoundary;
