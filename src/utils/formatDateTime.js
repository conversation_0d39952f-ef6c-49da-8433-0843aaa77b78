import { format, parseISO } from 'date-fns';

export const formatDateTime = (dateString) => {
  if (!dateString) return null;
  
  try {
    const date = parseISO(dateString);
    return format(date, 'yyyy-MM-dd HH:mm');
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

// If you need just the date without time
export const formatDate = (dateString) => {
  if (!dateString) return null;
  
  try {
    const date = parseISO(dateString);
    return format(date, 'yyyy-MM-dd');
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};