/**
 * Error handling utilities for comments system
 * Provides centralized error handling, retry logic, and offline state management
 */

import { toast } from 'react-toastify';

// Error types and codes
export const ERROR_TYPES = {
    NETWORK: 'NETWORK_ERROR',
    PERMISSION: 'PERMISSION_ERROR',
    VALIDATION: 'VALIDATION_ERROR',
    FIREBASE: 'FIREBASE_ERROR',
    AUTHENTICATION: 'AUTH_ERROR',
    OFFLINE: 'OFFLINE_ERROR',
    RATE_LIMIT: 'RATE_LIMIT_ERROR',
    UNKNOWN: 'UNKNOWN_ERROR'
};

// Firebase error codes mapping
const FIREBASE_ERROR_CODES = {
    'permission-denied': ERROR_TYPES.PERMISSION,
    'unauthenticated': ERROR_TYPES.AUTHENTICATION,
    'not-found': ERROR_TYPES.FIREBASE,
    'already-exists': ERROR_TYPES.FIREBASE,
    'resource-exhausted': ERROR_TYPES.RATE_LIMIT,
    'failed-precondition': ERROR_TYPES.FIREBASE,
    'aborted': ERROR_TYPES.FIREBASE,
    'out-of-range': ERROR_TYPES.VALIDATION,
    'unimplemented': ERROR_TYPES.FIREBASE,
    'internal': ERROR_TYPES.FIREBASE,
    'unavailable': ERROR_TYPES.NETWORK,
    'data-loss': ERROR_TYPES.FIREBASE,
    'deadline-exceeded': ERROR_TYPES.NETWORK
};

// Retry configuration
const RETRY_CONFIG = {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 10000, // 10 seconds
    backoffMultiplier: 2
};

/**
 * Enhanced error class for comments system
 */
export class CommentError extends Error {
    constructor(message, type = ERROR_TYPES.UNKNOWN, originalError = null, context = {}) {
        super(message);
        this.name = 'CommentError';
        this.type = type;
        this.originalError = originalError;
        this.context = context;
        this.timestamp = new Date().toISOString();
        this.retryable = this.isRetryable();
    }

    isRetryable() {
        const retryableTypes = [
            ERROR_TYPES.NETWORK,
            ERROR_TYPES.FIREBASE,
            ERROR_TYPES.RATE_LIMIT
        ];
        return retryableTypes.includes(this.type);
    }

    toJSON() {
        return {
            name: this.name,
            message: this.message,
            type: this.type,
            context: this.context,
            timestamp: this.timestamp,
            retryable: this.retryable,
            stack: this.stack
        };
    }
}

/**
 * Parse Firebase error and convert to CommentError
 * @param {Error} error - Original Firebase error
 * @param {Object} context - Additional context information
 * @returns {CommentError} - Parsed error
 */
export const parseFirebaseError = (error, context = {}) => {
    if (!error) {
        return new CommentError('Unknown error occurred', ERROR_TYPES.UNKNOWN, null, context);
    }

    // Check if it's already a CommentError
    if (error instanceof CommentError) {
        return error;
    }

    // Parse Firebase error code
    const errorCode = error.code || 'unknown';
    const errorType = FIREBASE_ERROR_CODES[errorCode] || ERROR_TYPES.FIREBASE;

    // Create user-friendly message
    let userMessage = getUserFriendlyMessage(errorCode, error.message);

    return new CommentError(userMessage, errorType, error, {
        ...context,
        firebaseCode: errorCode,
        originalMessage: error.message
    });
};

/**
 * Get user-friendly error message
 * @param {string} errorCode - Firebase error code
 * @param {string} originalMessage - Original error message
 * @returns {string} - User-friendly message
 */
const getUserFriendlyMessage = (errorCode, originalMessage) => {
    const messages = {
        'permission-denied': 'You do not have permission to perform this action',
        'unauthenticated': 'Please log in to continue',
        'not-found': 'The requested comment or chat was not found',
        'already-exists': 'This comment already exists',
        'resource-exhausted': 'Too many requests. Please try again later',
        'failed-precondition': 'Unable to complete the operation. Please try again',
        'unavailable': 'Service is temporarily unavailable. Please try again',
        'deadline-exceeded': 'Request timed out. Please check your connection',
        'internal': 'An internal error occurred. Please try again',
        'aborted': 'Operation was cancelled. Please try again'
    };

    const userMessage = messages[errorCode] || 'An unexpected error occurred. Please try again';

    // In development, include more details
    if (process.env.NODE_ENV === 'development' || !process.env.NODE_ENV) {
        return `${userMessage} (Code: ${errorCode}, Details: ${originalMessage})`;
    }

    return userMessage;
};

/**
 * Retry function with exponential backoff
 * @param {Function} operation - Async operation to retry
 * @param {Object} options - Retry options
 * @returns {Promise} - Result of the operation
 */
export const retryOperation = async (operation, options = {}) => {
    const config = { ...RETRY_CONFIG, ...options };
    let lastError;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
        try {
            return await operation();
        } catch (error) {
            lastError = parseFirebaseError(error, { attempt, maxRetries: config.maxRetries });

            // Don't retry if error is not retryable or we've reached max attempts
            if (!lastError.retryable || attempt === config.maxRetries) {
                throw lastError;
            }

            // Calculate delay with exponential backoff
            const delay = Math.min(
                config.baseDelay * Math.pow(config.backoffMultiplier, attempt),
                config.maxDelay
            );

            console.warn(`Operation failed (attempt ${attempt + 1}/${config.maxRetries + 1}). Retrying in ${delay}ms...`, {
                error: lastError.message,
                type: lastError.type
            });

            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw lastError;
};

/**
 * Handle comment operation errors with user feedback
 * @param {Error} error - Error to handle
 * @param {string} operation - Operation that failed
 * @param {Object} options - Handling options
 */
export const handleCommentError = (error, operation = 'comment operation', options = {}) => {
    const {
        showToast = true,
        logError = true,
        throwError = false
    } = options;

    const commentError = error instanceof CommentError ? error : parseFirebaseError(error);

    // Log error for debugging
    if (logError) {
        console.error(`Comment ${operation} failed:`, {
            error: commentError.toJSON(),
            context: commentError.context
        });
    }

    // Show user-friendly toast notification
    if (showToast) {
        const toastMessage = `${operation.charAt(0).toUpperCase() + operation.slice(1)} failed: ${commentError.message}`;

        if (commentError.type === ERROR_TYPES.NETWORK || commentError.type === ERROR_TYPES.OFFLINE) {
            toast.error(`${toastMessage}. Please check your internet connection.`);
        } else if (commentError.type === ERROR_TYPES.PERMISSION) {
            toast.error(`${toastMessage}. Please contact your administrator.`);
        } else if (commentError.type === ERROR_TYPES.RATE_LIMIT) {
            toast.warning(`${toastMessage}. Please wait a moment before trying again.`);
        } else {
            toast.error(toastMessage);
        }
    }

    // Throw error if requested
    if (throwError) {
        throw commentError;
    }

    return commentError;
};

/**
 * Network connectivity checker
 */
export class NetworkMonitor {
    constructor() {
        this.isOnline = navigator.onLine;
        this.listeners = new Set();

        // Listen for online/offline events
        window.addEventListener('online', this.handleOnline.bind(this));
        window.addEventListener('offline', this.handleOffline.bind(this));
    }

    handleOnline() {
        this.isOnline = true;
        this.notifyListeners('online');
    }

    handleOffline() {
        this.isOnline = false;
        this.notifyListeners('offline');
    }

    addListener(callback) {
        this.listeners.add(callback);
        return () => this.listeners.delete(callback);
    }

    notifyListeners(status) {
        this.listeners.forEach(callback => {
            try {
                callback(status, this.isOnline);
            } catch (error) {
                console.error('Network listener error:', error);
            }
        });
    }

    checkConnection() {
        return this.isOnline;
    }
}

// Global network monitor instance
export const networkMonitor = new NetworkMonitor();

/**
 * Offline queue for failed operations
 */
export class OfflineQueue {
    constructor() {
        this.queue = [];
        this.processing = false;

        // Process queue when coming back online
        networkMonitor.addListener((status) => {
            if (status === 'online' && this.queue.length > 0) {
                this.processQueue();
            }
        });
    }

    add(operation, context = {}) {
        this.queue.push({
            operation,
            context,
            timestamp: Date.now(),
            id: Math.random().toString(36).substr(2, 9)
        });
    }

    async processQueue() {
        if (this.processing || this.queue.length === 0) {
            return;
        }

        this.processing = true;
        const processedItems = [];

        while (this.queue.length > 0) {
            const item = this.queue.shift();

            try {
                await item.operation();
                processedItems.push({ ...item, status: 'success' });
            } catch (error) {
                console.error('Failed to process queued operation:', error);
                processedItems.push({ ...item, status: 'failed', error });

                // Re-queue if retryable
                const commentError = parseFirebaseError(error);
                if (commentError.retryable) {
                    this.queue.push(item);
                }
            }
        }

        this.processing = false;

        // Notify about processed items
        if (processedItems.length > 0) {
            const successCount = processedItems.filter(item => item.status === 'success').length;
            if (successCount > 0) {
                toast.success(`${successCount} queued comment(s) processed successfully`);
            }
        }
    }

    clear() {
        this.queue = [];
    }

    getQueueSize() {
        return this.queue.length;
    }
}

// Global offline queue instance
export const offlineQueue = new OfflineQueue();

/**
 * Wrapper for Firebase operations with error handling and offline support
 * @param {Function} operation - Firebase operation to execute
 * @param {Object} context - Context information for error handling
 * @param {Object} options - Options for error handling and retry
 * @returns {Promise} - Result of the operation
 */
export const executeWithErrorHandling = async (operation, context = {}, options = {}) => {
    const {
        enableRetry = true,
        enableOfflineQueue = true,
        retryOptions = {},
        errorOptions = {}
    } = options;

    try {
        // Check network connectivity
        if (!networkMonitor.checkConnection()) {
            const offlineError = new CommentError(
                'No internet connection available',
                ERROR_TYPES.OFFLINE,
                null,
                context
            );

            // Add to offline queue if enabled
            if (enableOfflineQueue) {
                offlineQueue.add(operation, context);
                toast.info('Operation queued for when connection is restored');
            }

            throw offlineError;
        }

        // Execute operation with retry if enabled
        if (enableRetry) {
            return await retryOperation(operation, retryOptions);
        } else {
            return await operation();
        }
    } catch (error) {
        return handleCommentError(error, context.operation || 'operation', {
            throwError: true,
            ...errorOptions
        });
    }
};
