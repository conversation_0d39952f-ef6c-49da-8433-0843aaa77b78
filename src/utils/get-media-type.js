/**
 * Determines the media type of a message based on its properties
 * @param {Object} message - The message object
 * @returns {string} - The media type (image, video, audio, file, text)
 */
export const getMediaType = (message) => {
    // If message has a type property, use that
    if (message.type) {
        return message.type;
    }

    // Otherwise try to determine from the message content
    if (typeof message.message === 'string') {
        // Check if it's an image URL
        if (
            message.message.match(/\.(jpeg|jpg|gif|png)$/i) ||
            (message.message.includes('firebasestorage.googleapis.com') &&
                (message.message.includes('image') || message.message.includes('png') ||
                    message.message.includes('jpg') || message.message.includes('jpeg') ||
                    message.message.includes('gif')))
        ) {
            return 'image';
        }

        // Check if it's a video URL
        if (
            message.message.match(/\.(mp4|webm|ogg|mov)$/i) ||
            (message.message.includes('firebasestorage.googleapis.com') &&
                message.message.includes('video'))
        ) {
            return 'video';
        }

        // Check if it's an audio URL
        if (
            message.message.match(/\.(mp3|wav|ogg)$/i) ||
            (message.message.includes('firebasestorage.googleapis.com') &&
                message.message.includes('audio'))
        ) {
            return 'audio';
        }

        // Check if it's a file URL
        if (
            message.message.includes('firebasestorage.googleapis.com') ||
            message.message.match(/\.(pdf|doc|docx|xls|xlsx|zip|rar)$/i)
        ) {
            return 'file';
        }
    }

    // Default to text
    return 'text';
};

