import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Set initial direction based on detected language
const setInitialDirection = (lng) => {
    document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
};

i18n
    .use(Backend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
        fallbackLng: 'en',
        supportedLngs: ['en', 'ar'],
        debug: false,
        interpolation: {
            escapeValue: false,
        },
        backend: {
            loadPath: '/locales/{{lng}}/translation.json',
        },
        detection: {
            order: ['localStorage', 'navigator'],
            caches: ['localStorage'],
        },
    })
    .then(() => {
        // Set direction after initialization
        setInitialDirection(i18n.language);

        // Watch for language changes
        i18n.on('languageChanged', (lng) => {
            document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
        });
    });

export default i18n;