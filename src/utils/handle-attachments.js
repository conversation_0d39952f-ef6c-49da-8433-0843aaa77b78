/**
 * Creates a FormData object from regular data that includes file attachments
 *
 * @param {Object} data - The data object containing fields and attachments
 * @param {string} attachmentFieldName - The field name to use for attachments (default: 'attachments')
 * @param {boolean} useArrayNotation - Whether to use array notation for the field name (default: true)
 * @returns {FormData} FormData object with all fields and attachments
 */
export const createFormDataWithAttachments = (data, attachmentFieldName = 'attachments', useArrayNotation = true) => {
    // Create FormData object to handle file uploads
    const formData = new FormData();

    // Check if data is valid
    if (!data || typeof data !== 'object') {
        return formData;
    }

    // Determine the field name for attachments
    const fieldName = useArrayNotation ? `${attachmentFieldName}[]` : attachmentFieldName;

    // Add all non-attachment fields to FormData
    Object.keys(data).forEach(key => {
        if (key !== attachmentFieldName) {
            // Handle nested objects by stringifying them
            if (typeof data[key] === 'object' && data[key] !== null && !(data[key] instanceof File)) {
                formData.append(key, JSON.stringify(data[key]));
            } else {
                formData.append(key, data[key]);
            }
        }
    });

    // Add each attachment file to FormData if attachments exist
    if (data[attachmentFieldName] && Array.isArray(data[attachmentFieldName])) {
        data[attachmentFieldName].forEach(file => {
            // Make sure we're appending the actual File object, not a custom object
            if (file instanceof File) {
                formData.append(fieldName, file);
            } else if (file && file.preview && file.path) {
                // For files from react-dropzone that might have been modified
                // Create a new File object from the original
                const newFile = new File(
                    [file],
                    file.name || file.path,
                    { type: file.type }
                );
                formData.append(fieldName, newFile);
            } else if (file && typeof file === 'object') {
                // Try to extract the file from the object
                console.warn('Attachment is not a File object:', file);
                // If we have a blob or similar, try to create a File
                if (file.size && file.type) {
                    try {
                        const newFile = new File(
                            [file],
                            file.name || 'file',
                            { type: file.type }
                        );
                        formData.append(fieldName, newFile);
                    } catch (error) {
                        console.error('Failed to create File from object:', error);
                    }
                }
            }
        });
    }

    return formData;
};

/**
 * Checks if the data contains file attachments
 *
 * @param {Object} data - The data object to check
 * @param {string} attachmentFieldName - The field name for attachments (default: 'attachments')
 * @returns {boolean} True if the data contains attachments
 */
export const hasAttachments = (data, attachmentFieldName = 'attachments') => {
    return data &&
        data[attachmentFieldName] &&
        Array.isArray(data[attachmentFieldName]) &&
        data[attachmentFieldName].length > 0;
};

