/**
 * Incoming Message Handler for Firebase Collection Consistency
 *
 * This utility provides a simple way to handle incoming messages and ensure
 * both the message collection and parent document are updated correctly.
 */

import { handleIncomingMessage, updateFirebaseParentDocument } from '../redux/features/metaBusinessChatSlice';

/**
 * Handle incoming messages with proper Firebase updates
 * @param {Function} dispatch - Redux dispatch function
 * @param {Array} messages - Array of incoming messages
 * @param {Object} selectedChat - The selected chat object (optional, will use from state if not provided)
 * @returns {Promise<Object>} Result of the operation
 */
export const handleIncomingMessages = async (dispatch, messages, selectedChat = null) => {
    try {
        console.log('[INCOMING_MESSAGE_HANDLER] Processing incoming messages:', {
            messagesCount: messages.length,
            hasSelectedChat: !!selectedChat
        });

        // Use the new handleIncomingMessage thunk
        const result = await dispatch(handleIncomingMessage({
            messages,
            selectedChat
        }));

        if (result.type.endsWith('/fulfilled')) {
            console.log('[INCOMING_MESSAGE_HANDLER] Successfully processed incoming messages:', result.payload);
            return {
                success: true,
                ...result.payload
            };
        } else {
            console.error('[INCOMING_MESSAGE_HANDLER] Failed to process incoming messages:', result.payload);
            return {
                success: false,
                error: result.payload
            };
        }

    } catch (error) {
        console.error('[INCOMING_MESSAGE_HANDLER] Error processing incoming messages:', error);
        return {
            success: false,
            error: error.message
        };
    }
};

/**
 * Handle a single incoming message
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} message - Single incoming message
 * @param {Object} selectedChat - The selected chat object (optional)
 * @returns {Promise<Object>} Result of the operation
 */
export const handleSingleIncomingMessage = async (dispatch, message, selectedChat = null) => {
    return handleIncomingMessages(dispatch, [message], selectedChat);
};

/**
 * Update Firebase parent document directly (for advanced use cases)
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} latestMessage - The latest message data
 * @returns {Promise<Object>} Result of the operation
 */
export const updateParentDocument = async (dispatch, selectedChat, latestMessage) => {
    try {
        console.log('[PARENT_DOCUMENT_UPDATER] Updating parent document:', {
            chatId: selectedChat?.id,
            messageId: latestMessage?.id
        });

        const result = await dispatch(updateFirebaseParentDocument({
            selectedChat,
            latestMessage
        }));

        if (result.type.endsWith('/fulfilled')) {
            console.log('[PARENT_DOCUMENT_UPDATER] Successfully updated parent document:', result.payload);
            return {
                success: true,
                ...result.payload
            };
        } else {
            console.error('[PARENT_DOCUMENT_UPDATER] Failed to update parent document:', result.payload);
            return {
                success: false,
                error: result.payload
            };
        }

    } catch (error) {
        console.error('[PARENT_DOCUMENT_UPDATER] Error updating parent document:', error);
        return {
            success: false,
            error: error.message
        };
    }
};

/**
 * Browser console helper for testing incoming messages
 * Usage: window.testIncomingMessage(dispatch, message, selectedChat)
 */
if (typeof window !== 'undefined') {
    window.testIncomingMessage = (dispatch, message, selectedChat) => {
        const testMessage = message || {
            id: `test_incoming_${Date.now()}`,
            message: 'Test incoming message',
            sender: 'test_sender',
            recipient: 'test_recipient',
            created_time: new Date().toISOString(),
            type: 'text'
        };

        console.log('🧪 Testing incoming message:', testMessage);
        return handleSingleIncomingMessage(dispatch, testMessage, selectedChat);
    };

    window.handleIncomingMessages = handleIncomingMessages;
    window.updateParentDocument = updateParentDocument;

    // Debug helper to check current Redux state
    window.debugMessageState = (store) => {
        const state = store.getState().metaBusinessSuite;
        console.log('🔍 Current Message State:', {
            selectedChat: state.selectedChat ? {
                id: state.selectedChat.id,
                type: state.selectedChat.sender_phone_number ? 'whatsapp' :
                    state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger'
            } : null,
            selectedPage: state.selectedPage,
            latestMessagesCount: state.latestMessages?.length || 0,
            messagesCount: state.messages?.length || 0,
            activeFilter: state.activeFilter
        });
        return state;
    };
}

export default {
    handleIncomingMessages,
    handleSingleIncomingMessage,
    updateParentDocument
};
