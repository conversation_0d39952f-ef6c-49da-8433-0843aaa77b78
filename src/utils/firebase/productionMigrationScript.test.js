/**
 * Tests for Production Migration Script
 *
 * Comprehensive test suite for the production Firebase collection migration script.
 * Tests batch processing, progress tracking, safety checks, and error handling.
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import {
    runProductionMigration,
    pauseMigration,
    resumeMigration,
    getMigrationProgress,
    emergencyStop,
    MIGRATION_CONFIG
} from './productionMigrationScript';

// Mock Firebase
vi.mock('../firebase.config', () => ({
    db: {
        collection: vi.fn(),
        doc: vi.fn()
    }
}));

// Mock Firebase Firestore functions
vi.mock('firebase/firestore', () => ({
    collection: vi.fn(),
    doc: vi.fn(),
    getDocs: vi.fn(),
    query: vi.fn(),
    orderBy: vi.fn(),
    limit: vi.fn(),
    startAfter: vi.fn(),
    writeBatch: vi.fn(),
    getDoc: vi.fn(),
    setDoc: vi.fn(),
    updateDoc: vi.fn(),
    serverTimestamp: vi.fn(() => ({ seconds: Date.now() / 1000 })),
    where: vi.fn()
}));

// Mock migration utilities
vi.mock('./dataMigration', () => ({
    migrateMessagesForChat: vi.fn(),
    verifyMigrationIntegrity: vi.fn(),
    getMigrationStatus: vi.fn()
}));

// Mock monitoring utilities
vi.mock('./migrationMonitoring', () => ({
    logMigrationOperation: vi.fn(),
    updateMigrationStatus: vi.fn(),
    getMigrationStatistics: vi.fn(),
    withPerformanceMonitoring: vi.fn((name, fn) => fn),
    LOG_LEVELS: {
        INFO: 'info',
        WARN: 'warn',
        ERROR: 'error',
        SUCCESS: 'success'
    },
    OPERATION_TYPES: {
        MIGRATION: 'migration',
        ROLLBACK: 'rollback',
        VERIFICATION: 'verification',
        CLEANUP: 'cleanup'
    },
    MIGRATION_STATUS: {
        NOT_STARTED: 'not_started',
        IN_PROGRESS: 'in_progress',
        COMPLETED: 'completed',
        FAILED: 'failed',
        ROLLED_BACK: 'rolled_back'
    }
}));

// Mock collection paths
vi.mock('../../services/firebase/collectionPaths', () => ({
    determineChatType: vi.fn(),
    requiresMigration: vi.fn(),
    getDualWritePaths: vi.fn(),
    getChatIdentifier: vi.fn()
}));

import {
    collection,
    doc,
    getDocs,
    query,
    orderBy,
    limit,
    getDoc,
    setDoc
} from 'firebase/firestore';
import { migrateMessagesForChat, verifyMigrationIntegrity, getMigrationStatus } from './dataMigration';
import { logMigrationOperation, getMigrationStatistics } from './migrationMonitoring';
import { determineChatType, requiresMigration, getChatIdentifier } from '../../services/firebase/collectionPaths';

describe('Production Migration Script', () => {
    beforeEach(() => {
        vi.clearAllMocks();

        // Reset global state
        vi.resetModules();

        // Mock console methods to reduce test noise
        vi.spyOn(console, 'log').mockImplementation(() => { });
        vi.spyOn(console, 'error').mockImplementation(() => { });
        vi.spyOn(console, 'warn').mockImplementation(() => { });
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('runProductionMigration', () => {
        it('should validate migration options', async () => {
            const invalidOptions = {
                chatTypes: 'not-an-array',
                batchSize: -1,
                onProgress: 'not-a-function'
            };

            await expect(runProductionMigration(invalidOptions)).rejects.toThrow('Invalid migration options');
        });

        it('should handle empty migration scenario', async () => {
            // Mock no chats requiring migration
            getDocs.mockResolvedValue({ empty: true, docs: [] });
            requiresMigration.mockReturnValue(false);

            const result = await runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: true
            });

            expect(result.success).toBe(true);
            expect(result.totalChats).toBe(0);
            expect(result.message).toBe('No chats require migration');
        });

        it('should process chats in batches', async () => {
            // Mock chat discovery
            const mockChats = [
                { chatId: 'chat1', selectedChat: { id: 'chat1' }, chatType: 'messenger' },
                { chatId: 'chat2', selectedChat: { id: 'chat2' }, chatType: 'messenger' },
                { chatId: 'chat3', selectedChat: { id: 'chat3' }, chatType: 'messenger' }
            ];

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: mockChats.map(chat => ({
                    id: chat.chatId,
                    data: () => chat.selectedChat
                }))
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            migrateMessagesForChat.mockResolvedValue({
                success: true,
                migratedCount: 10,
                skippedCount: 0,
                errorCount: 0,
                errors: []
            });

            verifyMigrationIntegrity.mockResolvedValue({ success: true });

            const result = await runProductionMigration({
                chatTypes: ['messenger'],
                batchSize: 2,
                dryRun: false
            });

            expect(result.success).toBe(true);
            expect(result.totalChats).toBe(3);
            expect(result.successfulChats).toBe(3);
            expect(migrateMessagesForChat).toHaveBeenCalledTimes(3);
        });

        it('should handle migration failures with retries', async () => {
            const mockChat = { chatId: 'chat1', selectedChat: { id: 'chat1' }, chatType: 'messenger' };

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    id: mockChat.chatId,
                    data: () => mockChat.selectedChat
                }]
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            // Mock failure on first two attempts, success on third
            migrateMessagesForChat
                .mockRejectedValueOnce(new Error('Network error'))
                .mockRejectedValueOnce(new Error('Timeout error'))
                .mockResolvedValueOnce({
                    success: true,
                    migratedCount: 5,
                    skippedCount: 0,
                    errorCount: 0,
                    errors: []
                });

            verifyMigrationIntegrity.mockResolvedValue({ success: true });

            const result = await runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: false
            });

            expect(result.success).toBe(true);
            expect(result.successfulChats).toBe(1);
            expect(migrateMessagesForChat).toHaveBeenCalledTimes(3);
        });

        it('should handle maximum retry failures', async () => {
            const mockChat = { chatId: 'chat1', selectedChat: { id: 'chat1' }, chatType: 'messenger' };

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    id: mockChat.chatId,
                    data: () => mockChat.selectedChat
                }]
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            // Mock consistent failures
            migrateMessagesForChat.mockRejectedValue(new Error('Persistent error'));

            const result = await runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: false
            });

            expect(result.success).toBe(false);
            expect(result.failedChats).toBe(1);
            expect(result.errors).toHaveLength(1);
            expect(migrateMessagesForChat).toHaveBeenCalledTimes(MIGRATION_CONFIG.MAX_RETRIES + 1);
        });

        it('should call progress callback during migration', async () => {
            const mockChat = { chatId: 'chat1', selectedChat: { id: 'chat1' }, chatType: 'messenger' };
            const onProgress = vi.fn();

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    id: mockChat.chatId,
                    data: () => mockChat.selectedChat
                }]
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            migrateMessagesForChat.mockResolvedValue({
                success: true,
                migratedCount: 10,
                skippedCount: 0,
                errorCount: 0,
                errors: []
            });

            await runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: true,
                onProgress
            });

            expect(onProgress).toHaveBeenCalled();
            const progressCall = onProgress.mock.calls[0][0];
            expect(progressCall).toHaveProperty('progressPercentage');
            expect(progressCall).toHaveProperty('processedChats');
            expect(progressCall).toHaveProperty('totalChats');
        });

        it('should handle dry run mode correctly', async () => {
            const mockChat = { chatId: 'chat1', selectedChat: { id: 'chat1' }, chatType: 'messenger' };

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    id: mockChat.chatId,
                    data: () => mockChat.selectedChat
                }]
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            migrateMessagesForChat.mockResolvedValue({
                success: true,
                migratedCount: 10,
                skippedCount: 0,
                errorCount: 0,
                errors: []
            });

            const result = await runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: true
            });

            expect(result.success).toBe(true);
            expect(migrateMessagesForChat).toHaveBeenCalledWith(
                expect.any(Object),
                expect.objectContaining({ dryRun: true })
            );

            // Verify integrity check is not called in dry run
            expect(verifyMigrationIntegrity).not.toHaveBeenCalled();
        });

        it('should verify chat ID mapping', async () => {
            const mockChat = { chatId: 'chat1', selectedChat: { id: 'chat1' }, chatType: 'messenger' };

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    id: mockChat.chatId,
                    data: () => mockChat.selectedChat
                }]
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue('different-id'); // Mismatch
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            const result = await runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: false
            });

            expect(result.success).toBe(false);
            expect(result.failedChats).toBe(1);
            expect(result.errors[0].error).toContain('Chat ID mapping verification failed');
        });

        it('should save and resume from progress', async () => {
            // Mock saved progress
            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    data: () => ({
                        sessionId: 'test-session',
                        processedChats: 5,
                        successfulChats: 4,
                        failedChats: 1,
                        lastProcessedChatId: 'chat5'
                    })
                }]
            });

            // Mock current chats
            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [
                    { id: 'chat6', data: () => ({ id: 'chat6' }) },
                    { id: 'chat7', data: () => ({ id: 'chat7' }) }
                ]
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            migrateMessagesForChat.mockResolvedValue({
                success: true,
                migratedCount: 10,
                skippedCount: 0,
                errorCount: 0,
                errors: []
            });

            const result = await runProductionMigration({
                chatTypes: ['messenger'],
                resumeFromLast: true,
                dryRun: false
            });

            expect(result.success).toBe(true);
            // Should process only chats after the last processed one
            expect(result.totalChats).toBe(2);
        });
    });

    describe('Safety Checks', () => {
        it('should run Firebase connection check', async () => {
            getDoc.mockResolvedValue({ exists: () => true });

            const result = await runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: true
            });

            expect(getDoc).toHaveBeenCalled();
        });

        it('should fail on Firebase connection error', async () => {
            getDoc.mockRejectedValue(new Error('Connection failed'));

            await expect(runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: true
            })).rejects.toThrow('Safety checks failed');
        });

        it('should check storage availability in browser environment', async () => {
            // Mock browser environment
            global.navigator = {
                storage: {
                    estimate: vi.fn().mockResolvedValue({
                        quota: 1000 * 1024 * 1024, // 1GB
                        usage: 900 * 1024 * 1024   // 900MB (high usage)
                    })
                }
            };

            await expect(runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: true
            })).rejects.toThrow('Insufficient storage space');

            delete global.navigator;
        });

        it('should check memory usage if available', async () => {
            // Mock performance.memory
            global.performance = {
                memory: {
                    usedJSHeapSize: 80 * 1024 * 1024,
                    jsHeapSizeLimit: 100 * 1024 * 1024
                }
            };

            await expect(runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: true
            })).rejects.toThrow('High memory usage detected');

            delete global.performance;
        });
    });

    describe('Migration Controls', () => {
        it('should pause migration', async () => {
            await pauseMigration();

            const progress = getMigrationProgress();
            expect(progress.isPaused).toBe(true);
            expect(logMigrationOperation).toHaveBeenCalledWith(
                expect.any(String),
                expect.any(String),
                'Migration paused by user',
                expect.any(Object)
            );
        });

        it('should resume migration', async () => {
            await resumeMigration();

            const progress = getMigrationProgress();
            expect(progress.isPaused).toBe(false);
            expect(logMigrationOperation).toHaveBeenCalledWith(
                expect.any(String),
                expect.any(String),
                'Migration resumed by user',
                expect.any(Object)
            );
        });

        it('should handle emergency stop', async () => {
            await emergencyStop();

            const progress = getMigrationProgress();
            expect(progress.isRunning).toBe(false);
            expect(progress.isPaused).toBe(true);
            expect(logMigrationOperation).toHaveBeenCalledWith(
                expect.any(String),
                expect.any(String),
                'Migration emergency stop activated',
                expect.any(Object)
            );
        });
    });

    describe('Progress Tracking', () => {
        it('should track migration progress correctly', () => {
            const progress = getMigrationProgress();

            expect(progress).toHaveProperty('isRunning');
            expect(progress).toHaveProperty('isPaused');
            expect(progress).toHaveProperty('totalChats');
            expect(progress).toHaveProperty('processedChats');
            expect(progress).toHaveProperty('successfulChats');
            expect(progress).toHaveProperty('failedChats');
            expect(progress).toHaveProperty('progressPercentage');
        });

        it('should calculate progress percentage correctly', () => {
            // This would require accessing internal state, which is not directly testable
            // In a real implementation, you might want to expose more of the state for testing
            const progress = getMigrationProgress();
            expect(typeof progress.progressPercentage).toBe('number');
            expect(progress.progressPercentage).toBeGreaterThanOrEqual(0);
            expect(progress.progressPercentage).toBeLessThanOrEqual(100);
        });
    });

    describe('Error Handling', () => {
        it('should handle validation errors gracefully', async () => {
            await expect(runProductionMigration({
                chatTypes: 'invalid',
                batchSize: 'invalid'
            })).rejects.toThrow('Invalid migration options');
        });

        it('should handle Firebase errors gracefully', async () => {
            getDocs.mockRejectedValue(new Error('Firebase error'));

            await expect(runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: true
            })).rejects.toThrow();
        });

        it('should call error callback on failures', async () => {
            const onError = vi.fn();
            const mockChat = { chatId: 'chat1', selectedChat: { id: 'chat1' }, chatType: 'messenger' };

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: [{
                    id: mockChat.chatId,
                    data: () => mockChat.selectedChat
                }]
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });
            migrateMessagesForChat.mockRejectedValue(new Error('Migration failed'));

            await runProductionMigration({
                chatTypes: ['messenger'],
                dryRun: false,
                onError
            });

            expect(onError).toHaveBeenCalled();
            const errorCall = onError.mock.calls[0][0];
            expect(errorCall).toHaveProperty('chatId');
            expect(errorCall).toHaveProperty('error');
        });
    });

    describe('Batch Processing', () => {
        it('should respect batch size configuration', async () => {
            const mockChats = Array.from({ length: 10 }, (_, i) => ({
                chatId: `chat${i}`,
                selectedChat: { id: `chat${i}` },
                chatType: 'messenger'
            }));

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: mockChats.map(chat => ({
                    id: chat.chatId,
                    data: () => chat.selectedChat
                }))
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            migrateMessagesForChat.mockResolvedValue({
                success: true,
                migratedCount: 5,
                skippedCount: 0,
                errorCount: 0,
                errors: []
            });

            const batchSize = 3;
            await runProductionMigration({
                chatTypes: ['messenger'],
                batchSize,
                dryRun: true
            });

            // Should process all chats despite batch size
            expect(migrateMessagesForChat).toHaveBeenCalledTimes(10);
        });

        it('should handle concurrent operations with semaphore', async () => {
            const mockChats = Array.from({ length: 20 }, (_, i) => ({
                chatId: `chat${i}`,
                selectedChat: { id: `chat${i}` },
                chatType: 'messenger'
            }));

            getDocs.mockResolvedValueOnce({
                empty: false,
                docs: mockChats.map(chat => ({
                    id: chat.chatId,
                    data: () => chat.selectedChat
                }))
            });

            requiresMigration.mockReturnValue(true);
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockImplementation((chat) => chat.id);
            getMigrationStatus.mockResolvedValue({ status: 'not_migrated' });

            // Add delay to simulate async operations
            migrateMessagesForChat.mockImplementation(() =>
                new Promise(resolve => setTimeout(() => resolve({
                    success: true,
                    migratedCount: 5,
                    skippedCount: 0,
                    errorCount: 0,
                    errors: []
                }), 10))
            );

            const startTime = Date.now();
            await runProductionMigration({
                chatTypes: ['messenger'],
                batchSize: 5,
                dryRun: true
            });
            const endTime = Date.now();

            // Should complete in reasonable time due to concurrency
            expect(endTime - startTime).toBeLessThan(1000); // Less than 1 second
            expect(migrateMessagesForChat).toHaveBeenCalledTimes(20);
        });
    });
});
