/**
 * Unit tests for Firebase Data Migration Utilities
 */

import {
    migrateMessagesForChat,
    verifyMigrationIntegrity,
    rollbackMigration,
    getMigrationStatus
} from './dataMigration';

// Mock Firebase Firestore
jest.mock('firebase/firestore', () => ({
    collection: jest.fn(),
    doc: jest.fn(),
    getDocs: jest.fn(),
    setDoc: jest.fn(),
    deleteDoc: jest.fn(),
    query: jest.fn(),
    orderBy: jest.fn(),
    writeBatch: jest.fn(),
    getDoc: jest.fn()
}));

// Mock Firebase config
jest.mock('../firebase.config', () => ({
    db: {}
}));

// Mock collection paths service
jest.mock('../../services/firebase/collectionPaths', () => ({
    getDualWritePaths: jest.fn(),
    determineChatType: jest.fn(),
    getChatIdentifier: jest.fn(),
    requiresMigration: jest.fn()
}));

// Mock migration monitoring
jest.mock('./migrationMonitoring', () => ({
    logMigrationOperation: jest.fn(),
    withPerformanceMonitoring: jest.fn((name, fn) => fn),
    LOG_LEVELS: {
        INFO: 'info',
        SUCCESS: 'success',
        ERROR: 'error'
    },
    OPERATION_TYPES: {
        MIGRATION: 'migration'
    }
}));

import {
    collection,
    doc,
    getDocs,
    setDoc,
    deleteDoc,
    query,
    orderBy,
    writeBatch,
    getDoc
} from 'firebase/firestore';

import {
    getDualWritePaths,
    determineChatType,
    getChatIdentifier,
    requiresMigration
} from '../../services/firebase/collectionPaths';

describe('Firebase Data Migration Utilities', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('migrateMessagesForChat', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockDualWritePaths = {
            current: {
                messages: 'chats/backend_chat_123/messages',
                comments: 'chats/backend_chat_123/comments'
            },
            legacy: {
                messages: 'chats/participant_456/messages',
                comments: 'chats/backend_chat_123/comments'
            },
            chatId: 'backend_chat_123',
            requiresMigration: true
        };

        const mockSourceMessages = [
            {
                id: 'msg1',
                data: () => ({
                    message: 'Hello',
                    sender: 'participant_456',
                    recipient: 'backend_chat_123',
                    created_time: '2024-01-01T10:00:00Z',
                    type: 'text'
                })
            },
            {
                id: 'msg2',
                data: () => ({
                    message: 'World',
                    sender: 'participant_456',
                    recipient: 'backend_chat_123',
                    created_time: '2024-01-01T10:01:00Z',
                    type: 'text'
                })
            }
        ];

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            requiresMigration.mockReturnValue(true);
            getDualWritePaths.mockReturnValue(mockDualWritePaths);
        });

        test('successfully migrates messages from legacy to current path', async () => {
            // Mock Firestore operations
            const mockSourceSnapshot = {
                empty: false,
                docs: mockSourceMessages
            };
            const mockDestSnapshot = {
                docs: []
            };
            const mockBatch = {
                set: jest.fn(),
                commit: jest.fn().mockResolvedValue()
            };

            getDocs.mockResolvedValueOnce(mockSourceSnapshot)
                .mockResolvedValueOnce(mockDestSnapshot);
            writeBatch.mockReturnValue(mockBatch);
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const result = await migrateMessagesForChat(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.migratedCount).toBe(2);
            expect(result.skippedCount).toBe(0);
            expect(result.errorCount).toBe(0);
            expect(result.fromPath).toBe('chats/participant_456/messages');
            expect(result.toPath).toBe('chats/backend_chat_123/messages');
        });

        test('skips migration for chat types that do not require it', async () => {
            requiresMigration.mockReturnValue(false);

            const result = await migrateMessagesForChat(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.migratedCount).toBe(0);
            expect(result.skippedCount).toBe(0);
            expect(result.errorCount).toBe(0);
        });

        test('handles dry run mode correctly', async () => {
            const mockSourceSnapshot = {
                empty: false,
                docs: mockSourceMessages
            };
            const mockDestSnapshot = {
                docs: []
            };

            getDocs.mockResolvedValueOnce(mockSourceSnapshot)
                .mockResolvedValueOnce(mockDestSnapshot);
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const result = await migrateMessagesForChat(mockSelectedChat, { dryRun: true });

            expect(result.success).toBe(true);
            expect(result.migratedCount).toBe(2);
            expect(result.skippedCount).toBe(0);
            expect(writeBatch).not.toHaveBeenCalled();
        });

        test('skips existing messages in destination', async () => {
            const mockSourceSnapshot = {
                empty: false,
                docs: mockSourceMessages
            };
            const mockDestSnapshot = {
                docs: [{ id: 'msg1' }] // msg1 already exists
            };
            const mockBatch = {
                set: jest.fn(),
                commit: jest.fn().mockResolvedValue()
            };

            getDocs.mockResolvedValueOnce(mockSourceSnapshot)
                .mockResolvedValueOnce(mockDestSnapshot);
            writeBatch.mockReturnValue(mockBatch);
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const result = await migrateMessagesForChat(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.migratedCount).toBe(1); // Only msg2 migrated
            expect(result.skippedCount).toBe(1); // msg1 skipped
        });

        test('handles empty source collection', async () => {
            const mockSourceSnapshot = {
                empty: true,
                docs: []
            };

            getDocs.mockResolvedValueOnce(mockSourceSnapshot);
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const result = await migrateMessagesForChat(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.migratedCount).toBe(0);
            expect(result.skippedCount).toBe(0);
        });

        test('handles batch write errors', async () => {
            const mockSourceSnapshot = {
                empty: false,
                docs: mockSourceMessages
            };
            const mockDestSnapshot = {
                docs: []
            };
            const mockBatch = {
                set: jest.fn(),
                commit: jest.fn().mockRejectedValue(new Error('Batch write failed'))
            };

            getDocs.mockResolvedValueOnce(mockSourceSnapshot)
                .mockResolvedValueOnce(mockDestSnapshot);
            writeBatch.mockReturnValue(mockBatch);
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const result = await migrateMessagesForChat(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.errorCount).toBe(2); // Both messages failed
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].type).toBe('batch_write_error');
        });

        test('handles invalid selectedChat', async () => {
            const result = await migrateMessagesForChat(null);

            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].message).toBe('selectedChat is required');
        });

        test('handles unable to determine chat type', async () => {
            determineChatType.mockReturnValue(null);

            const result = await migrateMessagesForChat(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].message).toBe('Unable to determine chat type');
        });

        test('handles deleteSource option', async () => {
            const mockSourceSnapshot = {
                empty: false,
                docs: mockSourceMessages
            };
            const mockDestSnapshot = {
                docs: []
            };
            const mockBatch = {
                set: jest.fn(),
                commit: jest.fn().mockResolvedValue()
            };
            const mockDeleteBatch = {
                delete: jest.fn(),
                commit: jest.fn().mockResolvedValue()
            };

            getDocs.mockResolvedValueOnce(mockSourceSnapshot)
                .mockResolvedValueOnce(mockDestSnapshot);
            writeBatch.mockReturnValueOnce(mockBatch)
                .mockReturnValueOnce(mockDeleteBatch);
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const result = await migrateMessagesForChat(mockSelectedChat, { deleteSource: true });

            expect(result.success).toBe(true);
            expect(mockDeleteBatch.delete).toHaveBeenCalledTimes(2);
            expect(mockDeleteBatch.commit).toHaveBeenCalled();
        });
    });

    describe('verifyMigrationIntegrity', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockDualWritePaths = {
            legacy: {
                messages: 'chats/participant_456/messages'
            },
            current: {
                messages: 'chats/backend_chat_123/messages'
            }
        };

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            requiresMigration.mockReturnValue(true);
            getDualWritePaths.mockReturnValue(mockDualWritePaths);
        });

        test('successfully verifies complete migration', async () => {
            const sourceMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello',
                        sender: 'participant_456',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];
            const destMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello',
                        sender: 'participant_456',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            getDocs.mockResolvedValueOnce({ docs: sourceMessages })
                .mockResolvedValueOnce({ docs: destMessages });
            collection.mockReturnValue({});

            const result = await verifyMigrationIntegrity(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.sourceCount).toBe(1);
            expect(result.destinationCount).toBe(1);
            expect(result.missingInDestination).toHaveLength(0);
            expect(result.dataIntegrityIssues).toHaveLength(0);
        });

        test('detects missing messages in destination', async () => {
            const sourceMessages = [
                { id: 'msg1', data: () => ({ message: 'Hello' }) },
                { id: 'msg2', data: () => ({ message: 'World' }) }
            ];
            const destMessages = [
                { id: 'msg1', data: () => ({ message: 'Hello' }) }
            ];

            getDocs.mockResolvedValueOnce({ docs: sourceMessages })
                .mockResolvedValueOnce({ docs: destMessages });
            collection.mockReturnValue({});

            const result = await verifyMigrationIntegrity(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.missingInDestination).toContain('msg2');
        });

        test('detects data integrity issues', async () => {
            const sourceMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello',
                        sender: 'participant_456'
                    })
                }
            ];
            const destMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello Modified',
                        sender: 'participant_456'
                    })
                }
            ];

            getDocs.mockResolvedValueOnce({ docs: sourceMessages })
                .mockResolvedValueOnce({ docs: destMessages });
            collection.mockReturnValue({});

            const result = await verifyMigrationIntegrity(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.dataIntegrityIssues).toHaveLength(1);
            expect(result.dataIntegrityIssues[0].field).toBe('message');
        });

        test('skips verification for chat types that do not require migration', async () => {
            requiresMigration.mockReturnValue(false);

            const result = await verifyMigrationIntegrity(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(getDocs).not.toHaveBeenCalled();
        });
    });

    describe('getMigrationStatus', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockDualWritePaths = {
            legacy: {
                messages: 'chats/participant_456/messages'
            },
            current: {
                messages: 'chats/backend_chat_123/messages'
            }
        };

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            getDualWritePaths.mockReturnValue(mockDualWritePaths);
        });

        test('returns not_required status for WhatsApp', async () => {
            requiresMigration.mockReturnValue(false);

            const result = await getMigrationStatus(mockSelectedChat);

            expect(result.requiresMigration).toBe(false);
            expect(result.status).toBe('not_required');
        });

        test('returns not_migrated status when source has data but destination is empty', async () => {
            requiresMigration.mockReturnValue(true);
            getDocs.mockResolvedValueOnce({ docs: [{ id: 'msg1' }] }) // source
                .mockResolvedValueOnce({ docs: [] }); // destination
            collection.mockReturnValue({});

            const result = await getMigrationStatus(mockSelectedChat);

            expect(result.requiresMigration).toBe(true);
            expect(result.status).toBe('not_migrated');
            expect(result.sourceCount).toBe(1);
            expect(result.destCount).toBe(0);
        });

        test('returns fully_migrated status when destination has data but source is empty', async () => {
            requiresMigration.mockReturnValue(true);
            getDocs.mockResolvedValueOnce({ docs: [] }) // source
                .mockResolvedValueOnce({ docs: [{ id: 'msg1' }] }); // destination
            collection.mockReturnValue({});

            const result = await getMigrationStatus(mockSelectedChat);

            expect(result.status).toBe('fully_migrated');
            expect(result.sourceCount).toBe(0);
            expect(result.destCount).toBe(1);
        });

        test('returns partially_migrated status when both have data', async () => {
            requiresMigration.mockReturnValue(true);
            getDocs.mockResolvedValueOnce({ docs: [{ id: 'msg1' }] }) // source
                .mockResolvedValueOnce({ docs: [{ id: 'msg2' }] }); // destination
            collection.mockReturnValue({});

            const result = await getMigrationStatus(mockSelectedChat);

            expect(result.status).toBe('partially_migrated');
            expect(result.sourceCount).toBe(1);
            expect(result.destCount).toBe(1);
        });

        test('returns no_data status when both collections are empty', async () => {
            requiresMigration.mockReturnValue(true);
            getDocs.mockResolvedValueOnce({ docs: [] }) // source
                .mockResolvedValueOnce({ docs: [] }); // destination
            collection.mockReturnValue({});

            const result = await getMigrationStatus(mockSelectedChat);

            expect(result.status).toBe('no_data');
            expect(result.sourceCount).toBe(0);
            expect(result.destCount).toBe(0);
        });

        test('handles errors gracefully', async () => {
            requiresMigration.mockReturnValue(true);
            getDocs.mockRejectedValue(new Error('Firestore error'));
            collection.mockReturnValue({});

            const result = await getMigrationStatus(mockSelectedChat);

            expect(result.requiresMigration).toBe(false);
            expect(result.error).toBe('Firestore error');
        });
    });

    describe('rollbackMigration', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            requiresMigration.mockReturnValue(true);
        });

        test('successfully performs rollback', async () => {
            // Mock the migrateMessagesForChat function to simulate successful rollback
            const mockMigrationResult = {
                success: true,
                migratedCount: 2,
                skippedCount: 0,
                errorCount: 0,
                errors: [],
                fromPath: 'chats/participant_456/messages',
                toPath: 'chats/backend_chat_123/messages'
            };

            // We need to mock the actual migration function since rollback calls it
            jest.doMock('./dataMigration', () => ({
                ...jest.requireActual('./dataMigration'),
                migrateMessagesForChat: jest.fn().mockResolvedValue(mockMigrationResult)
            }));

            const result = await rollbackMigration(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.migratedCount).toBe(2);
        });

        test('skips rollback for chat types that do not require migration', async () => {
            requiresMigration.mockReturnValue(false);

            const result = await rollbackMigration(mockSelectedChat);

            expect(result.success).toBe(true);
            expect(result.migratedCount).toBe(0);
        });

        test('handles rollback errors', async () => {
            // Mock migration to throw error
            jest.doMock('./dataMigration', () => ({
                ...jest.requireActual('./dataMigration'),
                migrateMessagesForChat: jest.fn().mockRejectedValue(new Error('Rollback failed'))
            }));

            const result = await rollbackMigration(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.errorCount).toBe(1);
            expect(result.errors[0].type).toBe('rollback_error');
        });
    });

    describe('Error handling and edge cases', () => {
        test('handles Firestore connection errors', async () => {
            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            determineChatType.mockReturnValue('messenger');
            requiresMigration.mockReturnValue(true);
            getDualWritePaths.mockReturnValue({
                current: { messages: 'chats/backend_chat_123/messages' },
                legacy: { messages: 'chats/participant_456/messages' },
                chatId: 'backend_chat_123'
            });

            getDocs.mockRejectedValue(new Error('Firestore connection failed'));
            collection.mockReturnValue({});

            const result = await migrateMessagesForChat(mockSelectedChat);

            expect(result.success).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0].message).toBe('Firestore connection failed');
        });

        test('handles malformed message data', async () => {
            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const malformedMessages = [
                {
                    id: 'msg1',
                    data: () => null // Malformed data
                }
            ];

            determineChatType.mockReturnValue('messenger');
            requiresMigration.mockReturnValue(true);
            getDualWritePaths.mockReturnValue({
                current: { messages: 'chats/backend_chat_123/messages' },
                legacy: { messages: 'chats/participant_456/messages' },
                chatId: 'backend_chat_123'
            });

            getDocs.mockResolvedValueOnce({ empty: false, docs: malformedMessages })
                .mockResolvedValueOnce({ docs: [] });
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const mockBatch = {
                set: jest.fn().mockImplementation(() => {
                    throw new Error('Invalid message data');
                }),
                commit: jest.fn()
            };
            writeBatch.mockReturnValue(mockBatch);

            const result = await migrateMessagesForChat(mockSelectedChat);

            expect(result.errorCount).toBeGreaterThan(0);
        });
    });
});
