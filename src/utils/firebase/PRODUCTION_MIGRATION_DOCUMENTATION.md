# Production Firebase Collection Migration Documentation

## Overview

This documentation covers the production-ready migration system for standardizing Firebase collection paths between messages and comments. The system migrates message collections from sender ID-based paths to chat ID-based paths for consistency.

## Architecture

### Core Components

1. **Production Migration Script** (`productionMigrationScript.js`)
   - Main migration orchestrator
   - Batch processing and concurrency control
   - Progress tracking and resumable operations
   - Safety checks and validation

2. **CLI Interface** (`migrationCLI.js`)
   - Interactive command-line interface
   - Progress monitoring and controls
   - Emergency stop capabilities
   - Report generation

3. **Data Migration Utilities** (`dataMigration.js`)
   - Individual chat migration logic
   - Data integrity verification
   - Rollback capabilities

4. **Migration Monitoring** (`migrationMonitoring.js`)
   - Comprehensive logging system
   - Progress tracking and statistics
   - Performance monitoring

## Migration Process

### Phase 1: Pre-Migration Safety Checks

The system performs several safety checks before starting migration:

1. **Firebase Connection Check**
   - Verifies database connectivity
   - Tests read/write permissions

2. **Storage Availability Check**
   - Ensures sufficient local storage (browser environments)
   - Requires minimum 100MB available space

3. **System Resources Check**
   - Monitors memory usage
   - Fails if memory usage > 80%

4. **Data Validation**
   - Verifies chat ID mapping correctness
   - Validates collection path resolution

### Phase 2: Chat Discovery and Batching

1. **Chat Collection Scanning**
   - Discovers chats requiring migration
   - Filters by chat type (messenger, instagram)
   - Excludes WhatsApp (already consistent)

2. **Migration Status Assessment**
   - Checks individual chat migration status
   - Identifies not_migrated and partially_migrated chats
   - Supports resumable operations

3. **Batch Creation**
   - Groups chats into processing batches
   - Configurable batch size (default: 10)
   - Optimizes for performance and memory usage

### Phase 3: Migration Execution

1. **Concurrent Processing**
   - Processes multiple chats simultaneously
   - Semaphore-based concurrency control
   - Maximum 5 concurrent operations

2. **Individual Chat Migration**
   - Migrates messages from sender ID to chat ID collections
   - Preserves all message data and metadata
   - Implements retry logic (max 3 attempts)

3. **Data Integrity Verification**
   - Compares source and destination data
   - Validates message count and content
   - Reports any discrepancies

### Phase 4: Progress Tracking and Monitoring

1. **Real-time Progress Updates**
   - Tracks processed/successful/failed chat counts
   - Calculates completion percentage
   - Provides ETA estimates

2. **Persistent Progress Storage**
   - Saves progress to Firebase every 5 chats
   - Enables resumable migrations
   - Maintains session history

3. **Comprehensive Logging**
   - Logs all operations with timestamps
   - Categorizes by log level (info, warn, error, success)
   - Stores in Firebase for analysis

## Configuration

### Migration Configuration (`MIGRATION_CONFIG`)

```javascript
{
    BATCH_SIZE: 10,                    // Chats processed in parallel
    MESSAGE_BATCH_SIZE: 100,           // Messages per batch within each chat
    MAX_RETRIES: 3,                    // Retry attempts for failed operations
    RETRY_DELAY: 2000,                 // Delay between retries (ms)
    PROGRESS_SAVE_INTERVAL: 5,         // Save progress every N chats
    SAFETY_CHECK_TIMEOUT: 30000,       // Safety check timeout (ms)
    MAX_CONCURRENT_OPERATIONS: 5       // Maximum concurrent chat migrations
}
```

### Migration Options

```javascript
{
    dryRun: false,                     // Simulate migration without changes
    resumeFromLast: true,              // Resume from last checkpoint
    chatTypes: ['messenger', 'instagram'], // Chat types to migrate
    batchSize: 10,                     // Override default batch size
    onProgress: (progress) => {},      // Progress callback function
    onError: (error) => {}             // Error callback function
}
```

## Usage

### Command Line Interface

```bash
# Start the CLI
node src/utils/firebase/migrationCLI.js

# Follow interactive prompts:
# 1. Select migration type (dry-run recommended first)
# 2. Choose chat types to migrate
# 3. Configure batch size
# 4. Confirm and start migration
```

### Programmatic Usage

```javascript
import { runProductionMigration } from './productionMigrationScript';

const result = await runProductionMigration({
    dryRun: true,
    chatTypes: ['messenger', 'instagram'],
    batchSize: 10,
    onProgress: (progress) => {
        console.log(`Progress: ${progress.progressPercentage}%`);
    },
    onError: (error) => {
        console.error(`Error in chat ${error.chatId}:`, error.error);
    }
});

console.log('Migration result:', result);
```

## Safety Features

### 1. Dry Run Mode

- Simulates migration without making changes
- Validates chat discovery and mapping
- Estimates migration scope and duration
- **Always run dry-run first in production**

### 2. Resumable Operations

- Saves progress every 5 processed chats
- Automatically resumes from last checkpoint
- Handles interruptions gracefully
- Maintains session continuity

### 3. Retry Logic

- Automatic retry for transient failures
- Exponential backoff delay
- Maximum 3 retry attempts per chat
- Detailed error logging

### 4. Emergency Controls

- **Pause**: Temporarily halt migration
- **Resume**: Continue paused migration
- **Emergency Stop**: Immediate termination with progress save

### 5. Data Integrity Verification

- Compares source and destination message counts
- Validates critical message fields
- Reports data discrepancies
- Ensures no data loss during migration

## Monitoring and Reporting

### Real-time Monitoring

```javascript
import { getMigrationProgress } from './productionMigrationScript';

const progress = getMigrationProgress();
console.log({
    isRunning: progress.isRunning,
    progressPercentage: progress.progressPercentage,
    processedChats: progress.processedChats,
    totalChats: progress.totalChats,
    successfulChats: progress.successfulChats,
    failedChats: progress.failedChats
});
```

### Migration Statistics

```javascript
import { getMigrationStatistics } from './migrationMonitoring';

const stats = await getMigrationStatistics();
console.log({
    total: stats.total,
    completed: stats.completed,
    failed: stats.failed,
    byType: stats.byType
});
```

### Log Analysis

```javascript
import { getRecentMigrationLogs } from './migrationMonitoring';

const logs = await getRecentMigrationLogs(100, 'error');
logs.forEach(log => {
    console.log(`[${log.timestamp}] ${log.operation}: ${log.message}`);
});
```

### Report Generation

```javascript
import { generateMigrationReport } from './migrationMonitoring';

const startDate = new Date('2024-01-01');
const endDate = new Date();
const report = await generateMigrationReport(startDate, endDate);

console.log({
    totalOperations: report.summary.totalOperations,
    successRate: report.summary.successfulOperations / report.summary.totalOperations,
    averageDuration: report.summary.averageDuration,
    errors: report.errors.length
});
```

## Error Handling

### Common Error Scenarios

1. **Network Connectivity Issues**
   - Automatic retry with exponential backoff
   - Graceful degradation for temporary failures
   - Detailed logging for troubleshooting

2. **Firebase Permission Errors**
   - Clear error messages with resolution steps
   - Validation of required permissions
   - Fallback to read-only operations when possible

3. **Data Validation Failures**
   - Chat ID mapping verification
   - Collection path validation
   - Data integrity checks

4. **Resource Constraints**
   - Memory usage monitoring
   - Storage availability checks
   - Concurrency limits to prevent overload

### Error Recovery

1. **Automatic Recovery**
   - Retry logic for transient failures
   - Resume from last checkpoint
   - Skip problematic chats and continue

2. **Manual Recovery**
   - Emergency stop and restart
   - Individual chat re-migration
   - Rollback capabilities

## Performance Optimization

### Batch Processing

- Processes chats in configurable batches
- Balances throughput with resource usage
- Prevents memory exhaustion

### Concurrency Control

- Semaphore-based operation limiting
- Prevents Firebase rate limiting
- Optimizes for available system resources

### Progress Persistence

- Minimal overhead progress tracking
- Efficient Firebase writes
- Resumable operations without data loss

## Testing

### Unit Tests

```bash
# Run all migration tests
npm test src/utils/firebase/productionMigrationScript.test.js

# Run with coverage
npm test -- --coverage src/utils/firebase/
```

### Integration Tests

```bash
# Test with staging Firebase instance
npm run test:integration -- --env=staging

# Test specific chat types
npm run test:integration -- --chatTypes=messenger
```

### Load Testing

```bash
# Test with large dataset
npm run test:load -- --chatCount=1000

# Test concurrency limits
npm run test:load -- --concurrency=10
```

## Deployment Checklist

### Pre-Deployment

- [ ] Run comprehensive tests in staging environment
- [ ] Verify Firebase permissions and quotas
- [ ] Create database backups
- [ ] Notify stakeholders of maintenance window
- [ ] Prepare rollback procedures

### During Deployment

- [ ] Start with dry-run to validate scope
- [ ] Begin with small batch size (5-10 chats)
- [ ] Monitor system resources and performance
- [ ] Watch for error patterns or failures
- [ ] Be prepared to pause/stop if issues arise

### Post-Deployment

- [ ] Verify migration statistics and success rates
- [ ] Run data integrity verification
- [ ] Monitor application functionality
- [ ] Clean up old collection paths (separate process)
- [ ] Update documentation and runbooks

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   ```
   Error: High memory usage detected: 85%
   Solution: Reduce batch size or restart with fresh session
   ```

2. **Firebase Rate Limiting**
   ```
   Error: Quota exceeded
   Solution: Reduce concurrency or implement longer delays
   ```

3. **Chat ID Mapping Failures**
   ```
   Error: Chat ID mapping verification failed
   Solution: Check chat data structure and identifier logic
   ```

4. **Network Timeouts**
   ```
   Error: Request timeout
   Solution: Check network connectivity and Firebase status
   ```

### Debug Mode

Enable detailed logging:

```javascript
const result = await runProductionMigration({
    // ... other options
    debug: true,
    logLevel: 'verbose'
});
```

### Emergency Procedures

1. **Immediate Stop**
   ```javascript
   import { emergencyStop } from './productionMigrationScript';
   await emergencyStop();
   ```

2. **Rollback Migration**
   ```javascript
   import { rollbackMigration } from './dataMigration';
   await rollbackMigration(selectedChat, { dryRun: false });
   ```

3. **Data Recovery**
   - Restore from database backups
   - Use rollback utilities for individual chats
   - Verify data integrity after recovery

## Support and Maintenance

### Monitoring Dashboards

- Migration progress and statistics
- Error rates and patterns
- System resource usage
- Firebase quota consumption

### Alerting

- High error rates (>5%)
- Memory usage warnings (>80%)
- Long-running migrations (>2 hours)
- Firebase quota warnings (>80%)

### Regular Maintenance

- Clean up old migration logs (monthly)
- Archive completed migration records
- Update configuration based on performance data
- Review and optimize batch sizes

## Conclusion

The production migration system provides a robust, safe, and monitored approach to migrating Firebase collection structures. Its comprehensive safety features, detailed monitoring, and resumable operations make it suitable for production environments with large datasets.

Always start with dry-run mode, monitor progress closely, and be prepared to pause or stop the migration if issues arise. The system is designed to handle failures gracefully and provide detailed information for troubleshooting and recovery.
