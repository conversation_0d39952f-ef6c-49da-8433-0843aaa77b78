/**
 * Production Migration CLI Interface
 *
 * Command-line interface for running the production Firebase collection migration.
 * Provides interactive prompts, progress monitoring, and safety controls.
 */

import {
    runProductionMigration,
    pauseMigration,
    resumeMigration,
    getMigrationProgress,
    emergencyStop,
    MIGRATION_CONFIG
} from './productionMigrationScript';
import {
    getMigrationStatistics,
    getRecentMigrationLogs,
    generateMigrationReport
} from './migrationMonitoring';

/**
 * CLI state
 */
let cliState = {
    isRunning: false,
    currentMigration: null,
    progressInterval: null
};

/**
 * Main CLI entry point
 */
export const runMigrationCLI = async () => {
    console.log('🚀 Firebase Collection Migration CLI');
    console.log('=====================================\n');

    try {
        await showMainMenu();
    } catch (error) {
        console.error('❌ CLI Error:', error.message);
        process.exit(1);
    }
};

/**
 * Show main menu
 * @private
 */
const showMainMenu = async () => {
    while (true) {
        console.log('\n📋 Main Menu:');
        console.log('1. Run Production Migration');
        console.log('2. Check Migration Status');
        console.log('3. View Migration Statistics');
        console.log('4. View Recent Logs');
        console.log('5. Generate Migration Report');
        console.log('6. Emergency Controls');
        console.log('7. Exit');

        const choice = await promptUser('\nSelect an option (1-7): ');

        switch (choice.trim()) {
            case '1':
                await runMigrationMenu();
                break;
            case '2':
                await checkMigrationStatus();
                break;
            case '3':
                await viewMigrationStatistics();
                break;
            case '4':
                await viewRecentLogs();
                break;
            case '5':
                await generateReport();
                break;
            case '6':
                await emergencyControlsMenu();
                break;
            case '7':
                console.log('👋 Goodbye!');
                process.exit(0);
                break;
            default:
                console.log('❌ Invalid option. Please try again.');
        }
    }
};

/**
 * Run migration menu
 * @private
 */
const runMigrationMenu = async () => {
    console.log('\n🔧 Migration Configuration:');

    // Get configuration from user
    const dryRun = await promptYesNo('Run in dry-run mode (recommended for first run)? (y/n): ');
    const resumeFromLast = await promptYesNo('Resume from last checkpoint? (y/n): ');

    console.log('\nSelect chat types to migrate:');
    console.log('1. Messenger only');
    console.log('2. Instagram only');
    console.log('3. Both Messenger and Instagram');

    const chatTypeChoice = await promptUser('Select option (1-3): ');
    let chatTypes = [];

    switch (chatTypeChoice.trim()) {
        case '1':
            chatTypes = ['messenger'];
            break;
        case '2':
            chatTypes = ['instagram'];
            break;
        case '3':
            chatTypes = ['messenger', 'instagram'];
            break;
        default:
            console.log('❌ Invalid option. Using default (both).');
            chatTypes = ['messenger', 'instagram'];
    }

    const batchSizeInput = await promptUser(`Batch size (default ${MIGRATION_CONFIG.BATCH_SIZE}): `);
    const batchSize = parseInt(batchSizeInput.trim()) || MIGRATION_CONFIG.BATCH_SIZE;

    // Confirm configuration
    console.log('\n📋 Migration Configuration:');
    console.log(`   Dry Run: ${dryRun ? 'Yes' : 'No'}`);
    console.log(`   Resume from Last: ${resumeFromLast ? 'Yes' : 'No'}`);
    console.log(`   Chat Types: ${chatTypes.join(', ')}`);
    console.log(`   Batch Size: ${batchSize}`);

    const confirm = await promptYesNo('\nProceed with migration? (y/n): ');
    if (!confirm) {
        console.log('❌ Migration cancelled.');
        return;
    }

    // Show safety warning for production runs
    if (!dryRun) {
        console.log('\n⚠️  WARNING: This will modify production data!');
        console.log('   Make sure you have:');
        console.log('   - Recent database backups');
        console.log('   - Tested the migration in a staging environment');
        console.log('   - Notified relevant stakeholders');

        const finalConfirm = await promptYesNo('\nAre you absolutely sure? (y/n): ');
        if (!finalConfirm) {
            console.log('❌ Migration cancelled for safety.');
            return;
        }
    }

    // Start migration
    await startMigration({
        dryRun,
        resumeFromLast,
        chatTypes,
        batchSize
    });
};

/**
 * Start the migration process
 * @private
 */
const startMigration = async (options) => {
    console.log('\n🚀 Starting migration...\n');

    cliState.isRunning = true;

    // Set up progress monitoring
    cliState.progressInterval = setInterval(() => {
        const progress = getMigrationProgress();
        displayProgress(progress);
    }, 5000); // Update every 5 seconds

    try {
        const result = await runProductionMigration({
            ...options,
            onProgress: (progress) => {
                displayProgress(progress);
            },
            onError: (error) => {
                console.log(`❌ Error: ${error.error?.message || 'Unknown error'} (Chat: ${error.chatId})`);
            }
        });

        // Clear progress interval
        if (cliState.progressInterval) {
            clearInterval(cliState.progressInterval);
            cliState.progressInterval = null;
        }

        // Display final results
        console.log('\n🎉 Migration Completed!');
        console.log('========================');
        console.log(`✅ Success: ${result.success}`);
        console.log(`📊 Total Chats: ${result.totalChats}`);
        console.log(`✅ Successful: ${result.successfulChats}`);
        console.log(`❌ Failed: ${result.failedChats}`);
        console.log(`⏭️  Skipped: ${result.skippedChats}`);
        console.log(`⏱️  Duration: ${Math.round(result.duration / 1000)}s`);

        if (result.errors && result.errors.length > 0) {
            console.log('\n❌ Errors encountered:');
            result.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. Chat ${error.chatId}: ${error.error}`);
            });
        }

    } catch (error) {
        console.error('\n💥 Migration failed:', error.message);

        // Clear progress interval
        if (cliState.progressInterval) {
            clearInterval(cliState.progressInterval);
            cliState.progressInterval = null;
        }
    } finally {
        cliState.isRunning = false;
    }
};

/**
 * Display migration progress
 * @private
 */
const displayProgress = (progress) => {
    if (!progress || !cliState.isRunning) return;

    const percentage = Math.round(progress.progressPercentage || 0);
    const progressBar = createProgressBar(percentage);

    // Clear previous line and display progress
    process.stdout.write('\r\x1b[K'); // Clear line
    process.stdout.write(
        `📈 Progress: ${progressBar} ${percentage}% ` +
        `(${progress.processedChats}/${progress.totalChats}) ` +
        `✅${progress.successfulChats} ❌${progress.failedChats} ⏭️${progress.skippedChats}`
    );
};

/**
 * Create a visual progress bar
 * @private
 */
const createProgressBar = (percentage) => {
    const width = 20;
    const filled = Math.round((percentage / 100) * width);
    const empty = width - filled;
    return '█'.repeat(filled) + '░'.repeat(empty);
};

/**
 * Check migration status
 * @private
 */
const checkMigrationStatus = async () => {
    console.log('\n📊 Current Migration Status:');

    try {
        const progress = getMigrationProgress();

        if (progress.isRunning) {
            console.log('🟢 Status: Running');
            console.log(`📈 Progress: ${Math.round(progress.progressPercentage || 0)}%`);
            console.log(`📊 Processed: ${progress.processedChats}/${progress.totalChats}`);
            console.log(`✅ Successful: ${progress.successfulChats}`);
            console.log(`❌ Failed: ${progress.failedChats}`);
            console.log(`⏭️  Skipped: ${progress.skippedChats}`);
            console.log(`🔄 Current Batch: ${progress.currentBatch}`);

            if (progress.isPaused) {
                console.log('⏸️  Status: Paused');
            }
        } else {
            console.log('🔴 Status: Not running');
        }

        if (progress.sessionId) {
            console.log(`🆔 Session ID: ${progress.sessionId}`);
        }

    } catch (error) {
        console.error('❌ Error checking status:', error.message);
    }
};

/**
 * View migration statistics
 * @private
 */
const viewMigrationStatistics = async () => {
    console.log('\n📊 Migration Statistics:');

    try {
        const stats = await getMigrationStatistics();

        console.log(`📈 Total Migrations: ${stats.total}`);
        console.log(`✅ Completed: ${stats.completed}`);
        console.log(`❌ Failed: ${stats.failed}`);
        console.log(`🔄 In Progress: ${stats.inProgress}`);
        console.log(`⏭️  Not Started: ${stats.notStarted}`);
        console.log(`🔄 Rolled Back: ${stats.rolledBack}`);

        console.log('\n📱 By Chat Type:');
        Object.entries(stats.byType).forEach(([type, typeStats]) => {
            if (typeStats.total > 0) {
                console.log(`   ${type}: ${typeStats.completed}/${typeStats.total} completed (${typeStats.failed} failed)`);
            }
        });

    } catch (error) {
        console.error('❌ Error getting statistics:', error.message);
    }
};

/**
 * View recent logs
 * @private
 */
const viewRecentLogs = async () => {
    console.log('\n📋 Recent Migration Logs:');

    const limitInput = await promptUser('Number of logs to show (default 20): ');
    const limit = parseInt(limitInput.trim()) || 20;

    try {
        const logs = await getRecentMigrationLogs(limit);

        if (logs.length === 0) {
            console.log('📭 No logs found.');
            return;
        }

        logs.forEach((log, index) => {
            const timestamp = new Date(log.timestamp).toLocaleString();
            const levelIcon = getLevelIcon(log.level);
            console.log(`${index + 1}. ${levelIcon} [${timestamp}] ${log.operation}: ${log.message}`);

            if (log.chatId) {
                console.log(`   Chat: ${log.chatType}/${log.chatId}`);
            }
        });

    } catch (error) {
        console.error('❌ Error getting logs:', error.message);
    }
};

/**
 * Generate migration report
 * @private
 */
const generateReport = async () => {
    console.log('\n📊 Generate Migration Report:');

    const daysInput = await promptUser('Report period in days (default 7): ');
    const days = parseInt(daysInput.trim()) || 7;

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    try {
        console.log('📈 Generating report...');
        const report = await generateMigrationReport(startDate, endDate);

        if (report.error) {
            console.error('❌ Error generating report:', report.error);
            return;
        }

        console.log('\n📊 Migration Report');
        console.log('==================');
        console.log(`📅 Period: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`);
        console.log(`📈 Total Operations: ${report.summary.totalOperations}`);
        console.log(`✅ Successful: ${report.summary.successfulOperations}`);
        console.log(`❌ Failed: ${report.summary.failedOperations}`);
        console.log(`⏱️  Average Duration: ${Math.round(report.summary.averageDuration)}ms`);

        if (Object.keys(report.operationBreakdown).length > 0) {
            console.log('\n🔧 By Operation Type:');
            Object.entries(report.operationBreakdown).forEach(([operation, stats]) => {
                console.log(`   ${operation}: ${stats.success}/${stats.total} successful`);
            });
        }

        if (Object.keys(report.chatTypeBreakdown).length > 0) {
            console.log('\n📱 By Chat Type:');
            Object.entries(report.chatTypeBreakdown).forEach(([type, stats]) => {
                console.log(`   ${type}: ${stats.success}/${stats.total} successful`);
            });
        }

        if (report.errors.length > 0) {
            console.log(`\n❌ Recent Errors (${report.errors.length}):`);
            report.errors.slice(0, 5).forEach((error, index) => {
                console.log(`   ${index + 1}. ${error.message} (${error.chatType}/${error.chatId})`);
            });

            if (report.errors.length > 5) {
                console.log(`   ... and ${report.errors.length - 5} more`);
            }
        }

    } catch (error) {
        console.error('❌ Error generating report:', error.message);
    }
};

/**
 * Emergency controls menu
 * @private
 */
const emergencyControlsMenu = async () => {
    console.log('\n🚨 Emergency Controls:');
    console.log('1. Pause Migration');
    console.log('2. Resume Migration');
    console.log('3. Emergency Stop');
    console.log('4. Back to Main Menu');

    const choice = await promptUser('Select option (1-4): ');

    switch (choice.trim()) {
        case '1':
            await pauseMigration();
            console.log('⏸️  Migration paused.');
            break;
        case '2':
            await resumeMigration();
            console.log('▶️  Migration resumed.');
            break;
        case '3':
            const confirm = await promptYesNo('⚠️  Are you sure you want to emergency stop? (y/n): ');
            if (confirm) {
                await emergencyStop();
                console.log('🛑 Emergency stop activated.');
            }
            break;
        case '4':
            return;
        default:
            console.log('❌ Invalid option.');
    }
};

/**
 * Get level icon for log display
 * @private
 */
const getLevelIcon = (level) => {
    switch (level) {
        case 'success': return '✅';
        case 'error': return '❌';
        case 'warn': return '⚠️';
        case 'info': return 'ℹ️';
        default: return '📝';
    }
};

/**
 * Prompt user for input
 * @private
 */
const promptUser = (question) => {
    return new Promise((resolve) => {
        process.stdout.write(question);
        process.stdin.once('data', (data) => {
            resolve(data.toString().trim());
        });
    });
};

/**
 * Prompt user for yes/no answer
 * @private
 */
const promptYesNo = async (question) => {
    const answer = await promptUser(question);
    return answer.toLowerCase().startsWith('y');
};

// Handle process termination gracefully
process.on('SIGINT', async () => {
    console.log('\n\n🛑 Received interrupt signal...');

    if (cliState.isRunning) {
        console.log('⏸️  Pausing migration...');
        await pauseMigration();
    }

    if (cliState.progressInterval) {
        clearInterval(cliState.progressInterval);
    }

    console.log('👋 Goodbye!');
    process.exit(0);
});

// Export for use in other modules
export default runMigrationCLI;
