/**
 * Production Firebase Collection Migration Script
 *
 * This script provides a production-ready solution for migrating message collections
 * from sender ID-based paths to chat ID-based paths for consistency with comments.
 *
 * Features:
 * - Batch processing for large datasets
 * - Progress tracking and resumable migration
 * - Safety checks and validation
 * - Comprehensive error handling and logging
 * - Performance monitoring
 * - Rollback capabilities
 */

import {
    collection,
    doc,
    getDocs,
    query,
    orderBy,
    limit,
    startAfter,
    writeBatch,
    getDoc,
    setDoc,
    updateDoc,
    serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase.config';
import {
    migrateMessagesForChat,
    verifyMigrationIntegrity,
    getMigrationStatus as getIndividualMigrationStatus
} from './dataMigration';
import {
    logMigrationOperation,
    updateMigrationStatus,
    getMigrationStatistics,
    withPerformanceMonitoring,
    LOG_LEVELS,
    OPERATION_TYPES,
    MIGRATION_STATUS
} from './migrationMonitoring';
import {
    determineChatType,
    requiresMigration,
    getDualWritePaths,
    getChatIdentifier
} from '../../services/firebase/collectionPaths';

/**
 * Production migration configuration
 */
export const MIGRATION_CONFIG = {
    BATCH_SIZE: 10, // Number of chats to process in parallel
    MESSAGE_BATCH_SIZE: 100, // Number of messages per batch within each chat
    MAX_RETRIES: 3,
    RETRY_DELAY: 2000, // 2 seconds
    PROGRESS_SAVE_INTERVAL: 5, // Save progress every 5 chats
    SAFETY_CHECK_TIMEOUT: 30000, // 30 seconds for safety checks
    MAX_CONCURRENT_OPERATIONS: 5
};

/**
 * Migration progress state
 */
let migrationState = {
    isRunning: false,
    isPaused: false,
    startTime: null,
    totalChats: 0,
    processedChats: 0,
    successfulChats: 0,
    failedChats: 0,
    skippedChats: 0,
    currentBatch: 0,
    lastProcessedChatId: null,
    errors: [],
    sessionId: null
};

/**
 * Main production migration function
 * @param {Object} options - Migration options
 * @param {boolean} options.dryRun - If true, simulate migration without making changes
 * @param {boolean} options.resumeFromLast - If true, resume from last processed chat
 * @param {Array<string>} options.chatTypes - Chat types to migrate ['messenger', 'instagram']
 * @param {number} options.batchSize - Number of chats to process in parallel
 * @param {Function} options.onProgress - Progress callback function
 * @param {Function} options.onError - Error callback function
 * @returns {Promise<Object>} - Migration result
 */
export const runProductionMigration = withPerformanceMonitoring(
    'runProductionMigration',
    async (options = {}) => {
        const {
            dryRun = false,
            resumeFromLast = true,
            chatTypes = ['messenger', 'instagram'],
            batchSize = MIGRATION_CONFIG.BATCH_SIZE,
            onProgress = null,
            onError = null
        } = options;

        // Validate options
        const validation = validateMigrationOptions(options);
        if (!validation.isValid) {
            throw new Error(`Invalid migration options: ${validation.errors.join(', ')}`);
        }

        // Initialize migration state
        await initializeMigrationState(dryRun, resumeFromLast);

        try {
            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.INFO,
                'Starting production migration',
                {
                    dryRun,
                    resumeFromLast,
                    chatTypes,
                    batchSize,
                    sessionId: migrationState.sessionId
                }
            );

            // Run safety checks
            await runSafetyChecks();

            // Get chats that need migration
            const chatsToMigrate = await getChatsForMigration(chatTypes, resumeFromLast);
            migrationState.totalChats = chatsToMigrate.length;

            if (migrationState.totalChats === 0) {
                await logMigrationOperation(
                    OPERATION_TYPES.MIGRATION,
                    LOG_LEVELS.INFO,
                    'No chats require migration',
                    { chatTypes }
                );

                return {
                    success: true,
                    totalChats: 0,
                    processedChats: 0,
                    message: 'No chats require migration'
                };
            }

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.INFO,
                `Found ${migrationState.totalChats} chats requiring migration`,
                { totalChats: migrationState.totalChats, chatTypes }
            );

            // Process chats in batches
            const batches = createChatBatches(chatsToMigrate, batchSize);

            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                if (migrationState.isPaused) {
                    await logMigrationOperation(
                        OPERATION_TYPES.MIGRATION,
                        LOG_LEVELS.INFO,
                        'Migration paused by user',
                        { batchIndex, totalBatches: batches.length }
                    );
                    break;
                }

                migrationState.currentBatch = batchIndex + 1;
                const batch = batches[batchIndex];

                await logMigrationOperation(
                    OPERATION_TYPES.MIGRATION,
                    LOG_LEVELS.INFO,
                    `Processing batch ${migrationState.currentBatch}/${batches.length}`,
                    { batchSize: batch.length }
                );

                // Process batch with concurrency control
                await processChatBatch(batch, dryRun, onProgress, onError);

                // Save progress periodically
                if (batchIndex % MIGRATION_CONFIG.PROGRESS_SAVE_INTERVAL === 0) {
                    await saveMigrationProgress();
                }

                // Report progress
                if (onProgress) {
                    onProgress({
                        ...migrationState,
                        progressPercentage: (migrationState.processedChats / migrationState.totalChats) * 100
                    });
                }
            }

            // Final progress save
            await saveMigrationProgress();

            // Generate final report
            const finalResult = {
                success: migrationState.failedChats === 0,
                totalChats: migrationState.totalChats,
                processedChats: migrationState.processedChats,
                successfulChats: migrationState.successfulChats,
                failedChats: migrationState.failedChats,
                skippedChats: migrationState.skippedChats,
                errors: migrationState.errors,
                duration: Date.now() - migrationState.startTime,
                sessionId: migrationState.sessionId
            };

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                finalResult.success ? LOG_LEVELS.SUCCESS : LOG_LEVELS.ERROR,
                `Production migration completed`,
                finalResult
            );

            return finalResult;

        } catch (error) {
            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.ERROR,
                `Production migration failed: ${error.message}`,
                {
                    error: error.message,
                    stack: error.stack,
                    sessionId: migrationState.sessionId
                }
            );

            if (onError) {
                onError(error);
            }

            throw error;
        } finally {
            migrationState.isRunning = false;
        }
    },
    { operationType: 'production_migration' }
);

/**
 * Initialize migration state
 * @private
 */
const initializeMigrationState = async (dryRun, resumeFromLast) => {
    migrationState.sessionId = `prod_migration_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    migrationState.isRunning = true;
    migrationState.isPaused = false;
    migrationState.startTime = Date.now();
    migrationState.errors = [];

    if (resumeFromLast) {
        const savedProgress = await loadMigrationProgress();
        if (savedProgress) {
            migrationState.processedChats = savedProgress.processedChats || 0;
            migrationState.successfulChats = savedProgress.successfulChats || 0;
            migrationState.failedChats = savedProgress.failedChats || 0;
            migrationState.skippedChats = savedProgress.skippedChats || 0;
            migrationState.lastProcessedChatId = savedProgress.lastProcessedChatId;

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.INFO,
                'Resuming migration from saved progress',
                {
                    processedChats: migrationState.processedChats,
                    lastProcessedChatId: migrationState.lastProcessedChatId
                }
            );
        }
    }
};

/**
 * Run safety checks before starting migration
 * @private
 */
const runSafetyChecks = async () => {
    const checks = [];

    // Check Firebase connection
    checks.push(checkFirebaseConnection());

    // Check available storage
    checks.push(checkStorageAvailability());

    // Check system resources
    checks.push(checkSystemResources());

    // Run all checks with timeout
    const checkResults = await Promise.race([
        Promise.all(checks),
        new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Safety checks timed out')), MIGRATION_CONFIG.SAFETY_CHECK_TIMEOUT)
        )
    ]);

    const failedChecks = checkResults.filter(result => !result.passed);
    if (failedChecks.length > 0) {
        const errorMessage = `Safety checks failed: ${failedChecks.map(c => c.error).join(', ')}`;
        throw new Error(errorMessage);
    }

    await logMigrationOperation(
        OPERATION_TYPES.MIGRATION,
        LOG_LEVELS.SUCCESS,
        'All safety checks passed',
        { checksRun: checkResults.length }
    );
};

/**
 * Check Firebase connection
 * @private
 */
const checkFirebaseConnection = async () => {
    try {
        // Try to read a small document to test connection
        const testRef = doc(db, 'migration_status', 'connection_test');
        await getDoc(testRef);
        return { passed: true, check: 'firebase_connection' };
    } catch (error) {
        return {
            passed: false,
            check: 'firebase_connection',
            error: `Firebase connection failed: ${error.message}`
        };
    }
};

/**
 * Check storage availability
 * @private
 */
const checkStorageAvailability = async () => {
    try {
        if (typeof navigator !== 'undefined' && 'storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            const availableSpace = estimate.quota - estimate.usage;
            const requiredSpace = 100 * 1024 * 1024; // 100MB minimum

            if (availableSpace < requiredSpace) {
                return {
                    passed: false,
                    check: 'storage_availability',
                    error: `Insufficient storage space. Available: ${Math.round(availableSpace / 1024 / 1024)}MB, Required: ${Math.round(requiredSpace / 1024 / 1024)}MB`
                };
            }
        }
        return { passed: true, check: 'storage_availability' };
    } catch (error) {
        // If we can't check storage, assume it's available
        return { passed: true, check: 'storage_availability' };
    }
};

/**
 * Check system resources
 * @private
 */
const checkSystemResources = async () => {
    try {
        // Check memory usage if available
        if (typeof performance !== 'undefined' && 'memory' in performance) {
            const memory = performance.memory;
            const memoryUsageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;

            if (memoryUsageRatio > 0.8) {
                return {
                    passed: false,
                    check: 'system_resources',
                    error: `High memory usage detected: ${Math.round(memoryUsageRatio * 100)}%`
                };
            }
        }
        return { passed: true, check: 'system_resources' };
    } catch (error) {
        // If we can't check resources, assume they're available
        return { passed: true, check: 'system_resources' };
    }
};

/**
 * Get chats that need migration
 * @private
 */
const getChatsForMigration = async (chatTypes, resumeFromLast) => {
    const chatsToMigrate = [];

    for (const chatType of chatTypes) {
        if (!requiresMigration(chatType)) {
            continue;
        }

        // Get migration statistics to understand scope
        const stats = await getMigrationStatistics();

        await logMigrationOperation(
            OPERATION_TYPES.MIGRATION,
            LOG_LEVELS.INFO,
            `Scanning for ${chatType} chats requiring migration`,
            { currentStats: stats }
        );

        // For production, we need to scan actual chat collections
        // This is a simplified approach - in a real production environment,
        // you might want to maintain a registry of chats or use a different discovery method
        const chatCollections = await discoverChatCollections(chatType);

        for (const chatInfo of chatCollections) {
            // Check if this chat needs migration
            const migrationStatus = await getIndividualMigrationStatus(chatInfo.selectedChat);

            if (migrationStatus.status === 'not_migrated' || migrationStatus.status === 'partially_migrated') {
                // Skip if resuming and this chat was already processed
                if (resumeFromLast && migrationState.lastProcessedChatId &&
                    chatInfo.chatId <= migrationState.lastProcessedChatId) {
                    continue;
                }

                chatsToMigrate.push(chatInfo);
            }
        }
    }

    return chatsToMigrate;
};

/**
 * Discover chat collections that need migration
 * @private
 */
const discoverChatCollections = async (chatType) => {
    const chatCollections = [];

    try {
        if (chatType === 'whatsapp') {
            // WhatsApp doesn't need migration
            return [];
        }

        // For messenger/instagram, scan the chats collection
        const chatsCollection = collection(db, 'chats');
        const chatsQuery = query(chatsCollection, limit(1000)); // Process in chunks
        const chatsSnapshot = await getDocs(chatsQuery);

        for (const chatDoc of chatsSnapshot.docs) {
            const chatData = chatDoc.data();
            const chatId = chatDoc.id;

            // Create a mock selectedChat object for migration functions
            const selectedChat = {
                id: chatId,
                ...chatData
            };

            const detectedChatType = determineChatType(selectedChat);

            if (detectedChatType === chatType) {
                chatCollections.push({
                    chatId,
                    selectedChat,
                    chatType: detectedChatType
                });
            }
        }

        await logMigrationOperation(
            OPERATION_TYPES.MIGRATION,
            LOG_LEVELS.INFO,
            `Discovered ${chatCollections.length} ${chatType} chats`,
            { chatType, discoveredCount: chatCollections.length }
        );

    } catch (error) {
        await logMigrationOperation(
            OPERATION_TYPES.MIGRATION,
            LOG_LEVELS.ERROR,
            `Error discovering ${chatType} chats: ${error.message}`,
            { chatType, error: error.message }
        );
    }

    return chatCollections;
};

/**
 * Create batches of chats for processing
 * @private
 */
const createChatBatches = (chats, batchSize) => {
    const batches = [];
    for (let i = 0; i < chats.length; i += batchSize) {
        batches.push(chats.slice(i, i + batchSize));
    }
    return batches;
};

/**
 * Process a batch of chats
 * @private
 */
const processChatBatch = async (chatBatch, dryRun, onProgress, onError) => {
    const semaphore = new Semaphore(MIGRATION_CONFIG.MAX_CONCURRENT_OPERATIONS);

    const batchPromises = chatBatch.map(async (chatInfo) => {
        return semaphore.acquire(async () => {
            return await processSingleChat(chatInfo, dryRun, onProgress, onError);
        });
    });

    await Promise.all(batchPromises);
};

/**
 * Process a single chat migration
 * @private
 */
const processSingleChat = async (chatInfo, dryRun, onProgress, onError) => {
    const { chatId, selectedChat, chatType } = chatInfo;
    let retryCount = 0;

    while (retryCount <= MIGRATION_CONFIG.MAX_RETRIES) {
        try {
            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.INFO,
                `Processing ${chatType} chat ${chatId} (attempt ${retryCount + 1})`,
                { chatId, chatType, attempt: retryCount + 1, dryRun }
            );

            // Verify chat ID mapping is correct
            const verifiedChatId = getChatIdentifier(selectedChat, chatType);
            if (verifiedChatId !== chatId) {
                throw new Error(`Chat ID mapping verification failed. Expected: ${chatId}, Got: ${verifiedChatId}`);
            }

            // Run the migration for this chat
            const migrationResult = await migrateMessagesForChat(selectedChat, {
                dryRun,
                deleteSource: false, // Don't delete source in production migration
                batchSize: MIGRATION_CONFIG.MESSAGE_BATCH_SIZE
            });

            if (migrationResult.success) {
                migrationState.successfulChats++;
                migrationState.lastProcessedChatId = chatId;

                await logMigrationOperation(
                    OPERATION_TYPES.MIGRATION,
                    LOG_LEVELS.SUCCESS,
                    `Successfully migrated ${chatType} chat ${chatId}`,
                    {
                        chatId,
                        chatType,
                        migratedCount: migrationResult.migratedCount,
                        skippedCount: migrationResult.skippedCount
                    }
                );

                // Verify migration integrity if not dry run
                if (!dryRun) {
                    const verificationResult = await verifyMigrationIntegrity(selectedChat);
                    if (!verificationResult.success) {
                        await logMigrationOperation(
                            OPERATION_TYPES.VERIFICATION,
                            LOG_LEVELS.ERROR,
                            `Migration verification failed for ${chatType} chat ${chatId}`,
                            {
                                chatId,
                                chatType,
                                verificationResult
                            }
                        );
                    }
                }
            } else {
                throw new Error(`Migration failed: ${migrationResult.errors.map(e => e.message).join(', ')}`);
            }

            break; // Success, exit retry loop

        } catch (error) {
            retryCount++;

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.ERROR,
                `Error processing ${chatType} chat ${chatId} (attempt ${retryCount}): ${error.message}`,
                {
                    chatId,
                    chatType,
                    attempt: retryCount,
                    error: error.message,
                    willRetry: retryCount <= MIGRATION_CONFIG.MAX_RETRIES
                }
            );

            if (retryCount <= MIGRATION_CONFIG.MAX_RETRIES) {
                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, MIGRATION_CONFIG.RETRY_DELAY * retryCount));
            } else {
                // Max retries exceeded
                migrationState.failedChats++;
                migrationState.errors.push({
                    chatId,
                    chatType,
                    error: error.message,
                    attempts: retryCount
                });

                if (onError) {
                    onError({
                        chatId,
                        chatType,
                        error,
                        attempts: retryCount
                    });
                }
            }
        }
    }

    migrationState.processedChats++;
};

/**
 * Save migration progress to Firebase
 * @private
 */
const saveMigrationProgress = async () => {
    try {
        const progressRef = doc(db, 'migration_progress', migrationState.sessionId);
        await setDoc(progressRef, {
            sessionId: migrationState.sessionId,
            processedChats: migrationState.processedChats,
            successfulChats: migrationState.successfulChats,
            failedChats: migrationState.failedChats,
            skippedChats: migrationState.skippe,
            lastProcessedChatId: migrationState.lastProcessedChatId,
            totalChats: migrationState.totalChats,
            currentBatch: migrationState.currentBatch,
            errors: migrationState.errors,
            lastUpdated: new Date().toISOString(),
            updatedAt: serverTimestamp()
        }, { merge: true });

        await logMigrationOperation(
            OPERATION_TYPES.MIGRATION,
            LOG_LEVELS.INFO,
            'Migration progress saved',
            {
                processedChats: migrationState.processedChats,
                totalChats: migrationState.totalChats,
                sessionId: migrationState.sessionId
            }
        );
    } catch (error) {
        console.error('Error saving migration progress:', error);
        // Don't throw - progress saving failure shouldn't stop migration
    }
};

/**
 * Load migration progress from Firebase
 * @private
 */
const loadMigrationProgress = async () => {
    try {
        // Get the most recent progress entry
        const progressCollection = collection(db, 'migration_progress');
        const progressQuery = query(progressCollection, orderBy('lastUpdated', 'desc'), limit(1));
        const progressSnapshot = await getDocs(progressQuery);

        if (!progressSnapshot.empty) {
            const progressData = progressSnapshot.docs[0].data();

            await logMigrationOperation(
                OPERATION_TYPES.MIGRATION,
                LOG_LEVELS.INFO,
                'Loaded migration progress',
                {
                    sessionId: progressData.sessionId,
                    processedChats: progressData.processedChats,
                    lastProcessedChatId: progressData.lastProcessedChatId
                }
            );

            return progressData;
        }

        return null;
    } catch (error) {
        console.error('Error loading migration progress:', error);
        return null;
    }
};

/**
 * Validate migration options
 * @private
 */
const validateMigrationOptions = (options) => {
    const errors = [];

    if (options.chatTypes && !Array.isArray(options.chatTypes)) {
        errors.push('chatTypes must be an array');
    }

    if (options.batchSize && (typeof options.batchSize !== 'number' || options.batchSize < 1)) {
        errors.push('batchSize must be a positive number');
    }

    if (options.onProgress && typeof options.onProgress !== 'function') {
        errors.push('onProgress must be a function');
    }

    if (options.onError && typeof options.onError !== 'function') {
        errors.push('onError must be a function');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Pause the migration
 */
export const pauseMigration = async () => {
    migrationState.isPaused = true;

    await logMigrationOperation(
        OPERATION_TYPES.MIGRATION,
        LOG_LEVELS.INFO,
        'Migration paused by user',
        { sessionId: migrationState.sessionId }
    );
};

/**
 * Resume the migration
 */
export const resumeMigration = async () => {
    migrationState.isPaused = false;

    await logMigrationOperation(
        OPERATION_TYPES.MIGRATION,
        LOG_LEVELS.INFO,
        'Migration resumed by user',
        { sessionId: migrationState.sessionId }
    );
};

/**
 * Get current migration status
 */
export const getMigrationProgress = () => {
    return {
        ...migrationState,
        progressPercentage: migrationState.totalChats > 0
            ? (migrationState.processedChats / migrationState.totalChats) * 100
            : 0
    };
};

/**
 * Simple semaphore implementation for concurrency control
 */
class Semaphore {
    constructor(maxConcurrency) {
        this.maxConcurrency = maxConcurrency;
        this.currentConcurrency = 0;
        this.queue = [];
    }

    async acquire(task) {
        return new Promise((resolve, reject) => {
            this.queue.push({ task, resolve, reject });
            this.tryNext();
        });
    }

    tryNext() {
        if (this.currentConcurrency >= this.maxConcurrency || this.queue.length === 0) {
            return;
        }

        this.currentConcurrency++;
        const { task, resolve, reject } = this.queue.shift();

        task()
            .then(resolve)
            .catch(reject)
            .finally(() => {
                this.currentConcurrency--;
                this.tryNext();
            });
    }
}

/**
 * Emergency stop function
 */
export const emergencyStop = async () => {
    migrationState.isRunning = false;
    migrationState.isPaused = true;

    await logMigrationOperation(
        OPERATION_TYPES.MIGRATION,
        LOG_LEVELS.ERROR,
        'Migration emergency stop activated',
        { sessionId: migrationState.sessionId }
    );

    // Save current progress
    await saveMigrationProgress();
};
