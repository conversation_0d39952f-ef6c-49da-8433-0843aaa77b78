/**
 * Example Usage of Firebase Rollback and Cleanup Utilities
 *
 * This file demonstrates how to use the rollback and cleanup utilities
 * for managing Firebase collection migrations.
 */

import {
    rollbackToChatIdStructure,
    cleanupOldSenderIdPaths,
    getComprehensiveStatus
} from './rollbackCleanup';
import { RollbackCleanupCLI, parseArgs, runCLI } from './rollbackCleanupCLI';

/**
 * Example 1: Emergency Rollback Procedure
 */
export const emergencyRollbackExample = async (selectedChat) => {
    console.log('=== EMERGENCY ROLLBACK EXAMPLE ===');

    try {
        // Step 1: Check current status
        console.log('1. Checking current status...');
        const status = await getComprehensiveStatus(selectedChat);
        console.log('Status:', status);

        if (!status.canRollback) {
            console.log('❌ Rollback not possible');
            return { success: false, reason: 'Cannot rollback' };
        }

        // Step 2: Perform dry run rollback
        console.log('2. Performing dry run rollback...');
        const dryRunResult = await rollbackToChatIdStructure(selectedChat, {
            dryRun: true,
            verifyIntegrity: true
        });
        console.log('Dry run result:', dryRunResult);

        if (!dryRunResult.success) {
            console.log('❌ Dry run failed');
            return { success: false, reason: 'Dry run failed', errors: dryRunResult.errors };
        }

        // Step 3: Execute actual rollback
        console.log('3. Executing rollback...');
        const rollbackResult = await rollbackToChatIdStructure(selectedChat, {
            dryRun: false,
            deleteSource: false, // Keep chat ID messages initially
            batchSize: 50,
            verifyIntegrity: true
        });
        console.log('Rollback result:', rollbackResult);

        if (rollbackResult.success) {
            console.log('✅ Emergency rollback completed successfully');
            console.log(`Rolled back ${rollbackResult.rolledBackCount} messages`);
        } else {
            console.log('❌ Rollback failed');
            return { success: false, reason: 'Rollback failed', errors: rollbackResult.errors };
        }

        // Step 4: Verify rollback
        console.log('4. Verifying rollback...');
        const postRollbackStatus = await getComprehensiveStatus(selectedChat);
        console.log('Post-rollback status:', postRollbackStatus);

        return {
            success: true,
            rolledBackCount: rollbackResult.rolledBackCount,
            finalStatus: postRollbackStatus
        };

    } catch (error) {
        console.error('Emergency rollback failed:', error);
        return { success: false, reason: 'Exception occurred', error: error.message };
    }
};

/**
 * Example 2: Safe Cleanup Procedure
 */
export const safeCleanupExample = async (selectedChat) => {
    console.log('=== SAFE CLEANUP EXAMPLE ===');

    try {
        // Step 1: Check current status
        console.log('1. Checking current status...');
        const status = await getComprehensiveStatus(selectedChat);
        console.log('Status:', status);

        if (!status.canCleanup) {
            console.log('❌ Cleanup not possible');
            return { success: false, reason: 'Cannot cleanup' };
        }

        if (status.recommendedAction !== 'cleanup') {
            console.log(`⚠️ Status recommends: ${status.recommendedAction}, not cleanup`);
            console.log('Proceeding anyway for demonstration...');
        }

        // Step 2: Perform dry run cleanup
        console.log('2. Performing dry run cleanup...');
        const dryRunResult = await cleanupOldSenderIdPaths(selectedChat, {
            dryRun: true,
            verifyMigration: true
        });
        console.log('Dry run result:', dryRunResult);

        if (!dryRunResult.success) {
            console.log('❌ Dry run failed');
            return { success: false, reason: 'Dry run failed', errors: dryRunResult.errors };
        }

        // Step 3: Execute actual cleanup
        console.log('3. Executing cleanup...');
        const cleanupResult = await cleanupOldSenderIdPaths(selectedChat, {
            dryRun: false,
            batchSize: 50,
            verifyMigration: true
        });
        console.log('Cleanup result:', cleanupResult);

        if (cleanupResult.success) {
            console.log('✅ Cleanup completed successfully');
            console.log(`Deleted ${cleanupResult.deletedCount} old messages`);
        } else {
            console.log('❌ Cleanup failed');
            return { success: false, reason: 'Cleanup failed', errors: cleanupResult.errors };
        }

        // Step 4: Verify cleanup
        console.log('4. Verifying cleanup...');
        const postCleanupStatus = await getComprehensiveStatus(selectedChat);
        console.log('Post-cleanup status:', postCleanupStatus);

        return {
            success: true,
            deletedCount: cleanupResult.deletedCount,
            finalStatus: postCleanupStatus
        };

    } catch (error) {
        console.error('Cleanup failed:', error);
        return { success: false, reason: 'Exception occurred', error: error.message };
    }
};

/**
 * Example 3: CLI Usage Examples
 */
export const cliUsageExamples = () => {
    console.log('=== CLI USAGE EXAMPLES ===');

    // Example CLI arguments
    const examples = [
        {
            description: 'Check comprehensive status',
            args: ['status', '--detailed'],
            expectedParsed: {
                command: 'status',
                options: { detailed: true }
            }
        },
        {
            description: 'Dry run rollback',
            args: ['rollback', '--dry-run', '--verify-integrity'],
            expectedParsed: {
                command: 'rollback',
                options: { dryRun: true, verifyIntegrity: true }
            }
        },
        {
            description: 'Emergency rollback with source deletion',
            args: ['rollback', '--force', '--delete-source', '--batch-size=25'],
            expectedParsed: {
                command: 'rollback',
                options: { force: true, deleteSource: true, batchSize: 25 }
            }
        },
        {
            description: 'Dry run cleanup',
            args: ['cleanup', '--dry-run'],
            expectedParsed: {
                command: 'cleanup',
                options: { dryRun: true }
            }
        },
        {
            description: 'Production cleanup',
            args: ['cleanup', '--force', '--verify-migration', '--batch-size=100'],
            expectedParsed: {
                command: 'cleanup',
                options: { force: true, verifyMigration: true, batchSize: 100 }
            }
        }
    ];

    examples.forEach((example, index) => {
        console.log(`\n${index + 1}. ${example.description}`);
        console.log(`Command: node rollbackCleanupCLI.js ${example.args.join(' ')}`);

        const parsed = parseArgs(example.args);
        console.log('Parsed arguments:', parsed);

        // Verify parsing matches expected
        const matches = JSON.stringify(parsed) === JSON.stringify(example.expectedParsed);
        console.log(`Parsing correct: ${matches ? '✅' : '❌'}`);
    });
};

/**
 * Example 4: Comprehensive Status Monitoring
 */
export const statusMonitoringExample = async (selectedChat) => {
    console.log('=== STATUS MONITORING EXAMPLE ===');

    try {
        // Get comprehensive status
        const status = await getComprehensiveStatus(selectedChat);

        console.log('📊 COMPREHENSIVE STATUS:');
        console.log(`Chat ID: ${selectedChat?.id}`);
        console.log(`Chat Type: ${status.chatType || 'Unknown'}`);
        console.log(`Requires Operations: ${status.requiresOperations ? 'Yes' : 'No'}`);
        console.log(`Current Status: ${status.status}`);
        console.log(`Recommended Action: ${status.recommendedAction}`);

        if (status.requiresOperations) {
            console.log('\n📁 COLLECTION DETAILS:');
            console.log(`Legacy Path: ${status.legacyPath}`);
            console.log(`Current Path: ${status.currentPath}`);
            console.log(`Legacy Count: ${status.legacyCount || 0}`);
            console.log(`Current Count: ${status.currentCount || 0}`);

            console.log('\n🔧 AVAILABLE OPERATIONS:');
            console.log(`Can Rollback: ${status.canRollback ? '✅' : '❌'}`);
            console.log(`Can Cleanup: ${status.canCleanup ? '✅' : '❌'}`);

            console.log('\n💡 RECOMMENDATIONS:');
            switch (status.recommendedAction) {
                case 'migrate':
                    console.log('• Run migration to move messages from sender ID to chat ID structure');
                    break;
                case 'cleanup':
                    console.log('• Run cleanup to remove old sender ID-based messages');
                    console.log('• Ensure migration is verified before cleanup');
                    break;
                case 'none':
                    console.log('• No action required - system is in desired state');
                    break;
                default:
                    console.log('• Check individual operations for specific actions');
            }
        }

        if (status.error) {
            console.log(`\n❌ ERROR: ${status.error}`);
        }

        return status;

    } catch (error) {
        console.error('Status monitoring failed:', error);
        return { success: false, error: error.message };
    }
};

/**
 * Example 5: Batch Processing Multiple Chats
 */
export const batchProcessingExample = async (selectedChats) => {
    console.log('=== BATCH PROCESSING EXAMPLE ===');

    const results = {
        processed: 0,
        successful: 0,
        failed: 0,
        errors: []
    };

    for (const chat of selectedChats) {
        console.log(`\nProcessing chat: ${chat.id}`);
        results.processed++;

        try {
            // Check status for each chat
            const status = await getComprehensiveStatus(chat);
            console.log(`Chat ${chat.id} status: ${status.status}`);

            // Perform appropriate action based on status
            let operationResult;
            switch (status.recommendedAction) {
                case 'cleanup':
                    console.log(`Performing cleanup for chat ${chat.id}`);
                    operationResult = await cleanupOldSenderIdPaths(chat, {
                        dryRun: false,
                        verifyMigration: true
                    });
                    break;

                case 'migrate':
                    console.log(`Chat ${chat.id} needs migration (not rollback/cleanup)`);
                    operationResult = { success: true, skipped: true, reason: 'needs_migration' };
                    break;

                case 'none':
                    console.log(`No action needed for chat ${chat.id}`);
                    operationResult = { success: true, skipped: true, reason: 'no_action_needed' };
                    break;

                default:
                    console.log(`Unknown action for chat ${chat.id}: ${status.recommendedAction}`);
                    operationResult = { success: false, error: 'Unknown recommended action' };
            }

            if (operationResult.success) {
                results.successful++;
                console.log(`✅ Chat ${chat.id} processed successfully`);
            } else {
                results.failed++;
                results.errors.push({
                    chatId: chat.id,
                    error: operationResult.error || 'Unknown error'
                });
                console.log(`❌ Chat ${chat.id} processing failed`);
            }

        } catch (error) {
            results.failed++;
            results.errors.push({
                chatId: chat.id,
                error: error.message
            });
            console.error(`❌ Error processing chat ${chat.id}:`, error);
        }
    }

    console.log('\n📋 BATCH PROCESSING RESULTS:');
    console.log(`Total Processed: ${results.processed}`);
    console.log(`Successful: ${results.successful}`);
    console.log(`Failed: ${results.failed}`);
    console.log(`Success Rate: ${((results.successful / results.processed) * 100).toFixed(1)}%`);

    if (results.errors.length > 0) {
        console.log('\n❌ ERRORS:');
        results.errors.forEach((error, index) => {
            console.log(`${index + 1}. Chat ${error.chatId}: ${error.error}`);
        });
    }

    return results;
};

/**
 * Example 6: CLI Integration Example
 */
export const cliIntegrationExample = async (selectedChat) => {
    console.log('=== CLI INTEGRATION EXAMPLE ===');

    const cli = new RollbackCleanupCLI();

    try {
        // Example 1: Get status
        console.log('\n1. Getting status via CLI...');
        const statusResult = await cli.execute('status', selectedChat, { detailed: true });
        console.log('Status result:', statusResult.success ? '✅' : '❌');

        // Example 2: Dry run rollback
        console.log('\n2. Dry run rollback via CLI...');
        const dryRunResult = await cli.execute('rollback', selectedChat, {
            dryRun: true,
            verifyIntegrity: true
        });
        console.log('Dry run result:', dryRunResult.success ? '✅' : '❌');

        // Example 3: Dry run cleanup
        console.log('\n3. Dry run cleanup via CLI...');
        const cleanupDryRunResult = await cli.execute('cleanup', selectedChat, {
            dryRun: true,
            verifyMigration: true
        });
        console.log('Cleanup dry run result:', cleanupDryRunResult.success ? '✅' : '❌');

        // Example 4: Help
        console.log('\n4. Showing help via CLI...');
        const helpResult = await cli.execute('help', selectedChat);
        console.log('Help result:', helpResult.success ? '✅' : '❌');

        return {
            success: true,
            statusResult,
            dryRunResult,
            cleanupDryRunResult,
            helpResult
        };

    } catch (error) {
        console.error('CLI integration example failed:', error);
        return { success: false, error: error.message };
    }
};

/**
 * Main example runner
 */
export const runAllExamples = async () => {
    console.log('🚀 RUNNING ALL ROLLBACK & CLEANUP EXAMPLES');

    // Mock chat data for examples
    const mockChat = {
        id: 'example_chat_123',
        sender_id: 'sender_456',
        type: 'messenger'
    };

    const mockChats = [
        { id: 'chat_1', sender_id: 'sender_1', type: 'messenger' },
        { id: 'chat_2', sender_id: 'sender_2', type: 'messenger' },
        { id: 'chat_3', sender_id: 'sender_3', type: 'instagram' }
    ];

    try {
        // Run CLI usage examples (no async operations)
        cliUsageExamples();

        // Note: The following examples would require actual Firebase setup
        // They are commented out to avoid errors in the example file

        /*
        // Run status monitoring example
        console.log('\n' + '='.repeat(50));
        await statusMonitoringExample(mockChat);

        // Run CLI integration example
        console.log('\n' + '='.repeat(50));
        await cliIntegrationExample(mockChat);

        // Run emergency rollback example (commented for safety)
        // console.log('\n' + '='.repeat(50));
        // await emergencyRollbackExample(mockChat);

        // Run safe cleanup example (commented for safety)
        // console.log('\n' + '='.repeat(50));
        // await safeCleanupExample(mockChat);

        // Run batch processing example (commented for safety)
        // console.log('\n' + '='.repeat(50));
        // await batchProcessingExample(mockChats);
        */

        console.log('\n✅ All examples completed successfully');
        console.log('\n💡 Note: Firebase-dependent examples are commented out');
        console.log('   Uncomment and configure Firebase to run full examples');

    } catch (error) {
        console.error('❌ Example execution failed:', error);
    }
};

// Export all examples for individual use
export default {
    emergencyRollbackExample,
    safeCleanupExample,
    cliUsageExamples,
    statusMonitoringExample,
    batchProcessingExample,
    cliIntegrationExample,
    runAllExamples
};
