/**
 * Comment validation utilities
 * Provides validation functions for comment text, user permissions, and data sanitization
 */

// Constants for validation
export const VALIDATION_CONSTANTS = {
    MAX_COMMENT_LENGTH: 1000,
    MIN_COMMENT_LENGTH: 1,
    ALLOWED_HTML_TAGS: [], // No HTML allowed for security
    REQUIRED_USER_PERMISSIONS: ['comments_read', 'comments_write'], // Default permissions
    ADMIN_PERMISSIONS: ['admin', 'super_admin'],
    MODERATOR_PERMISSIONS: ['moderator', 'admin', 'super_admin']
};

/**
 * Validate comment text content
 * @param {string} text - The comment text to validate
 * @returns {Object} - Validation result with isValid and errors
 */
export const validateCommentText = (text) => {
    const errors = [];

    // Check if text exists
    if (!text || typeof text !== 'string') {
        errors.push('Comment text is required');
        return { isValid: false, errors };
    }

    const trimmedText = text.trim();

    // Check minimum length
    if (trimmedText.length < VALIDATION_CONSTANTS.MIN_COMMENT_LENGTH) {
        errors.push('Comment cannot be empty');
    }

    // Check maximum length
    if (trimmedText.length > VALIDATION_CONSTANTS.MAX_COMMENT_LENGTH) {
        errors.push(`Comment cannot exceed ${VALIDATION_CONSTANTS.MAX_COMMENT_LENGTH} characters`);
    }

    // Check for potentially malicious content
    if (containsMaliciousContent(trimmedText)) {
        errors.push('Comment contains invalid content');
    }

    return {
        isValid: errors.length === 0,
        errors,
        sanitizedText: sanitizeCommentText(trimmedText)
    };
};

/**
 * Check if text contains potentially malicious content
 * @param {string} text - Text to check
 * @returns {boolean} - True if malicious content detected
 */
const containsMaliciousContent = (text) => {
    // Check for script tags
    if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(text)) {
        return true;
    }

    // Check for javascript: URLs
    if (/javascript:/gi.test(text)) {
        return true;
    }

    // Check for on* event handlers
    if (/\bon\w+\s*=/gi.test(text)) {
        return true;
    }

    // Check for data: URLs with base64 content that might be malicious
    if (/data:(?!image\/[a-z]+;base64,)[^;]+;base64,/gi.test(text)) {
        return true;
    }

    return false;
};

/**
 * Sanitize comment text by removing/escaping dangerous content
 * @param {string} text - Text to sanitize
 * @returns {string} - Sanitized text
 */
export const sanitizeCommentText = (text) => {
    if (!text || typeof text !== 'string') {
        return '';
    }

    // Remove any HTML tags
    let sanitized = text.replace(/<[^>]*>/g, '');

    // Escape special characters that could be used for XSS
    sanitized = sanitized
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');

    // Remove excessive whitespace
    sanitized = sanitized.replace(/\s+/g, ' ').trim();

    return sanitized;
};

/**
 * Validate user permissions for comments functionality
 * @param {Object} user - User object from auth state
 * @param {string} action - Action to validate ('read', 'write', 'moderate')
 * @returns {Object} - Validation result with isValid and reason
 */
export const validateUserPermissions = (user, action = 'read') => {
    // Check if user exists and is authenticated
    if (!user || !user.user) {
        return {
            isValid: false,
            reason: 'User not authenticated',
            errorCode: 'AUTH_REQUIRED'
        };
    }

    // Check if user has basic user data
    if (!user.user.id || !user.user.email) {
        return {
            isValid: false,
            reason: 'Invalid user data',
            errorCode: 'INVALID_USER'
        };
    }

    // Check specific action permissions
    switch (action) {
        case 'read':
            // All authenticated users can read comments
            return { isValid: true };

        case 'write':
            // All authenticated users can write comments
            return { isValid: true };

        case 'moderate':
            // Comments moderation is not used in this system
            return {
                isValid: false,
                reason: 'Comment moderation is not available',
                errorCode: 'FEATURE_NOT_AVAILABLE'
            };

        default:
            return {
                isValid: false,
                reason: 'Invalid action specified',
                errorCode: 'INVALID_ACTION'
            };
    }
};

// Helper functions removed since moderation is not used in this system

/**
 * Validate chat context for comments
 * @param {string} chatId - Chat identifier
 * @param {string} chatType - Chat type ('whatsapp', 'messenger', 'instagram')
 * @returns {Object} - Validation result
 */
export const validateChatContext = (chatId, chatType) => {
    const errors = [];

    if (!chatId || typeof chatId !== 'string' || chatId.trim().length === 0) {
        errors.push('Valid chat ID is required');
    }

    if (!chatType || !['whatsapp', 'messenger', 'instagram'].includes(chatType)) {
        errors.push('Valid chat type is required (whatsapp, messenger, or instagram)');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Validate author information
 * @param {Object} author - Author object
 * @returns {Object} - Validation result
 */
export const validateAuthor = (author) => {
    const errors = [];

    if (!author || typeof author !== 'object') {
        errors.push('Author information is required');
        return { isValid: false, errors };
    }

    if (!author.id || (typeof author.id !== 'string' && typeof author.id !== 'number')) {
        errors.push('Author ID is required');
    }

    if (!author.name || typeof author.name !== 'string' || author.name.trim().length === 0) {
        errors.push('Author name is required');
    }

    if (!author.email || typeof author.email !== 'string' || !isValidEmail(author.email)) {
        errors.push('Valid author email is required');
    }

    return {
        isValid: errors.length === 0,
        errors,
        sanitizedAuthor: {
            id: String(author.id), // Ensure ID is always a string
            name: sanitizeCommentText(author.name || ''),
            email: author.email
        }
    };
};

/**
 * Simple email validation
 * @param {string} email - Email to validate
 * @returns {boolean} - True if email is valid
 */
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
