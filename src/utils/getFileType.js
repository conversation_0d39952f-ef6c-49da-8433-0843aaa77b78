/**
 * Determines the file type based on the file object or URL
 * @param {File|string} file - The file object or URL to check
 * @returns {string} - The file type (image, video, audio, pdf, document, or file)
 */
export const getFileType = (file) => {
    // If file is a string (URL), determine type from extension
    if (typeof file === 'string') {
        const extension = file.split('.').pop().toLowerCase();

        if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) {
            return 'image';
        } else if (['mp4', 'webm', 'mov', 'avi'].includes(extension)) {
            return 'video';
        } else if (['mp3', 'wav', 'ogg', 'm4a'].includes(extension)) {
            return 'audio';
        } else if (extension === 'pdf') {
            return 'pdf';
        } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(extension)) {
            return 'document';
        }
        return 'file';
    }

    // If file is null or undefined
    if (!file) return 'text';

    // If file is a File object
    if (file.type) {
        const mimeType = file.type.toLowerCase();

        if (mimeType.startsWith('image/')) {
            return 'image';
        } else if (mimeType.startsWith('video/')) {
            return 'video';
        } else if (mimeType.startsWith('audio/')) {
            return 'audio';
        } else if (mimeType === 'application/pdf') {
            return 'pdf';
        } else if (
            mimeType.includes('word') ||
            mimeType.includes('document') ||
            mimeType.includes('excel') ||
            mimeType.includes('spreadsheet') ||
            mimeType.includes('powerpoint') ||
            mimeType.includes('presentation') ||
            mimeType === 'text/plain'
        ) {
            return 'document';
        }
    }

    // If file has a name but no type, try to determine from extension
    if (file.name) {
        const extension = file.name.split('.').pop().toLowerCase();

        if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) {
            return 'image';
        } else if (['mp4', 'webm', 'mov', 'avi'].includes(extension)) {
            return 'video';
        } else if (['mp3', 'wav', 'ogg', 'm4a'].includes(extension)) {
            return 'audio';
        } else if (extension === 'pdf') {
            return 'pdf';
        } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(extension)) {
            return 'document';
        }
    }

    // Default to generic file type
    return 'file';
};
