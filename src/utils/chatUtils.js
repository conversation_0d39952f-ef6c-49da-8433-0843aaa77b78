/**
 * Utility functions for chat operations
 */

/**
 * Extract chat ID and type from the current selected chat
 * @param {Object} selectedChat - The selected Messenger/Instagram chat
 * @param {Object} selectedWhatsappChat - The selected WhatsApp chat
 * @param {string} activeFilter - The active filter ('whatsapp' or other)
 * @returns {Object|null} - Object with chatId and chatType, or null if no chat selected
 */
export const extractChatInfo = (selectedChat, selectedWhatsappChat, activeFilter) => {
    if (activeFilter === "whatsapp" && selectedWhatsappChat) {
        return {
            chatId: selectedWhatsappChat.sender_phone_number
                ?.trim()
                .replace(/^\+|\s+/g, ""),
            chatType: "whatsapp",
        };
    } else if (selectedChat) {
        console.log('[EXTRACT_CHAT_INFO] selectedChat structure:', {
            id: selectedChat.id,
            flage: selectedChat.flage,
            participants: selectedChat.participants,
            participantsData: selectedChat.participants?.data,
            participantsLength: selectedChat.participants?.data?.length
        });

        const chatId =
            selectedChat.flage === "instagram"
                ? selectedChat.participants?.data[1]?.id
                : selectedChat.participants?.data[0]?.id;

        console.log('[EXTRACT_CHAT_INFO] Extracted chatId:', {
            chatId,
            chatType: selectedChat.flage === "instagram" ? "instagram" : "messenger",
            isUndefined: chatId === undefined
        });

        return {
            chatId,
            chatType: selectedChat.flage === "instagram" ? "instagram" : "messenger",
        };
    }
    return null;
};

/**
 * Extract user information for read receipts
 * @param {Object} user - The user object from auth state
 * @returns {Object|null} - Object with user id and name, or null if no user
 */
export const extractUserInfo = (user) => {
    if (!user?.user) return null;

    return {
        id: String(user.user.id), // Ensure ID is always a string
        name: user.user.name || user.user.email || 'Unknown User',
        email: user.user.email,
        photo: user.user.photo || null
    };
};

/**
 * Get all comment IDs from a list of comments
 * @param {Array} comments - Array of comment objects
 * @returns {Array} - Array of comment IDs
 */
export const getCommentIds = (comments) => {
    return comments.map(comment => comment.id).filter(Boolean);
};

/**
 * Check if a user has read a specific comment
 * @param {Object} comment - The comment object
 * @param {string} userId - The user ID to check
 * @returns {boolean} - True if user has read the comment
 */
export const hasUserReadComment = (comment, userId) => {
    if (!comment || !comment.readBy || !userId) return false;
    return comment.readBy.some(receipt => receipt.userId === userId);
};

/**
 * Get unread comments for a specific user
 * @param {Array} comments - Array of comment objects
 * @param {string} userId - The user ID
 * @returns {Array} - Array of unread comments
 */
export const getUnreadComments = (comments, userId) => {
    if (!comments || !Array.isArray(comments) || !userId) return [];
    return comments.filter(comment => !hasUserReadComment(comment, userId));
};

/**
 * Get unread comment IDs for a specific user
 * @param {Array} comments - Array of comment objects
 * @param {string} userId - The user ID
 * @returns {Array} - Array of unread comment IDs
 */
export const getUnreadCommentIds = (comments, userId) => {
    const unreadComments = getUnreadComments(comments, userId);
    return getCommentIds(unreadComments);
};
