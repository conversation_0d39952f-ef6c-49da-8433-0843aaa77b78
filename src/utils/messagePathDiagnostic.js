/**
 * Message Path Diagnostic Utility
 *
 * This utility helps debug message path issues by showing exactly what paths
 * are being used for sending, receiving, and listening for Messenger/Instagram messages.
 */

import { getSenderId, getCollectionPaths, determineChatType } from '../services/firebase/collectionPaths';

/**
 * Diagnose message paths for a given chat
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} selectedPage - The selected page object
 * @returns {Object} Diagnostic information
 */
export const diagnoseMessagePaths = (selectedChat, selectedPage) => {
    if (!selectedChat || !selectedPage) {
        return {
            error: 'Missing selectedChat or selectedPage',
            selectedChat: !!selectedChat,
            selectedPage: !!selectedPage
        };
    }

    const chatType = determineChatType(selectedChat);
    const senderId = getSenderId(selectedChat, chatType);
    const collectionPaths = getCollectionPaths(selectedChat, chatType, selectedPage.page_id);

    // For Messenger/Instagram, show all possible sender IDs
    let possibleSenderIds = {};
    if (chatType === 'messenger' || chatType === 'instagram') {
        possibleSenderIds = {
            chatId: selectedChat.id,
            participantId: chatType === 'instagram'
                ? selectedChat.participants?.data?.[1]?.id
                : selectedChat.participants?.data?.[0]?.id,
            senderId: selectedChat.sender_id,
            userId: selectedChat.user_id
        };
    }

    return {
        chatType,
        pageId: selectedPage.id,
        selectedSenderId: senderId,
        possibleSenderIds,
        expectedPaths: {
            messages: collectionPaths.messages,
            comments: collectionPaths.comments
        },
        chatObject: {
            id: selectedChat.id,
            participants: selectedChat.participants?.data,
            sender_id: selectedChat.sender_id,
            user_id: selectedChat.user_id,
            flage: selectedChat.flage
        },
        recommendations: generateRecommendations(chatType, selectedChat, senderId, possibleSenderIds)
    };
};

/**
 * Generate recommendations based on diagnostic results
 */
const generateRecommendations = (chatType, selectedChat, senderId, possibleSenderIds) => {
    const recommendations = [];

    if (chatType === 'whatsapp') {
        recommendations.push('WhatsApp uses phone number as sender ID - should work correctly');
        return recommendations;
    }

    if (!senderId) {
        recommendations.push('❌ CRITICAL: Could not determine sender ID');
        recommendations.push('Check if selectedChat has id, participants, sender_id, or user_id fields');
        return recommendations;
    }

    // Check for potential mismatches
    const uniqueIds = new Set(Object.values(possibleSenderIds).filter(Boolean));
    if (uniqueIds.size > 1) {
        recommendations.push('⚠️  WARNING: Multiple possible sender IDs detected');
        recommendations.push('Backend webhook might use different ID than frontend');
        recommendations.push('Verify which ID the webhook uses for incoming messages');
    }

    if (selectedChat.id && selectedChat.id !== senderId) {
        recommendations.push('⚠️  Chat ID differs from selected sender ID');
        recommendations.push(`Chat ID: ${selectedChat.id}, Selected: ${senderId}`);
    }

    recommendations.push('✅ Check Firebase console for actual message paths');
    recommendations.push('✅ Verify backend webhook writes to same path as frontend expects');

    return recommendations;
};

/**
 * Test message path consistency
 * @param {Object} selectedChat - The selected chat object
 * @param {Object} selectedPage - The selected page object
 * @returns {Object} Test results
 */
export const testMessagePathConsistency = (selectedChat, selectedPage) => {
    const diagnostic = diagnoseMessagePaths(selectedChat, selectedPage);

    if (diagnostic.error) {
        return { success: false, error: diagnostic.error };
    }

    const tests = [];

    // Test 1: Can determine sender ID
    tests.push({
        name: 'Sender ID Resolution',
        passed: !!diagnostic.selectedSenderId,
        result: diagnostic.selectedSenderId || 'Could not determine',
        expected: 'Valid sender ID'
    });

    // Test 2: Can generate collection paths
    tests.push({
        name: 'Collection Path Generation',
        passed: !!diagnostic.expectedPaths.messages,
        result: diagnostic.expectedPaths.messages || 'Could not generate',
        expected: `${diagnostic.pageId}/${diagnostic.selectedSenderId}/messages`
    });

    // Test 3: Check for ID consistency (Messenger/Instagram only)
    if (diagnostic.chatType !== 'whatsapp') {
        const uniqueIds = new Set(Object.values(diagnostic.possibleSenderIds).filter(Boolean));
        tests.push({
            name: 'Sender ID Consistency',
            passed: uniqueIds.size === 1,
            result: `${uniqueIds.size} unique IDs: ${Array.from(uniqueIds).join(', ')}`,
            expected: '1 unique ID across all fields'
        });
    }

    const allPassed = tests.every(test => test.passed);

    return {
        success: allPassed,
        tests,
        diagnostic,
        summary: allPassed
            ? '✅ All tests passed - paths should work correctly'
            : '❌ Some tests failed - check recommendations'
    };
};

/**
 * Browser console helpers for debugging
 */
if (typeof window !== 'undefined') {
    window.diagnoseMessagePaths = diagnoseMessagePaths;
    window.testMessagePathConsistency = testMessagePathConsistency;

    // Helper to diagnose current selected chat
    window.diagnoseCurrentChat = (store) => {
        const state = store.getState().metaBusinessSuite;
        return diagnoseMessagePaths(state.selectedChat, state.selectedPage);
    };

    // Helper to test current chat consistency
    window.testCurrentChatConsistency = (store) => {
        const state = store.getState().metaBusinessSuite;
        return testMessagePathConsistency(state.selectedChat, state.selectedPage);
    };
}

export default {
    diagnoseMessagePaths,
    testMessagePathConsistency
};
