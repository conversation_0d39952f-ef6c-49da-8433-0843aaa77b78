import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
    selectComments,
    updateUnreadCommentCounts,
    selectShouldPlayNotificationSound,
    resetNotificationSound
} from '../redux/features/metaBusinessChatSlice';
import { playNotificationSound } from '../utils/notificationSound';

/**
 * Hook to handle comment notifications and unread counts
 */
export const useCommentNotifications = () => {
    const dispatch = useDispatch();
    const comments = useSelector(selectComments);
    const shouldPlaySound = useSelector(selectShouldPlayNotificationSound);
    const currentUser = useSelector((state) => state.auth.user);

    console.log('🎯 [HOOK] useCommentNotifications state:', {
        commentsCount: comments.length,
        shouldPlaySound,
        currentUserId: currentUser?.user?.id
    });

    // Update unread counts when comments change
    useEffect(() => {
        if (currentUser?.user?.id) {
            console.log('📊 [HOOK] Updating unread counts for user:', currentUser.user.id);
            dispatch(updateUnreadCommentCounts({
                currentUserId: String(currentUser.user.id)
            }));
        }
    }, [comments, currentUser, dispatch]);

    // Handle notification sound (this is redundant with middleware, but keeping for debugging)
    useEffect(() => {
        if (shouldPlaySound) {
            console.log('🔇 [HOOK] shouldPlaySound is true, but sound is PAUSED via hook...');
            // PAUSED: Comment out the sound playing
            // playNotificationSound();
            dispatch(resetNotificationSound());
        }
    }, [shouldPlaySound, dispatch]);
};
