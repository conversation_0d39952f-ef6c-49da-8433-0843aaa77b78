import { useEffect } from 'react';

const useSecurityProtection = () => {
    useEffect(() => {
        // Disable right-click
        const handleContextMenu = (e) => {
            e.preventDefault();
        };
        document.addEventListener('contextmenu', handleContextMenu);

        // Disable keyboard shortcuts
        const handleKeyDown = (e) => {
            if (e.ctrlKey && e.key.toLowerCase() === 'u') {
                e.preventDefault();
                alert("Viewing the page source is disabled.");
            }

            if (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === 'i') {
                e.preventDefault();
                alert("Opening developer tools is disabled.");
            }

            if (e.key === 'F12') {
                e.preventDefault();
                alert("Opening developer tools is disabled.");
            }

            if (e.ctrlKey && e.key.toLowerCase() === 'c') {
                e.preventDefault();
                alert("Copying is disabled.");
            }

            if (e.ctrl<PERSON>ey && e.shiftKey && e.key.toLowerCase() === 'j') {
                e.preventDefault();
                alert("Opening developer console is disabled.");
            }

            if (e.ctrlKey && e.key.toLowerCase() === 's') {
                e.preventDefault();
                alert("Saving the page is disabled.");
            }
        };
        document.addEventListener('keydown', handleKeyDown);

        // Prevent text selection
        document.body.style.userSelect = 'none';

        // Detect dev tools usage
        const checkDevTools = setInterval(() => {
            if (window?.devtools?.isOpen) {
                window.location.reload();
            }
        }, 1000);

        // Detect dev tools access via an element
        const element = new Image();
        Object.defineProperty(element, 'id', {
            get: function () {
                window.location.reload();
            }
        });

        // Blur the content when the window loses focus
        const handleBlur = () => {
            document.body.style.filter = 'blur(8px)';
        };

        // Remove blur when the window gains focus
        const handleFocus = () => {
            document.body.style.filter = 'none';
        };

        window.addEventListener('blur', handleBlur);
        window.addEventListener('focus', handleFocus);

        // Add watermark
        const watermark = document.createElement('div');
        watermark.style.position = 'fixed';
        watermark.style.top = '50%';
        watermark.style.left = '50%';
        watermark.style.transform = 'translate(-50%, -50%)';
        watermark.style.fontSize = '40px';
        watermark.style.color = 'rgba(255, 255, 255, 0.3)';
        watermark.style.pointerEvents = 'none';
        watermark.style.zIndex = '9999';
        document.body.appendChild(watermark);

        // Clean up
        return () => {
            document.removeEventListener('contextmenu', handleContextMenu);
            document.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('blur', handleBlur);
            window.removeEventListener('focus', handleFocus);
            clearInterval(checkDevTools);
            document.body.removeChild(watermark);  // Remove the watermark when the component unmounts
        };
    }, []);
};

export default useSecurityProtection;
