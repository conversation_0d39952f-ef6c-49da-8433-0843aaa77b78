import { useState, useEffect } from 'react';

export const useSwipe = (elementRef) => {
    const [touchStart, setTouchStart] = useState(null);
    const [touchEnd, setTouchEnd] = useState(null);
    const [isDragging, setIsDragging] = useState(false);
    const [scrollPosition, setScrollPosition] = useState(0);

    const minSwipeDistance = 50;

    const onTouchStart = (e) => {
        setTouchEnd(null);
        setTouchStart(e.targetTouches[0].clientX);
        setIsDragging(true);
    };

    const onTouchMove = (e) => {
        if (isDragging) {
            setTouchEnd(e.targetTouches[0].clientX);
        }
    };

    const onTouchEnd = () => {
        setIsDragging(false);
        if (!touchStart || !touchEnd) return;

        const distance = touchStart - touchEnd;
        const isLeftSwipe = distance > minSwipeDistance;
        const isRightSwipe = distance < -minSwipeDistance;

        if (elementRef.current) {
            const container = elementRef.current;
            const maxScroll = container.scrollWidth - container.clientWidth;

            if (isLeftSwipe) {
                const newPosition = Math.min(scrollPosition + container.clientWidth / 2, maxScroll);
                setScrollPosition(newPosition);
                container.scrollTo({ left: newPosition, behavior: 'smooth' });
            }
            if (isRightSwipe) {
                const newPosition = Math.max(scrollPosition - container.clientWidth / 2, 0);
                setScrollPosition(newPosition);
                container.scrollTo({ left: newPosition, behavior: 'smooth' });
            }
        }
    };

    return {
        onTouchStart,
        onTouchMove,
        onTouchEnd,
        isDragging
    };
};
