import { useState, useEffect } from 'react';
import { useSelector } from'react-redux';

/**
 * Custom hook to check if the user has an active package subscription
 * @returns {Object} - Object containing package status and modal visibility state
 */
const usePackageCheck = () => {
  const user = useSelector(state => state.auth.user);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [hasActivePackage, setHasActivePackage] = useState(true); // Default to true to prevent flashing

  useEffect(() => {
    // Check if user exists and has package information
    if (user) {
      // If package_id is null or undefined, user doesn't have an active package
      const packageStatus = (user.user?.package_id !== null && user.user?.package_id !== undefined) || user?.user?.parent_id !== null;
      setHasActivePackage(packageStatus);

      // Show modal if user doesn't have an active package
      if (!packageStatus) {
        setShowSubscriptionModal(true);
      }
    }
  }, [user]);

  return {
    hasActivePackage,
    showSubscriptionModal,
    setShowSubscriptionModal
  };
};

export default usePackageCheck;
