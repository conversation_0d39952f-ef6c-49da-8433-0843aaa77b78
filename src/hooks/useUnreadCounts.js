/**
 * Hook for managing unread message and comment counts
 * 
 * This hook monitors message and comment updates and maintains
 * unread counts for each chat, providing real-time updates
 * for notification badges.
 */

import { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectMessages,
  selectWhatsappChatMessages,
  selectComments,
  selectSelectedChat,
  selectSelectedWhatsappChat,
  selectActiveFilter,
  updateUnreadMessageCounts,
  markMessagesAsRead,
  selectChatUnreadMessages,
  selectChatUnreadComments,
} from '../redux/features/metaBusinessChatSlice';

/**
 * Hook for managing unread counts
 * @param {Object} options - Configuration options
 * @returns {Object} - Unread count utilities
 */
export const useUnreadCounts = (options = {}) => {
  const dispatch = useDispatch();
  
  // Redux selectors
  const messages = useSelector(selectMessages);
  const whatsappMessages = useSelector(selectWhatsappChatMessages);
  const comments = useSelector(selectComments);
  const selectedChat = useSelector(selectSelectedChat);
  const selectedWhatsappChat = useSelector(selectSelectedWhatsappChat);
  const activeFilter = useSelector(selectActiveFilter);
  const chatUnreadMessages = useSelector(selectChatUnreadMessages);
  const chatUnreadComments = useSelector(selectChatUnreadComments);
  const currentUser = useSelector((state) => state.auth?.user);

  // Refs to track previous states
  const prevMessagesRef = useRef([]);
  const prevWhatsappMessagesRef = useRef([]);
  const prevCommentsRef = useRef([]);
  const lastReadTimestampRef = useRef({});

  // Default options
  const {
    autoMarkAsRead = true,
    trackMessages = true,
    trackComments = true,
  } = options;

  /**
   * Get current chat ID based on active filter
   */
  const getCurrentChatId = () => {
    if (activeFilter === 'whatsapp') {
      return selectedWhatsappChat?.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "");
    } else {
      return selectedChat?.id?.toString();
    }
  };

  /**
   * Get current user ID
   */
  const getCurrentUserId = () => {
    return currentUser?.user?.id ? String(currentUser.user.id) : currentUser?.id ? String(currentUser.id) : null;
  };

  /**
   * Check if a message is from the current user
   */
  const isMessageFromCurrentUser = (message) => {
    const currentUserId = getCurrentUserId();
    if (!currentUserId) return false;

    if (activeFilter === 'whatsapp') {
      const currentUserPhone = selectedWhatsappChat?.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "");
      return message.sender === currentUserPhone;
    } else {
      const selectedPageId = selectedChat?.page_id?.toString();
      return message.from?.id?.toString() === selectedPageId || message.sender?.toString() === selectedPageId;
    }
  };

  /**
   * Calculate unread message count for a chat
   */
  const calculateUnreadMessageCount = (chatMessages, chatId) => {
    if (!chatMessages || !chatId) return 0;

    const currentUserId = getCurrentUserId();
    if (!currentUserId) return 0;

    const lastReadTimestamp = lastReadTimestampRef.current[chatId] || 0;

    return chatMessages.filter(message => {
      // Don't count own messages as unread
      if (isMessageFromCurrentUser(message)) return false;

      // Check if message is newer than last read timestamp
      const messageTime = new Date(message.created_time || message.created_at || 0).getTime();
      return messageTime > lastReadTimestamp;
    }).length;
  };

  /**
   * Mark messages as read for current chat
   */
  const markCurrentChatMessagesAsRead = () => {
    const chatId = getCurrentChatId();
    if (!chatId) return;

    lastReadTimestampRef.current[chatId] = Date.now();
    dispatch(markMessagesAsRead({ chatId }));
  };

  /**
   * Update unread count for a specific chat
   */
  const updateChatUnreadCount = (chatId, messages) => {
    if (!chatId || !trackMessages) return;

    const unreadCount = calculateUnreadMessageCount(messages, chatId);
    dispatch(updateUnreadMessageCounts({ chatId, unreadCount }));
  };

  // Monitor message changes for regular chats
  useEffect(() => {
    if (activeFilter !== 'whatsapp' && messages && trackMessages) {
      const currentChatId = getCurrentChatId();
      
      // Check if messages have changed
      const messagesChanged = JSON.stringify(messages) !== JSON.stringify(prevMessagesRef.current);
      
      if (messagesChanged && currentChatId) {
        updateChatUnreadCount(currentChatId, messages);
        
        // Auto-mark as read if user is actively viewing the chat
        if (autoMarkAsRead && selectedChat) {
          markCurrentChatMessagesAsRead();
        }
      }
      
      prevMessagesRef.current = messages;
    }
  }, [messages, activeFilter, selectedChat, trackMessages, autoMarkAsRead]);

  // Monitor message changes for WhatsApp chats
  useEffect(() => {
    if (activeFilter === 'whatsapp' && whatsappMessages && trackMessages) {
      const currentChatId = getCurrentChatId();
      
      // Check if messages have changed
      const messagesChanged = JSON.stringify(whatsappMessages) !== JSON.stringify(prevWhatsappMessagesRef.current);
      
      if (messagesChanged && currentChatId) {
        updateChatUnreadCount(currentChatId, whatsappMessages);
        
        // Auto-mark as read if user is actively viewing the chat
        if (autoMarkAsRead && selectedWhatsappChat) {
          markCurrentChatMessagesAsRead();
        }
      }
      
      prevWhatsappMessagesRef.current = whatsappMessages;
    }
  }, [whatsappMessages, activeFilter, selectedWhatsappChat, trackMessages, autoMarkAsRead]);

  // Monitor comment changes (handled by existing comment system)
  useEffect(() => {
    if (comments && trackComments) {
      // Comments are already handled by the existing comment notification system
      // This is just for consistency and future enhancements
      prevCommentsRef.current = comments;
    }
  }, [comments, trackComments]);

  // Mark messages as read when user switches to a chat
  useEffect(() => {
    if (autoMarkAsRead) {
      const chatId = getCurrentChatId();
      if (chatId && (selectedChat || selectedWhatsappChat)) {
        // Small delay to ensure messages are loaded
        const timer = setTimeout(() => {
          markCurrentChatMessagesAsRead();
        }, 500);

        return () => clearTimeout(timer);
      }
    }
  }, [selectedChat, selectedWhatsappChat, autoMarkAsRead]);

  return {
    // Utilities
    getCurrentChatId,
    getCurrentUserId,
    markCurrentChatMessagesAsRead,
    updateChatUnreadCount,
    calculateUnreadMessageCount,
    
    // State
    chatUnreadMessages,
    chatUnreadComments,
    
    // Getters
    getUnreadMessageCount: (chatId) => chatUnreadMessages[chatId]?.count || 0,
    getUnreadCommentCount: (chatId) => chatUnreadComments[chatId]?.count || 0,
    getTotalUnreadCount: (chatId) => {
      const messageCount = chatUnreadMessages[chatId]?.count || 0;
      const commentCount = chatUnreadComments[chatId]?.count || 0;
      return messageCount + commentCount;
    },
    
    // Status
    isTrackingMessages: trackMessages,
    isTrackingComments: trackComments,
    isAutoMarkingAsRead: autoMarkAsRead,
  };
};

export default useUnreadCounts;
