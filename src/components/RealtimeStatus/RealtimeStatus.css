/* Real-time Status Component Styles */

.realtime-status {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin: 16px 0;
  overflow: hidden;
  transition: all 0.2s ease;
}

.realtime-status-header {
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
}

.realtime-status-header:hover {
  background: #f1f5f9;
}

.realtime-status-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.expand-icon {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.realtime-status-content {
  padding: 16px;
}

/* Status Overview */
.status-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.status-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.status-value {
  font-size: 12px;
  font-weight: 600;
}

.status-value.active {
  color: #10b981;
}

.status-value.inactive {
  color: #6b7280;
}

/* Detailed Status */
.detailed-status {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.status-detail {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  font-size: 13px;
  border-bottom: 1px solid #f3f4f6;
}

.status-detail:last-child {
  border-bottom: none;
}

.active-listeners {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.active-listeners ul {
  margin: 8px 0 0 0;
  padding: 0;
  list-style: none;
}

.listener-item {
  padding: 4px 8px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 11px;
  font-family: monospace;
  color: #374151;
}

/* Controls */
.realtime-controls {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.realtime-controls h5 {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
}

.control-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: #ffffff;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.start-btn {
  color: #059669;
  border-color: #10b981;
}

.start-btn:hover:not(:disabled) {
  background: #ecfdf5;
  border-color: #059669;
}

.stop-btn {
  color: #dc2626;
  border-color: #ef4444;
}

.stop-btn:hover:not(:disabled) {
  background: #fef2f2;
  border-color: #dc2626;
}

/* Debug Info */
.debug-info {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px;
}

.debug-info details {
  font-size: 12px;
}

.debug-info summary {
  cursor: pointer;
  font-weight: 500;
  color: #374151;
  padding: 4px;
}

.debug-info pre {
  margin: 8px 0 0 0;
  padding: 8px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 10px;
  overflow-x: auto;
  color: #374151;
}

/* Compact Status */
.realtime-status-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 11px;
}

.realtime-indicators {
  display: flex;
  gap: 4px;
}

.realtime-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 9px;
  font-weight: bold;
  color: #ffffff;
  transition: all 0.2s ease;
}

.realtime-indicator.active {
  background: #10b981;
  box-shadow: 0 0 4px rgba(16, 185, 129, 0.4);
}

.realtime-indicator.inactive {
  background: #6b7280;
}

.realtime-count {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .status-overview {
    grid-template-columns: 1fr;
  }
  
  .control-buttons {
    flex-direction: column;
  }
  
  .control-btn {
    width: 100%;
  }
}
