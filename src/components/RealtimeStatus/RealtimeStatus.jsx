/**
 * Real-time Status Component
 * 
 * Displays the current status of real-time listeners and provides
 * controls for managing real-time functionality.
 */

import React, { useState, useEffect } from 'react';
import { useRealtimeChat } from '../../hooks/useRealtimeChat';
import './RealtimeStatus.css';

const RealtimeStatus = ({ showControls = false, compact = false }) => {
  const [listenerStatus, setListenerStatus] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    startChatsListener,
    startMessagesListener,
    startCommentsListener,
    stopAllListeners,
    getListenerStatus,
    isChatsListenerActive,
    isMessagesListenerActive,
    isCommentsListenerActive,
    activeListeners
  } = useRealtimeChat({
    autoStartChatsListener: true,
    autoStartMessagesListener: true,
    autoStartCommentsListener: false,
    cleanupOnUnmount: false
  });

  // Update listener status periodically
  useEffect(() => {
    const updateStatus = async () => {
      const status = await getListenerStatus();
      setListenerStatus(status);
    };

    updateStatus();
    const interval = setInterval(updateStatus, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [getListenerStatus]);

  const handleStartChatsListener = async () => {
    try {
      await startChatsListener();
      console.log('[REALTIME_STATUS] Started chats listener');
    } catch (error) {
      console.error('[REALTIME_STATUS] Failed to start chats listener:', error);
    }
  };

  const handleStartMessagesListener = async () => {
    try {
      await startMessagesListener();
      console.log('[REALTIME_STATUS] Started messages listener');
    } catch (error) {
      console.error('[REALTIME_STATUS] Failed to start messages listener:', error);
    }
  };

  const handleStartCommentsListener = async () => {
    try {
      await startCommentsListener();
      console.log('[REALTIME_STATUS] Started comments listener');
    } catch (error) {
      console.error('[REALTIME_STATUS] Failed to start comments listener:', error);
    }
  };

  const handleStopAllListeners = async () => {
    try {
      await stopAllListeners();
      console.log('[REALTIME_STATUS] Stopped all listeners');
    } catch (error) {
      console.error('[REALTIME_STATUS] Failed to stop listeners:', error);
    }
  };

  if (compact) {
    return (
      <div className="realtime-status-compact">
        <div className="realtime-indicators">
          <span 
            className={`realtime-indicator ${isChatsListenerActive ? 'active' : 'inactive'}`}
            title={`Chats listener: ${isChatsListenerActive ? 'Active' : 'Inactive'}`}
          >
            C
          </span>
          <span 
            className={`realtime-indicator ${isMessagesListenerActive ? 'active' : 'inactive'}`}
            title={`Messages listener: ${isMessagesListenerActive ? 'Active' : 'Inactive'}`}
          >
            M
          </span>
          <span 
            className={`realtime-indicator ${isCommentsListenerActive ? 'active' : 'inactive'}`}
            title={`Comments listener: ${isCommentsListenerActive ? 'Active' : 'Inactive'}`}
          >
            C
          </span>
        </div>
        {listenerStatus && (
          <span className="realtime-count">
            {listenerStatus.totalListeners} active
          </span>
        )}
      </div>
    );
  }

  return (
    <div className="realtime-status">
      <div className="realtime-status-header" onClick={() => setIsExpanded(!isExpanded)}>
        <h4>Real-time Status</h4>
        <span className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>▼</span>
      </div>

      {isExpanded && (
        <div className="realtime-status-content">
          {/* Status Overview */}
          <div className="status-overview">
            <div className="status-item">
              <span className="status-label">Chats:</span>
              <span className={`status-value ${isChatsListenerActive ? 'active' : 'inactive'}`}>
                {isChatsListenerActive ? '● Active' : '○ Inactive'}
              </span>
            </div>
            <div className="status-item">
              <span className="status-label">Messages:</span>
              <span className={`status-value ${isMessagesListenerActive ? 'active' : 'inactive'}`}>
                {isMessagesListenerActive ? '● Active' : '○ Inactive'}
              </span>
            </div>
            <div className="status-item">
              <span className="status-label">Comments:</span>
              <span className={`status-value ${isCommentsListenerActive ? 'active' : 'inactive'}`}>
                {isCommentsListenerActive ? '● Active' : '○ Inactive'}
              </span>
            </div>
          </div>

          {/* Detailed Status */}
          {listenerStatus && (
            <div className="detailed-status">
              <div className="status-detail">
                <strong>Total Listeners:</strong> {listenerStatus.totalListeners}
              </div>
              <div className="status-detail">
                <strong>Chat Listeners:</strong> {listenerStatus.chatListeners}
              </div>
              <div className="status-detail">
                <strong>Comment Listeners:</strong> {listenerStatus.commentListeners}
              </div>
              <div className="status-detail">
                <strong>Queued Read Receipts:</strong> {listenerStatus.queuedReadReceipts}
              </div>
              
              {listenerStatus.activeListeners && listenerStatus.activeListeners.length > 0 && (
                <div className="active-listeners">
                  <strong>Active Listeners:</strong>
                  <ul>
                    {listenerStatus.activeListeners.map((listener, index) => (
                      <li key={index} className="listener-item">{listener}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Controls */}
          {showControls && (
            <div className="realtime-controls">
              <h5>Controls</h5>
              <div className="control-buttons">
                <button 
                  onClick={handleStartChatsListener}
                  disabled={isChatsListenerActive}
                  className="control-btn start-btn"
                >
                  Start Chats
                </button>
                <button 
                  onClick={handleStartMessagesListener}
                  disabled={isMessagesListenerActive}
                  className="control-btn start-btn"
                >
                  Start Messages
                </button>
                <button 
                  onClick={handleStartCommentsListener}
                  disabled={isCommentsListenerActive}
                  className="control-btn start-btn"
                >
                  Start Comments
                </button>
                <button 
                  onClick={handleStopAllListeners}
                  disabled={!listenerStatus?.totalListeners}
                  className="control-btn stop-btn"
                >
                  Stop All
                </button>
              </div>
            </div>
          )}

          {/* Debug Info */}
          <div className="debug-info">
            <details>
              <summary>Debug Information</summary>
              <pre>{JSON.stringify(listenerStatus, null, 2)}</pre>
            </details>
          </div>
        </div>
      )}
    </div>
  );
};

export default RealtimeStatus;
