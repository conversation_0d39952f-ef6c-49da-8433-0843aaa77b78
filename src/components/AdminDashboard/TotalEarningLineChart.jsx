import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "victory";

const TotalEarningLineChart = () => {
    // Generating data for 7 days
    const data = [
        { x: 1, y: 750 },
        { x: 2, y: 650 },
        { x: 3, y: 600 },
        { x: 4, y: 650 },
        { x: 5, y: 600 },
        { x: 6, y: 650 },
        { x: 7, y: 600 }
    ];
    return (
        <VictoryChart theme={VictoryTheme.material}>
            {/* X-axis with 7 days */}
            <VictoryAxis
                tickValues={[1, 2, 3, 4, 5, 6, 7]}
                tickFormat={(day) => {
                    switch (day) {
                        case 1:
                            return "Sun";
                        case 2:
                            return "Mon";
                        case 3:
                            return "Tue";
                        case 4:
                            return "Wed";
                        case 5:
                            return "Thu";
                        case 6:
                            return "Fri";
                        case 7:
                            return "Sat";
                        default:
                            return "";
                    }
                }}
            />
            {/* Y-axis from 0 to 800 */}
            <VictoryAxis dependentAxis tickValues={[0, 200, 400, 600, 800]} />
            {/* Line chart */}
            <VictoryLine
                style={{
                    data: { stroke: "#92C020" },
                    parent: { border: "1px solid #ccc" }
                }}
                data={data}
            />
        </VictoryChart>
    );
};

export default TotalEarningLineChart;