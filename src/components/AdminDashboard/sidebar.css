.admin-sidebar {
    width: 70px ;
    transition: ease-in-out width 0.5s;
    padding-top: 2rem;
    padding-right: 5px;
    padding-left: 5px;
    min-height: 850px;
    max-height: 950px;
    box-sizing: border-box;
    border: 1px solid rgb(121, 121, 121);
    border-radius: 0 0 0 10px;
    box-shadow: 4px 0 93px 21px rgba(255, 255, 255, 0.17);
    background: rgb(0, 0, 0);
}

.admin-sidebar .nav-pills .nav-link {
    color: #FFFFFF;
    font-size: 1rem;
    font-weight: 400;
    border-radius: 8px;
}

.admin-sidebar .nav-pills .nav-link.active{
    color: #92C020;
    background: rgba(146, 192, 32, 0.2);
    font-weight: 900;
    transition: ease-in-out all 0.5s;
}

.admin-sidebar .menu-item_label, .admin-sidebar .settings-button_label {
    display: none;
}

.admin-sidebar.expanded {
    width: 250px;
    transition: ease-in-out width 0.5s;
}

.admin-sidebar.expanded .menu-item_label, .sidebar.expanded  .settings-button_label {
    display: block;
}

.admin-sidebar .menu-item {
    display: flex;
    justify-content: center;
    align-items: center;
}

.admin-sidebar .nav-item {
    width: fit-content;
}

.admin-sidebar.expanded .nav-item {
    width: 100%;
}

.admin-sidebar.expanded .menu-item {
    display: flex;
    justify-content: flex-start;
    text-align: left;
    width: 100%;
    font-weight: 400;
    font-size: 1rem;
    align-items: flex-end;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 50px;
    border: 1px solid #1E599D;
    max-width: 130px;
}

.sidebar-header_title {
    color: #1E599D;
    text-align: right;
    font-family: Cairo, sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 10px;
}

.admin-sidebar .sidebar-header_icon{
    border-radius: 5px;
    background-color: #1A478F;
    color: white;
    width: fit-content;
    padding: 5px;
    margin: 0 auto;
}

.admin-sidebar.expanded .sidebar-header_icon {
    background-color: #1A478F;
    color: white;
    padding: 5px;
    border-radius: 0 12px 12px 0;
    margin-right: 0;
}

.admin-sidebar.expanded .menu-header {
    color: #1A478F;
    text-align: right;
    font-family: Cairo, sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin: 27px 0;
}

.admin-sidebar .menu-header {
    color: #1A478F;
    text-align: center;
    font-family: Cairo, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin: 10px 0;
}

.admin-sidebar.expanded .settings-button {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 35px;
    padding: 10px 0;
}

.admin-sidebar .nav-pills .nav-link.active .settings-button {
    color: white;
}

.admin-sidebar .nav-pills .nav-link .settings-button {
    width: 100%;
}

.admin-sidebar.expanded .nav-pills .nav-link .settings-button {
    color: #1A478F;
    width: 100%;
}

.sidebar-top-section {
    border-radius: 20px 0 0 20px;
    position: relative;
}

.admin-sidebar-content {
    border-radius: 20px 0 0 20px;
    height: 90%;
}

.main-menu {
    color: #000;
    font-size: 0.875rem;
    font-weight: 400;
    text-transform: uppercase;
    opacity: 0.3;
    text-align: center;
}

.admin-sidebar.expanded {
    font-size: 1.25rem;
    font-weight: 400;
    width: 250px;
    transition: ease-in-out width 0.5s;
}

.admin-sidebar-nav-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 90vh;
}

.admin-sidebar.expanded .main-menu {
    text-align: left;
    font-size: 1.25rem;
}

.admin-sidebar.expanded .menu-item_label, .admin-sidebar.expanded  .settings-button_label {
    display: block;
}

.admin-sidebar .menu-item {
    display: flex;
    justify-content: center;
    align-items: center;
}

.admin-sidebar.expanded .menu-item {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    text-align: left;
    width: 100%;
}

.admin-sidebar.expanded .settings-button {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 35px;
    background: #F2F2F2;
    padding: 10px 0;
}

.expand-sidebar-icon-admin {
    position: absolute;
    top: -5%;
    right: 5%;
}

.collapse-sidebar-icon-admin {
    position: absolute;
    top: -3.8%;
    right: 0;
}

.sidebar-statistics {
    color: #92C020;
    font-weight: 900;
    padding: 10px;
    border-bottom: 2px solid rgb(121, 121, 121);
}
