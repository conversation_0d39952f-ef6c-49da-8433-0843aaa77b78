import {useEffect, useState} from "react";
import {useNavigate, useParams} from "react-router-dom";
import TeamMemberChartsComponent from "../../components/TeamMemberProfile/TeamMemberCharts.component";
import {IoCaretBack} from "react-icons/io5";
import AdminTeamMember from "../../pages/AdminDashboard/AdminTeamMember";
import adminService from "../../services/admin";

const TeamMemberAdminProfile = () => {
    const [teamMember, setTeamMember] = useState({});
    const params = useParams();

    useEffect(() => {
        const getData = async () => {
            try {
                const response = await adminService.getSingleAdminMemberApi(params.id);
                setTeamMember(response.data);
            } catch (error) {
                console.error("Error fetching team member data:", error);
            }
        };
        getData();
    }, [params.id]);

    const navigate = useNavigate();
    return (
        <>
            <div role={"button"} onClick={() => navigate(-1)} title={"Back To Teams"}>
                <IoCaretBack color={"#000"} className={"bg-white rounded-circle p-1"} size={35}/>
            </div>
            <AdminTeamMember teamMember={teamMember} setTeamMember={setTeamMember} params={params}/>
            <div className={"teamMemberProfileContainer admin-theme"}>
                <TeamMemberChartsComponent teamMember={teamMember} classNames={"admin-theme"}/>
                {/*<TeamMemberCalendarComponent />*/}
            </div>
        </>
    );
};

export default TeamMemberAdminProfile;