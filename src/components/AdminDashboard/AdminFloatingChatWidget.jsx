import { useEffect, useRef } from "react";
import { FaComments } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { setupGlobalAdminChatListener } from "../../redux/features/adminSupportChatSlice";
import "./AdminFloatingChatWidget.css";
import notificationSound from "../../assets/media/notification_sound.wav";

const AdminFloatingChatWidget = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const unreadChats = useSelector(
    (state) => state.adminSupportChat.unreadChats
  );
  const unreadCount = unreadChats.length;
  const hasNewMessages = unreadCount > 0;
  // PAUSED: Don't create audio element to prevent loading sound file
  // const notificationSoundRef = useRef(new Audio(notificationSound));
  const notificationSoundRef = useRef(null);
  const prevUnreadCountRef = useRef(0);

  // Get the RTL setting from i18n
  const { i18n } = useTranslation();
  const isRTL = i18n.language === "ar";

  // Ensure the global listener is set up
  useEffect(() => {
    const setupListener = async () => {
      await dispatch(setupGlobalAdminChatListener());
    };

    setupListener();

    // No need to return cleanup function here as it's handled in the main component
  }, [dispatch]);

  // Play notification sound when new unread messages arrive (PAUSED)
  useEffect(() => {
    // If there are new unread messages and the count has increased, play sound
    if (hasNewMessages && unreadCount > prevUnreadCountRef.current) {
      console.log(
        "🔇 [ADMIN FLOATING CHAT WIDGET] Notification sound PAUSED - would have played for new messages"
      );
      // PAUSED: Comment out the sound playing
      // notificationSoundRef.current.play().catch((error) => {
      //   console.error("Error playing notification sound:", error);
      // });
    }

    // Update the previous unread count reference
    prevUnreadCountRef.current = unreadCount;
  }, [hasNewMessages, unreadCount]);

  const handleClick = () => {
    navigate("/admin/support/chat");
  };

  // Hide the widget if we're already in the admin support chat page
  if (location.pathname.startsWith("/admin/support/chat")) {
    return null;
  }

  return (
    <div
      className={`admin-floating-chat-widget ${isRTL ? "rtl" : "ltr"} ${
        hasNewMessages ? "has-new-messages" : ""
      }`}
      onClick={handleClick}
      title="Client Support Chats"
    >
      <div className="widget-icon">
        <FaComments size={24} />
        {unreadCount > 0 && (
          <span className="unread-badge">
            {unreadCount > 9 ? "9+" : unreadCount}
          </span>
        )}
      </div>
      <div className="widget-label">Support</div>
    </div>
  );
};

export default AdminFloatingChatWidget;
