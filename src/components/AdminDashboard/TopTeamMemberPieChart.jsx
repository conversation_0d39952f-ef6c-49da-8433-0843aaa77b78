import { Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as <PERSON><PERSON><PERSON><PERSON>,
  Legend,
} from "chart.js";
import React from "react";

ChartJS.register(ArcElement, ChartTooltip, Legend);

const TopTeamMemberPieChart = ({ pieChartSource }) => {
  return (
    <>
      <div className={"d-flex justify-content-between text-white"}>
        <h2>Top Team Members</h2>
      </div>
      <div className="position-relative">
        <Doughnut
          data={{
            labels: pieChartSource
              ?.filter((datum) => datum.y > 0)
              ?.map((datum) => datum.x),
            datasets: [
              {
                data: pieChartSource
                  ?.filter((datum) => datum.y > 0)
                  ?.map((datum) => datum.y),
                backgroundColor: pieChartSource
                  ?.filter((datum) => datum.y > 0)
                  ?.map((datum) => datum.color),
                borderColor: "#fff",
                borderWidth: 4,
              },
            ],
          }}
          options={{
            responsive: true,
            plugins: {
              tooltip: {
                enabled: true,
                boxPadding: 8,
                callbacks: {
                  label: function (context) {
                    const label = context.label || "";
                    const value = context.raw || 0;
                    return `${label}: ${value}`;
                  },
                },
              },
              legend: {
                display: false,
              },
            },
            cutout: "80%",
          }}
        />
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            textAlign: "center",
            fontSize: "20px",
            fontWeight: "bold",
            color: "#fff",
            zIndex: 10,
          }}
        >
          {pieChartSource?.length > 0
            ? `${
                pieChartSource
                  ?.filter((datum) => datum.y > 0)
                  ?.reduce((a, b) => (a.y > b.y ? a : b), {
                    x: "",
                    y: 0,
                  }).x
              }: ${
                pieChartSource
                  .filter((datum) => datum.y > 0)
                  ?.reduce((a, b) => (a.y > b.y ? a : b), {
                    x: "",
                    y: 0,
                  }).y
              }`
            : "No data available"}
        </div>
      </div>
      <div className="labels-container text-capitalize mt-3 text-white">
        {pieChartSource
          ?.filter((datum) => datum.y > 0)
          ?.map((datum, index) => (
            <div key={index} className="label-item d-flex align-items-center">
              <span
                className="dot"
                style={{
                  backgroundColor: datum.color,
                  width: "10px",
                  height: "10px",
                  borderRadius: "50%",
                  display: "inline-block",
                  marginRight: "8px",
                }}
              />
              {datum.x}
            </div>
          ))}
      </div>
    </>
  );
};

export default TopTeamMemberPieChart;
