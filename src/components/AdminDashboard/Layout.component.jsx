import SidebarComponent from "./Sidebar.component";
import { Outlet } from "react-router-dom";
import "./admin-dashboard.css";
import Header from "./Header.component";
import "./layout.css";
import { useEffect, useState } from "react";
import { Container } from "react-bootstrap";
import AdminFloatingChatWidget from "./AdminFloatingChatWidget";
import AdminSupportChatModal from "./AdminSupportChatModal";
import { useSelector, useDispatch } from "react-redux";
import {
  setChatModalVisibility,
  selectShowChatModal,
} from "../../redux/features/adminSupportChatSlice";

const LayoutComponent = () => {
  const dispatch = useDispatch();
  const [isExpanded, setIsExpanded] = useState(true);
  const [mobileSidebar, setMobileSidebar] = useState(false);
  const showChatModal = useSelector(selectShowChatModal);

  const iconSize = mobileSidebar ? 22 : 20;

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 576) {
        setMobileSidebar(true);
        setIsExpanded(false);
      } else if (window.innerWidth <= 768) {
        setMobileSidebar(false);
        setIsExpanded(false);
      } else {
        setMobileSidebar(false);
        setIsExpanded(true);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <>
      <div className={"d-flex justify-content-start admin-page"}>
        <SidebarComponent
          isExpanded={isExpanded}
          mobileSidebar={mobileSidebar}
          setIsExpanded={setIsExpanded}
          iconSize={iconSize}
        />
        <Container>
          <Header />
          <main className={"mb-5"} style={{ marginTop: "3rem" }}>
            <Outlet />
          </main>
        </Container>

        {/* Admin Floating Chat Widget */}
        <AdminFloatingChatWidget />

        {/* Admin Support Chat Modal */}
        <AdminSupportChatModal
          show={showChatModal}
          onClose={() => dispatch(setChatModalVisibility(false))}
        />
      </div>
    </>
  );
};

export default LayoutComponent;
