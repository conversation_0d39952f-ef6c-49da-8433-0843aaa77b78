import { HiMiniXMark } from "react-icons/hi2";
import Form from "react-bootstrap/Form";
import * as yup from "yup";
import { Formik } from "formik";
import { <PERSON><PERSON>, Col, Row } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { handleAddAdminTeamMember } from "../../redux/features/clientSlice";
import { useEffect, useState } from "react";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import * as Yup from "yup";
import getAllRolesApi from "../../services/roles/get-all-roles.api";
import "react-phone-number-input/style.css";
import "./InviteNewAdminTM.css";
import { setRoles } from "../../redux/features/roleSlice";

const InviteNewAdminTm = ({ handleClose }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const MemberValidationSchema = yup.object().shape({
    name: yup.string().required("Name is required"),
    phone: Yup.string()
      .matches(/^\+?[0-9\s-]*$/, {
        message: "Invalid phone number format",
        excludeEmptyString: true,
      })
      .required("Phone is required"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    password: yup
      .string()
      .required("Password is required")
      .min(8, "Password must be at least 8 characters"),
    confirmPassword: yup
      .string()
      .required("Confirm Password is required")
      .oneOf([yup.ref("password")], "Passwords must match"),
    role: yup.string().required("Role is required"),
  });
  const dispatch = useDispatch();

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    const fetchRoles = async () => {
      const roleType = user.user.flag === "admin" ? "admin" : "user";
      const response = await getAllRolesApi(roleType);
      dispatch(setRoles(response?.data));
    };
    fetchRoles();
  }, [dispatch, user]);

  const { roles } = useSelector((state) => state.role);
  const filteredRoles = roles?.filter(
    (role) => !role.show_name.includes("Owner")
  );

  const roleNames = filteredRoles.map((role) => ({
    id: role.id,
    name: role.show_name.replace("owner-", ""),
    show_name: role.show_name,
  }));

  return (
    <Formik
      initialValues={{
        name: "",
        email: "",
        phone: "",
        password: "",
        role: "",
        confirmPassword: "",
      }}
      validationSchema={MemberValidationSchema}
      validateOnChange={false}
      validateOnBlur={true}
      onSubmit={(values, { resetForm }) => {
        dispatch(handleAddAdminTeamMember({ values, handleClose, resetForm }));
      }}
    >
      {({
        handleSubmit,
        handleChange,
        values,
        touched,
        errors,
        handleBlur,
      }) => (
        <Form className={"p-4"} noValidate onSubmit={handleSubmit}>
          <div
            className={
              "position-relative w-100 text-center mb-5 d-flex justify-content-center align-items-center "
            }
          >
            <h5 className={"fw-bold"}>Invite New Team Member</h5>
            <div
              className={"member-modal-close-icon"}
              onClick={() => handleClose()}
            >
              <HiMiniXMark size={25} color={"#E35757"} />
            </div>
          </div>
          <Row>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field">
                <Form.Label>Name</Form.Label>
                <Form.Control
                  name={"name"}
                  type="text"
                  placeholder="member name"
                  value={values.name}
                  onChange={handleChange}
                  isInvalid={touched.name && errors.name}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.name}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field">
                <Form.Label>Email</Form.Label>
                <Form.Control
                  name={"email"}
                  type="email"
                  placeholder="<EMAIL>"
                  value={values.email}
                  onChange={handleChange}
                  isInvalid={touched.email && errors.email}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.email}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field">
                <Form.Label>Phone</Form.Label>
                <Form.Control
                  name="phone"
                  type="tel"
                  placeholder="Enter phone number"
                  value={values.phone}
                  onChange={handleChange}
                  isInvalid={touched.phone && errors.phone}
                  onBlur={handleBlur}
                />
                {touched.phone && errors.phone && (
                  <small className="text-danger">{errors.phone}</small>
                )}
              </Form.Group>
            </Col>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field">
                <Form.Label>Role</Form.Label>
                <Form.Select
                  name="role"
                  aria-label="Team member role"
                  onChange={handleChange}
                  isInvalid={touched.role && errors.role}
                  value={values.role}
                  className="px-3"
                >
                  <option value="">Select Role</option>
                  {roleNames.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </Form.Select>
                <Form.Control.Feedback type="invalid">
                  {errors.role}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>
          <Row>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field position-relative password-input-container">
                <Form.Label>Password</Form.Label>
                <Form.Control
                  name={"password"}
                  type={showPassword ? "text" : "password"}
                  placeholder="password"
                  value={values.password}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isInvalid={touched.password && errors.password}
                />
                <div
                  className={"password-input-icon-TM"}
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <AiOutlineEye size={25} />
                  ) : (
                    <AiOutlineEyeInvisible size={25} />
                  )}
                </div>
                {touched.password && errors.password && (
                  <div className="text-danger mt-1">{errors.password}</div>
                )}
              </Form.Group>
            </Col>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field position-relative password-input-container">
                <Form.Label>Confirm Password</Form.Label>
                <Form.Control
                  name={"confirmPassword"}
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="confirm password"
                  value={values.confirmPassword}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isInvalid={touched.confirmPassword && errors.confirmPassword}
                />
                <div
                  className={"password-input-icon-TM"}
                  onClick={toggleConfirmPasswordVisibility}
                >
                  {showConfirmPassword ? (
                    <AiOutlineEye size={25} />
                  ) : (
                    <AiOutlineEyeInvisible size={25} />
                  )}
                </div>
                {touched.confirmPassword && errors.confirmPassword && (
                  <div className="text-danger mt-1">
                    {errors.confirmPassword}
                  </div>
                )}
              </Form.Group>
            </Col>
          </Row>
          <Button
            className={"submit-btn d-flex justify-content-center mx-auto"}
            style={{ width: "fit-content" }}
            type="submit"
          >
            Add to Page
          </Button>
        </Form>
      )}
    </Formik>
  );
};

export default InviteNewAdminTm;
