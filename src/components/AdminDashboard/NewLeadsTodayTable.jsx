import { useEffect, useMemo, useRef, useState } from "react";
import {
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import { Button, Col, Row, Table } from "react-bootstrap";
import { Link } from "react-router-dom";
import { BsFillCaretDownFill } from "react-icons/bs";
import { FaCalendarAlt } from "react-icons/fa";
import { TiArrowForward } from "react-icons/ti";
import DatePicker from "react-datepicker";
import { toast } from "react-toastify";
import { useSelector } from "react-redux";
import PaginationRecordsForReports from "../Reports/PaginationRecordsForReports";
import adminDashboardService from "../../services/admin/admin-dashboard-services";
import { useTranslatedColumns } from "../Reports/ColumnsForTables.module";
import "react-datepicker/dist/react-datepicker.css";

const formatDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split(".")[0];
};

function GlobalFilter({
  setGlobalFilter,
  fetchInitialData,
  recordsPerPage,
  currentPage,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  selectedTM,
  setSelectedTM,
  source,
  setSource,
}) {
  const applyFilters = async () => {
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      assigned: selectedTM,
      source,
      recordsPerPage,
      currentPage,
    };
    await fetchInitialData(params);
  };

  const clearFilters = () => {
    setGlobalFilter("");
    setStartDate(null);
    setEndDate(null);
    setSelectedTM(null);
    setSource(null);
    fetchInitialData({ currentPage, recordsPerPage });
  };

  return (
    <Row
      className={
        "align-items-center justify-content-center justify-content-lg-between flex-column flex-lg-row"
      }
    >
      <Col lg={3} md={8} className={"my-2"}>
        <select
          value={source || ""}
          onChange={(e) => setSource(e.target.value)}
          className="form-select rounded-pill"
          aria-label="Default select example"
        >
          <option value="">Select Source</option>
          <option value={1}>Facebook</option>
          <option value={2}>Google Adsense</option>
          <option value={3}>SnapChat</option>
          <option value={4}>TikTok</option>
          <option value={5}>Instagram</option>
          <option value={6}>LinkedIn</option>
          <option value={7}>Phone</option>
          <option value={8}>WhatsApp</option>
          <option value={9}>Web (Generic)</option>
        </select>
      </Col>

      <Col lg={4} md={8} className={"d-flex justify-content-between"}>
        <DatePicker
          className={"date-picker-container my-2"}
          key={startDate}
          selected={startDate || null}
          onChange={(date) => setStartDate(date)}
          selectsStart
          startDate={startDate || null}
          endDate={endDate}
          customInput={
            <div className="position-relative">
              <FaCalendarAlt
                className="position-absolute top-50 end-0 translate-middle-y me-2"
                style={{ pointerEvents: "none" }}
              />
              <input
                className="form-control rounded-pill"
                placeholder={"From"}
                defaultValue={
                  startDate ? new Date(startDate).toLocaleDateString() : ""
                }
              />
            </div>
          }
        />
        <DatePicker
          className={"date-picker-container my-2 ms-3"}
          key={endDate}
          selected={endDate}
          onChange={(date) => setEndDate(date)}
          selectsEnd
          startDate={startDate || null}
          endDate={endDate || null}
          minDate={startDate}
          placeholderText="To"
          customInput={
            <div className="position-relative">
              <FaCalendarAlt
                className="position-absolute top-50 end-0 translate-middle-y me-2"
                style={{ pointerEvents: "none" }}
              />
              <input
                className="form-control rounded-pill"
                placeholder={"To"}
                defaultValue={
                  endDate ? new Date(endDate).toLocaleDateString() : ""
                }
              />
            </div>
          }
        />
      </Col>
      <Col
        lg={4}
        md={8}
        className={"d-flex justify-content-evenly justify-content-lg-end my-2"}
      >
        <Button onClick={applyFilters} className="px-3 py-2 apply-btn">
          Apply
        </Button>
        <Button
          onClick={clearFilters}
          className="rounded-pill clear-btn px-3 mx-2"
        >
          Clear
        </Button>
        <div
          className={
            "d-flex justify-content-between align-items-center rounded-pill bg-dark text-white px-3 py-1 fs-6"
          }
          role={"button"}
        >
          <TiArrowForward />
          <div>Export</div>
        </div>
      </Col>
    </Row>
  );
}

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={`Search ${count} records...`}
    />
  );
}

const NewLeadsTodayTable = () => {
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [data, setData] = useState([]);
  const [paginationLinks, setPaginationLinks] = useState([]);
  const [total, setTotal] = useState(0);
  const [recordsToDisplay, setRecordsToDisplay] = useState(10); // Default value
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [selectedTM, setSelectedTM] = useState(null);
  const [source, setSource] = useState(null);
  const fetchLeadsAbortController = useRef(null);
  const { selectedClient } = useSelector((state) => state.admin);
  const fetchInitialData = async (params) => {
    try {
      setLoading(true);

      // Abort previous fetch if any
      if (fetchLeadsAbortController.current) {
        fetchLeadsAbortController.current.abort();
      }
      fetchLeadsAbortController.current = new AbortController();
      const signal = fetchLeadsAbortController.current.signal;
      // Fetch data
      const response = await adminDashboardService.getAdminNewLeadsTodayApi({
        signal,
        params,
      });

      // Handle response
      if (response?.success) {
        if (
          response?.result === "there is no data" ||
          (Array.isArray(response?.result) && response?.result.length === 0)
        ) {
          // Handle no data case
          setData([]);
          setTotal(0);
          setRecordsToDisplay(0);
          setPaginationLinks([]);
          toast.info("No data available for the selected filters.", {
            position: "bottom-right",
            theme: "dark",
          });
        } else {
          const { data, current_page, per_page, links, total, to } =
            response.result;
          setData(data);
          setCurrentPage(current_page);
          setRecordsPerPage(per_page);
          setPaginationLinks(links);
          setTotal(total);
          setRecordsToDisplay(to);
        }
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching initial data:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInitialData({
      currentPage,
      recordsPerPage,
      clientId: selectedClient?.id || 144,
    });
  }, [selectedClient?.id]);

  const defaultColumn = useMemo(
    () => ({
      Filter: DefaultColumnFilter,
    }),
    []
  );
  const { newLeadsForAdmin } = useTranslatedColumns();
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    preGlobalFilteredRows,
    setGlobalFilter,
    setPageSize,
    rows,
    state: { globalFilter },
  } = useTable(
    {
      columns: newLeadsForAdmin,
      data: data,
      defaultColumn,
      initialState: { pageIndex: currentPage - 1, pageSize: recordsPerPage },
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const handlePageChange = async (url) => {
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      assigned: selectedTM,
      source,
      url,
      recordsPerPage,
    };
    await fetchInitialData(params);
  };

  const handlePageSizeChange = async (size) => {
    setRecordsPerPage(size);
    setPageSize(size);
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      assigned: selectedTM,
      source,
      currentPage,
      recordsPerPage: size,
    };
    await fetchInitialData(params);
  };

  return loading ? (
    <FetchingDataLoading className={"admin-theme"} />
  ) : data.length > 0 ? (
    <>
      <div className={"admin-theme all-leads-table px-2"}>
        <h2 className={"page-title text-white"}>New leads today</h2>
        <Table
          responsive={"xl"}
          className="table text-center position-relative lh-lg"
          {...getTableProps()}
        >
          {loading ? (
            <FetchingDataLoading />
          ) : (
            <>
              <thead>
                {headerGroups?.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers?.map((column, j) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={j}
                      >
                        {column.render("Header")}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {rows?.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      style={{ cursor: "default" }}
                      key={row.original.id}
                    >
                      {row?.cells?.map((cell, j) => {
                        return (
                          <td {...cell.getCellProps()} key={j}>
                            {cell.column.id === "client_name" ? (
                              <Link
                                to={`/admin/clients/leads/${row.original.client_id}`}
                                className={"p-2"}
                              >
                                {cell.render("Cell")}
                              </Link>
                            ) : (
                              <Link
                                to={`/admin/clients/leads/profile/${row.original.id}`}
                                className={"p-2"}
                              >
                                {cell.render("Cell")}
                              </Link>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </>
          )}
        </Table>
        <PaginationRecordsForReports
          onPageChange={handlePageChange}
          links={paginationLinks}
          handlePageSizeChange={handlePageSizeChange}
          per_page={recordsPerPage}
          to={recordsToDisplay}
          total={total}
          className={"dark-mode"}
        />
      </div>
    </>
  ) : (
    <h1 className={"admin-theme text-center py-4 text-white"}>
      There is no new leads
    </h1>
  );
};

export default NewLeadsTodayTable;
