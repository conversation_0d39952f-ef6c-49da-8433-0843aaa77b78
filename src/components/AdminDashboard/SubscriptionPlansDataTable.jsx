import { useMemo } from "react";
import {
    useFilters,
    useGlobalFilter,
    usePagination,
    useSortBy,
    useTable,
} from "react-table";
import { Pagination, Table } from "react-bootstrap";
import { BsFillCaretDownFill } from "react-icons/bs";
import { Link } from "react-router-dom";
import FetchingDataAdmin from "../LoadingAnimation/FetchingDataAdmin";

function DefaultColumnFilter({
    column: { filterValue, preFilteredRows, setFilter },
}) {
    const count = preFilteredRows.length;

    return (
        <input
            className="form-control"
            value={filterValue || ""}
            onChange={(e) => {
                setFilter(e.target.value || undefined);
            }}
            placeholder={`Search ${count} records...`}
        />
    );
}

const SubscriptionPlansDataTable = ({
    columns,
    data,
    loading,
    classNames,
    setCurrentPage,
    currentPage,
    recordsPerPage,
    setRecordsPerPage,
    totalPages,
}) => {
    const defaultColumn = useMemo(
        () => ({
            // Default Filter UI
            Filter: DefaultColumnFilter,
        }),
        [],
    );
    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        prepareRow,
        canPreviousPage,
        canNextPage,
        gotoPage,
        setPageSize,
        rows,
        state: { pageIndex, pageSize },
    } = useTable(
        {
            columns,
            data,
            defaultColumn,
            initialState: { pageIndex: currentPage - 1, pageSize: recordsPerPage },
        },
        useFilters,
        useGlobalFilter,
        useSortBy,
        usePagination,
    );

    const handlePageChange = (pageIndex) => {
        setCurrentPage(pageIndex + 1);
        gotoPage(pageIndex);
    };

    const handlePageSizeChange = (size) => {
        setRecordsPerPage(size);
        setPageSize(size);
    };
    return loading ? (
        <FetchingDataAdmin className={"admin-theme"} />
    ) : (
        <>
            <div className={`all-leads-table ${classNames} py-2`}>
                <p className={"fs-5 fw-bold text-white"}>Subscriptions Plans</p>
                <Table
                    responsive
                    className="table text-center position-relative"
                    {...getTableProps()}
                >
                    {loading ? (
                        <FetchingDataAdmin className={"admin-theme"} />
                    ) : (
                        <>
                            <thead>
                                {headerGroups.map((headerGroup, index) => (
                                    <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                                        {headerGroup.headers.map((column, j) => (
                                            <th
                                                {...column.getHeaderProps(
                                                    column.getSortByToggleProps(),
                                                )}
                                                key={j}
                                            >
                                                {column.render("Header")}
                                                {/* Render the columns filter UI */}
                                                {/*<div>{column.canFilter ? column.render('Filter') : null}</div>*/}
                                                <span>
                                                    {column.isSorted ? (
                                                        column.isSortedDesc ? (
                                                            " 🔽"
                                                        ) : (
                                                            " 🔼"
                                                        )
                                                    ) : (
                                                        <> {column.accessor && <BsFillCaretDownFill />}</>
                                                    )}
                                                </span>
                                            </th>
                                        ))}
                                    </tr>
                                ))}
                            </thead>
                            <tbody {...getTableBodyProps()}>
                                {rows.map((row) => {
                                    prepareRow(row);
                                    return (
                                        <tr
                                            {...row.getRowProps()}
                                            className={"client-table-row filter-table-rows"}
                                            key={row.original.id}
                                        >
                                            {row.cells.map((cell, j) => {
                                                return (
                                                    <td {...cell.getCellProps()} key={j}>
                                                        {cell.column.id === "Actions" ||
                                                            cell.column.id === "lastActivity" ||
                                                            cell.column.id === "assignedTo" ? (
                                                            cell.render("Cell")
                                                        ) : (
                                                            <Link
                                                                to={`/admin/client/leads/profile/${row.original.id}`}
                                                                className={"p-2"}
                                                            >
                                                                {cell.render("Cell")}
                                                            </Link>
                                                        )}
                                                    </td>
                                                );
                                            })}
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </>
                    )}
                </Table>
                <div
                    className={
                        "d-flex justify-content-between flex-column flex-md-row align-items-center my-4 mx-3"
                    }
                >
                    <div
                        className={
                            "d-flex flex-column justify-content-center align-items-center"
                        }
                    >
                        <div className={"mb-1 text-white"}>Records Per Page</div>
                        <div className="btn-group records-buttons-container" role="group">
                            {[10, 20, 30, 40, 50].map((pageSizeOption) => (
                                <div
                                    key={pageSizeOption}
                                    role="button"
                                    className={`${pageSize === pageSizeOption ? "record-button-selected" : "record-button"}`}
                                    onClick={() => handlePageSizeChange(pageSizeOption)}
                                >
                                    {pageSizeOption}
                                </div>
                            ))}
                        </div>
                    </div>
                    <Pagination className={"data-table-pagination"}>
                        <Pagination.Prev
                            onClick={() => handlePageChange(pageIndex - 1)}
                            disabled={!canPreviousPage}
                        />
                        {Array.from({ length: totalPages }).map((_, index) => {
                            if (
                                totalPages <= 5 ||
                                index === 0 ||
                                index === totalPages - 1 ||
                                (index >= pageIndex - 2 && index <= pageIndex + 2)
                            ) {
                                return (
                                    <Pagination.Item
                                        key={index}
                                        onClick={() => handlePageChange(index)}
                                        active={pageIndex === index}
                                    >
                                        {index + 1}
                                    </Pagination.Item>
                                );
                            } else if (index === 1 || index === totalPages - 2) {
                                return <Pagination.Ellipsis key={index} />;
                            }
                            return null;
                        })}
                        <Pagination.Next
                            onClick={() => handlePageChange(pageIndex + 1)}
                            disabled={!canNextPage}
                        />
                    </Pagination>
                </div>
            </div>
        </>
    );
};

export default SubscriptionPlansDataTable;
