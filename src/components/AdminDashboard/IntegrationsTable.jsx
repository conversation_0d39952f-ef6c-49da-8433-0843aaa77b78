import {useMemo, useState} from 'react';
import IntegrationsDataTable from "./IntegrationsDataTable";
import {IoSparklesSharp} from "react-icons/io5";
import {ReactSVG} from "react-svg";
import {Tooltip} from "react-tooltip";
import {sourceToIcon} from "../../constants/sourceIcons";


const IntegrationsTable = () => {
    const [loading, setLoading] = useState(false);
    const [totalPages, setTotalPages] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(10);
    const columns = useMemo(() => [{
        Header: ' ', Cell: ({row}) => {
            if (row.original.assignedTo === null || row.original.assignedTo === undefined) {
                return <IoSparklesSharp size={20} color={"#92C020"}/>;
            } else {
                return null;
            }
        },
    }, {
        Header: "Source", accessor: "source", Cell: ({row}) => {
            const source = row.original.source;
            const IconComponent = sourceToIcon[source] || null;

            return (<div className={"mx-auto social-icon-container"}>
                {IconComponent && <ReactSVG src={IconComponent}/>}
            </div>);
        },
    },
        {
            Header: "Page Name",
            accessor: "page_name",
            Cell: ({row}) => {
                const pageName = row.original.page_name;
                return (
                    <>
                        <div
                            className={`one-line page-name${row.original.id} mx-auto`}
                            style={{maxWidth: "150px"}}
                        >
                            {pageName}
                        </div>
                        <Tooltip
                            anchorSelect={`.page-name${row.original.id}`}
                            content={pageName}
                            className={"bg-dark text-white"}
                        />
                    </>
                );
            }
        },
         {
            Header: "Camping's count", accessor: "campings_count",
        },
        {
            Header: "Leads Count", accessor: "leads_count"
        }], []);
    const data = [
        {
            id: 1,
            source: 1,
            page_name: "DigiMindz Marketing Agency",
            campings_count: 1,
            leads_count: 1
        },
        {
            id: 2,
            source: 4,
            page_name: "DigiMindz Marketing Agency",
            campings_count: 1,
            leads_count: 1
        },
        {
            id: 3,
            source: 5,
            page_name: "DigiMindz Marketing Agency",
            campings_count: 1,
            leads_count: 1
        },
        {
            id: 4,
            source: 1,
            page_name: "DigiMindz Marketing Agency",
            campings_count: 1,
            leads_count: 1
        },
    ]
    return (
        <IntegrationsDataTable
            setCurrentPage={setCurrentPage}
            currentPage={currentPage}
            recordsPerPage={recordsPerPage}
            setRecordsPerPage={setRecordsPerPage}
            loading={loading} classNames={"admin-theme px-3 py-2"} data={data} columns={columns} totalPages={totalPages} setTotalPages={setTotalPages}
        />
    );
};

export default IntegrationsTable;
