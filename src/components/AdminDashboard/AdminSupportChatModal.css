.admin-support-chat-modal .modal-content {
  height: 80vh;
}

.admin-support-chat-modal .modal-body {
  padding: 0;
  display: flex;
  overflow: hidden;
}

.clients-sidebar {
  width: 300px;
  border-right: 1px solid #dee2e6;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-container {
  padding: 10px;
  border-bottom: 1px solid #dee2e6;
}

.clients-list {
  overflow-y: auto;
  flex-grow: 1;
}

.client-item {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.client-item.has-unread {
  background-color: rgba(13, 110, 253, 0.1);
}

.client-name {
  font-weight: 500;
  margin-bottom: 3px;
}

.last-message {
  font-size: 0.85rem;
  color: #6c757d;
  max-width: 180px;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chat-area.full-width {
  width: 100%;
}

.chat-messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message-container {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-left {
  align-self: flex-start;
  align-items: flex-start;
}

.message-right {
  align-self: flex-end;
  align-items: flex-end;
}

.message-bubble {
  padding: 10px 15px;
  border-radius: 18px;
  position: relative;
  word-break: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.message-bubble:hover {
  opacity: 0.9;
}

.client-message {
  background-color: #f1f0f0;
  color: #333;
  border-top-left-radius: 4px;
}

.support-message {
  background-color: #0d6efd;
  color: white;
  border-top-right-radius: 4px;
}

/* Rename admin-message to support-message for consistency */
.message-right .message-bubble {
  background-color: #0d6efd;
  color: white;
  border-top-right-radius: 4px;
}

.message-left .message-bubble {
  background-color: lightgray;
  color: #333;
  border-top-left-radius: 4px;
}

.message-content {
  margin-bottom: 5px;
  white-space: pre-wrap;
}

.message-time {
  font-size: 0.75rem;
  margin-top: 5px;
  opacity: 0.8;
  text-align: right;
}

/* Add a subtle animation for time appearance */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 0.8; }
}

.message-time {
  animation: fadeIn 0.3s ease;
}

.message-sender {
  font-size: 0.8rem;
  margin-top: 4px;
  color: #666;
}

.message-input-container {
  padding: 15px;
  border-top: 1px solid #dee2e6;
  background-color: #f8f9fa;
}

.attachment-preview {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.attachment-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button {
  color: #0d6efd;
  transition: transform 0.2s;
}

.back-button:hover {
  transform: translateX(-3px);
}

/* Media queries for responsive design */
@media (max-width: 767.98px) {
  .clients-sidebar {
    width: 100%;
  }

  .chat-area {
    width: 100%;
  }
}

/* Improve active focus colors for chat items */
.client-item.active {
  background-color: #0d6efd;
  color: #FFF;
  border-left: 4px solid #0a58ca;
}

.client-item.active .last-message,
.client-item.active .text-muted {
  color: #0a58ca;
}

/* Add a focus style that's distinct from the active style */
.client-item:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  outline: none;
}

/* Improve the has-unread styling to be more noticeable */
.client-item.has-unread {
  background-color: rgba(13, 110, 253, 0.1);
  border-left: 4px solid #0d6efd;
}

/* Style for when a chat is both active and has unread messages */
.client-item.active.has-unread {
  background-color: #0d6efd;
  border-left: 4px solid #ffc107;
}

/* Improve focus for the message input */
.message-input:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.message-image {
  border-radius: 8px;
  transition: transform 0.2s;
}

.message-image:hover {
  transform: scale(1.05);
}

.file-attachment {
  display: inline-block;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
  transition: background-color 0.2s;
}

.file-attachment:hover {
  background-color: rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.message-audio {
  max-width: 200px;
  margin: 5px 0;
}

.support-message .file-attachment {
  background-color: rgba(255, 255, 255, 0.2);
}

.support-message .file-attachment:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

