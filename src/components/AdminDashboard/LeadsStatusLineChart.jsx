import "./project-overview.css";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip as ChartTooltip,
  Legend,
} from "chart.js";
import { useTranslation } from "react-i18next";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ChartTooltip,
  Legend
);

const LeadsStatusLineChart = ({ lineChartData }) => {
  const { t } = useTranslation();

  return (
    <>
      <h2 className="text-white">
        {t("dashboard.leadsStatusYear", "Leads Status Over The Year")}
      </h2>
      <div
        style={{
          position: "relative",
          height: "350px", // Fixed height container for the chart
          width: "100%",
        }}
      >
        <Line
          className="line-opportunities-chart"
          style={{ position: "relative", width: "100%", height: "100%" }}
          data={{
            labels: lineChartData?.map((d) => d.x),
            datasets: [
              {
                label: t("status.pending", "Pending"),
                backgroundColor: "rgba(128,128,128,0.2)",
                borderColor: "grey",
                data: lineChartData?.map((d) => d.y1.value),
                fill: false,
                tension: 0.4,
                pointRadius: 6,
                pointHoverRadius: 8,
              },
              {
                label: t("status.inProgress", "In Progress"),
                backgroundColor: "rgba(27, 71, 248, 0.2)",
                borderColor: "rgb(27, 71, 248)",
                data: lineChartData?.map((d) => d.y2.value),
                fill: false,
                tension: 0.4,
                pointRadius: 6,
                pointHoverRadius: 8,
              },
              {
                label: t("status.completed", "Completed"),
                backgroundColor: "rgba(0, 128, 0, 0.2)",
                borderColor: "green",
                data: lineChartData?.map((d) => d.y3.value),
                fill: false,
                tension: 0.4,
                pointRadius: 6,
                pointHoverRadius: 8,
              },
              {
                label: t("status.rejected", "Rejected"),
                backgroundColor: "rgba(255, 0, 0, 0.2)",
                borderColor: "red",
                data: lineChartData?.map((d) => d.y4.value),
                fill: false,
                tension: 0.4,
                pointRadius: 6,
                pointHoverRadius: 8,
              },
            ],
          }}
          options={{
            responsive: true,
            maintainAspectRatio: false,
            interaction: { mode: "index", intersect: false },
            plugins: {
              tooltip: {
                mode: "index",
                intersect: false,
                boxPadding: 8,
                callbacks: {
                  label: (ctx) => `${ctx.dataset.label}: ${ctx.raw}`,
                },
              },
              legend: {
                display: true,
                position: "top",
                labels: {
                  usePointStyle: true,
                  pointStyle: "circle",
                  padding: 15,
                  font: { size: 12 },
                  color: "white",
                },
              },
            },
            scales: {
              x: {
                beginAtZero: true,
                ticks: { color: "white" },
                grid: { color: "rgba(255,255,255,0.2)" },
              },
              y: {
                beginAtZero: true,
                ticks: {
                  callback: (v) => v,
                  color: "white",
                },
                grid: { color: "rgba(255,255,255,0.2)" },
              },
            },
          }}
        />
      </div>
    </>
  );
};

export default LeadsStatusLineChart;
