import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Tooltip as Chart<PERSON>ooltip,
  Legend,
} from "chart.js";
import { useTranslation } from "react-i18next";

ChartJS.register(CategoryScale, LinearScale, BarElement, ChartTooltip, Legend);

const SalesFunnelBarChart = ({ barChartData }) => {
  const { t } = useTranslation();

  return (
    <>
      <h2 className={"text-white"}>
        {t("dashboard.salesFunnel", "Leads Funnel")}
      </h2>
      <div
        style={{
          position: "relative",
          height: "350px", // Fixed height container
          width: "100%",
        }}
      >
        <Bar
          className="sales-bar-chart"
          style={{
            position: "relative",
            width: "100%",
            height: "100%", // Fill parent container
          }}
          data={{
            labels: barChartData?.map((datum) => datum.x),
            datasets: [
              {
                label: "Opportunities",
                data: barChartData?.map((datum) => datum.y),
                backgroundColor: "#BED754",
                borderColor: "#BED754",
                borderWidth: 1,
                barThickness: 20,
                borderRadius: 10,
              },
            ],
          }}
          options={{
            responsive: true,
            maintainAspectRatio: false, // Allow chart to fill container
            plugins: {
              tooltip: {
                boxPadding: 8,
                callbacks: {
                  label: function (context) {
                    const label = context.label || "";
                    const value = context.raw || 0;
                    return `Leads: ${value}`;
                  },
                },
                backgroundColor: "white",
                titleColor: "black",
                bodyColor: "black",
                borderColor: "#444444",
                borderWidth: 1,
                padding: 10,
                cornerRadius: 5,
                pointer: {
                  length: 8,
                },
              },
              legend: {
                display: false,
              },
            },
            scales: {
              x: {
                grid: {
                  display: false,
                },
                ticks: {
                  font: {
                    size: 12,
                    family: "Arial",
                    weight: "bold",
                  },
                  color: "#fff",
                },
              },
              y: {
                grid: {
                  color: "#fff",
                },
                ticks: {
                  callback: function (value) {
                    return value;
                  },
                  font: {
                    size: 12,
                    family: "Arial",
                    weight: "bold",
                  },
                  color: "#fff",
                },
                beginAtZero: true,
              },
            },
          }}
        />
      </div>
    </>
  );
};

export default SalesFunnelBarChart;
