import { useEffect, useState } from "react";
import { Accordion, Col, Form, Row } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import {
  setDynamicAdminRolesData,
  handleAdminSwitchChange,
  setSelectedPermissions,
  updateAdminRole,
} from "../../redux/features/adminRoleSlice";

const AdminRolesTab = ({ isMobileScreen, isCreateMode = false, formikProps }) => {
  const dispatch = useDispatch();
  const {
    allPermissions,
    dynamicAdminRolesData,
    selectedPermissions,
    roleId,
  } = useSelector((state) => state.adminRole);
  const [expandedPanels, setExpandedPanels] = useState([]);

  // Add the handleSubmit function
  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch(updateAdminRole({ roleId, selectedPermissions }));
  };

  // Handle permissions for both create and edit modes
  const handlePermissionChange = (permissionName) => {
    if (isCreateMode && formikProps) {
      const { values, setFieldValue } = formikProps;
      const currentPermissions = values.permissions || [];

      if (currentPermissions.includes(permissionName)) {
        // Remove permission if already selected
        setFieldValue(
          "permissions",
          currentPermissions.filter((name) => name !== permissionName)
        );
      } else {
        // Add permission if not selected
        setFieldValue("permissions", [...currentPermissions, permissionName]);
      }
    } else {
      // Use Redux for existing roles
      dispatch(handleAdminSwitchChange(permissionName));
    }
  };

  useEffect(() => {
    const generateDynamicAdminRolesData = () => {
      if (!allPermissions || allPermissions.length === 0) {
        return [];
      }

      // Group admin permissions by category (remove admin- prefix for grouping)
      const permissionGroups = {};
      
      allPermissions.forEach((permission) => {
        // Remove 'admin-' prefix and get the base permission name
        const baseName = permission.name.replace(/^admin-/, '');
        
        // Extract category from permission name (e.g., 'lead-edit' -> 'lead')
        const parts = baseName.split('-');
        const category = parts[0];
        
        if (!permissionGroups[category]) {
          permissionGroups[category] = [];
        }
        
        permissionGroups[category].push({
          id: permission.id,
          name: permission.name,
          displayName: baseName, // Show without admin- prefix
          disabled: false,
        });
      });

      // Convert to the expected format
      const dynamicData = Object.keys(permissionGroups).map((category, index) => ({
        key: category,
        label: category.charAt(0).toUpperCase() + category.slice(1), // Capitalize first letter
        content: permissionGroups[category],
      }));

      return dynamicData;
    };

    const dynamicData = generateDynamicAdminRolesData();
    dispatch(setDynamicAdminRolesData(dynamicData));
  }, [allPermissions, dispatch]);

  // Handle accordion panel toggle
  const handleAccordionToggle = (panelKey) => {
    setExpandedPanels((prev) => {
      if (prev.includes(panelKey)) {
        return prev.filter((key) => key !== panelKey);
      } else {
        return [...prev, panelKey];
      }
    });
  };

  // Check if permission is selected
  const isPermissionSelected = (permissionName) => {
    if (isCreateMode && formikProps) {
      return formikProps.values.permissions?.includes(permissionName) || false;
    }
    return selectedPermissions?.includes(permissionName) || false;
  };

  const handleCategorySelectAll = (categoryPermissions) => {
    const categoryPermissionNames = categoryPermissions
      .filter((permission) => !permission.disabled)
      .map((permission) => permission.name);

    if (isCreateMode) {
      const areAllSelected = categoryPermissionNames.every((permissionName) =>
        formikProps.values.permissions?.includes(permissionName)
      );
      
      if (areAllSelected) {
        // Remove all category permissions
        const newPermissions = formikProps.values.permissions?.filter(
          (perm) => !categoryPermissionNames.includes(perm)
        ) || [];
        formikProps.setFieldValue("permissions", newPermissions);
      } else {
        // Add all category permissions
        const currentPermissions = formikProps.values.permissions || [];
        const newPermissions = [
          ...currentPermissions.filter((perm) => !categoryPermissionNames.includes(perm)),
          ...categoryPermissionNames,
        ];
        formikProps.setFieldValue("permissions", newPermissions);
      }
    } else {
      const areAllSelected = categoryPermissionNames.every((permissionName) =>
        selectedPermissions?.includes(permissionName)
      );
      
      if (areAllSelected) {
        // Remove all category permissions
        const newPermissions = selectedPermissions?.filter(
          (perm) => !categoryPermissionNames.includes(perm)
        ) || [];
        dispatch(setSelectedPermissions(newPermissions));
      } else {
        // Add all category permissions
        const newPermissions = [
          ...selectedPermissions?.filter((perm) => !categoryPermissionNames.includes(perm)) || [],
          ...categoryPermissionNames,
        ];
        dispatch(setSelectedPermissions(newPermissions));
      }
    }
  };

  const handleGlobalSelectAll = () => {
    const allPermissionNames = dynamicAdminRolesData
      ?.flatMap((category) =>
        category.content
          .filter((permission) => !permission.disabled)
          .map((permission) => permission.name)
      )
      .filter(Boolean);

    if (isCreateMode) {
      const areAllSelected = allPermissionNames?.every((permissionName) =>
        formikProps.values.permissions?.includes(permissionName)
      );
      formikProps.setFieldValue(
        "permissions",
        areAllSelected ? [] : allPermissionNames
      );
    } else {
      const areAllSelected = allPermissionNames?.every((permissionName) =>
        selectedPermissions?.includes(permissionName)
      );
      dispatch(
        setSelectedPermissions(areAllSelected ? [] : allPermissionNames)
      );
    }
  };

  const isCategoryFullySelected = (categoryPermissions) => {
    const categoryPermissionNames = categoryPermissions
      .filter((permission) => !permission.disabled)
      .map((permission) => permission.name);

    if (isCreateMode && formikProps) {
      return categoryPermissionNames.every((permissionName) =>
        formikProps.values.permissions?.includes(permissionName)
      );
    }
    return categoryPermissionNames.every((permissionName) =>
      selectedPermissions?.includes(permissionName)
    );
  };

  const isGloballySelected = () => {
    const allPermissionNames = dynamicAdminRolesData
      ?.flatMap((category) =>
        category.content
          .filter((permission) => !permission.disabled)
          .map((permission) => permission.name)
      )
      .filter(Boolean);

    if (isCreateMode && formikProps) {
      return allPermissionNames?.every((permissionName) =>
        formikProps.values.permissions?.includes(permissionName)
      );
    }
    return allPermissionNames?.every((permissionName) =>
      selectedPermissions?.includes(permissionName)
    );
  };

  // Handle expand/collapse all
  const handleExpandCollapseAll = () => {
    if (expandedPanels.length === dynamicAdminRolesData?.length) {
      setExpandedPanels([]);
    } else {
      setExpandedPanels(dynamicAdminRolesData?.map((_, index) => String(index)) || []);
    }
  };

  return (
    <Form
      onSubmit={isCreateMode ? undefined : handleSubmit}
      id={isCreateMode ? undefined : "admin-role-form"}
    >
      <div className="global-controls mb-4 d-flex justify-content-end align-items-center gap-4">
        <Form.Check
          type="switch"
          id="admin-expand-collapse-all"
          label="Expand/Collapse All"
          checked={expandedPanels.length === dynamicAdminRolesData?.length}
          onChange={handleExpandCollapseAll}
          className="global-control-switch"
        />
        <Form.Check
          type="switch"
          id="admin-select-deselect-all"
          label="Select/Deselect All Permissions"
          checked={isGloballySelected()}
          onChange={handleGlobalSelectAll}
          className="global-control-switch"
        />
      </div>
      <Row className="g-4">
        {dynamicAdminRolesData?.map((category, index) => (
          <Col lg={4} md={6} sm={12} key={index}>
            <Accordion
              activeKey={expandedPanels}
              onSelect={() => handleAccordionToggle(String(index))}
            >
              <Accordion.Item
                eventKey={String(index)}
                className="permission-accordion"
              >
                <Accordion.Header>
                  <div className="d-flex justify-content-between align-items-center w-100">
                    <span className="fw-bold">{category.label}</span>
                    <div className="d-flex align-items-center">
                      <Form.Check
                        type="checkbox"
                        className="permission-select-all ms-2"
                        id={`admin-select-all-${category.key}`}
                        label="Select All"
                        checked={isCategoryFullySelected(category.content)}
                        onChange={() => handleCategorySelectAll(category.content)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>
                  </div>
                </Accordion.Header>
                <Accordion.Body>
                  {category.content.map((permission, idx) => (
                    <div key={idx} className="permission-item">
                      <Form.Group className="d-flex justify-content-between align-items-center">
                        <Form.Label className="mb-0">
                          {permission.displayName}
                        </Form.Label>
                        <Form.Check
                          type="switch"
                          className="permission-switch"
                          id={`admin-permission-switch-${permission.id}`}
                          onChange={
                            permission.disabled
                              ? null
                              : () => handlePermissionChange(permission.name)
                          }
                          checked={
                            permission.disabled ||
                            isPermissionSelected(permission.name)
                          }
                          disabled={permission.disabled}
                        />
                      </Form.Group>
                    </div>
                  ))}
                </Accordion.Body>
              </Accordion.Item>
            </Accordion>
          </Col>
        ))}
      </Row>
    </Form>
  );
};

export default AdminRolesTab;
