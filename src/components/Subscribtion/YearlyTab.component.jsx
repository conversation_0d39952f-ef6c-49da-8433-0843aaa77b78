import {<PERSON>, <PERSON>lapse, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>} from "react-bootstrap";
import {useReducer, useState, useRef} from "react";
import {useNavigate} from "react-router-dom";
import {toast} from "react-toastify";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useSwipe } from '../../hooks/useSwipe';
import { useIsMobile } from '../../utils/useIsMobile';
import './Subscription.css';
import { showErrorToast } from "../../utils/toast-success-error";

const TOGGLE_COLLAPSE = "TOGGLE_COLLAPSE";

const collapseReducer = (state, action) => {
    switch (action.type) {
        case TOGGLE_COLLAPSE:
            return {...state, [action.cardId]: !state[action.cardId]};
        default:
            return state;
    }
};

const YearlyTabComponent = ({packages, loading}) => {
    const { user } = useSelector((state) => state.auth);
    const [showDowngradeModal, setShowDowngradeModal] = useState(false);
    const [pendingDowngrade, setPendingDowngrade] = useState(null);
    const initialState = {
        card1: false, card2: true, card3: false,
    };
    const [collapseState, dispatch] = useReducer(collapseReducer, initialState);
    const navigate = useNavigate();
    const packagesContainerRef = useRef(null);
    const isMobile = useIsMobile();
    const { onTouchStart, onTouchMove, onTouchEnd } = useSwipe(packagesContainerRef);

    // Package feature descriptions
    const packageFeatures = {
        1: {
            subtitle: "Up to 3 team members.",
            description: "Basic access with daily limitations on leads and messaging.",
            keyFeatures: [
                "Lead source integrations",
                "4 automatic leads every day",
                "Upload excel for bulk leads import up to 50 per day",
                "Limited to 4 messages per day",

                "Instant Lead Alerts (app only)",
                "Reports for lead assignment only",
                "No marketing details access",
                "Access across mobile app & web version"
            ]
        },
        2: {
            subtitle: "Up to 20 team members.",
            description: "Unlimited pro features for salespeople, marketers, and growing teams.",
            keyFeatures: [
                "Everything in Free Forever, plus:",
                "Unlimited lead engagement & client management",
                "Unlimited email lead alerts & lead distribution",
                "Unlimited message, file, and page templates",
                "Unlimited view tracking on your sales content",
                "Comprehensive reports for team members lead assignment and sales performance",
                "Custom branding on your files & pages",
                "Bulk calling & messaging",
                "Custom fields, activity attachments & geolocation"
            ]
        },
        3: {
            subtitle: "Customizable team capacity.",
            description: "Unlock the full power of DVConnect for larger teams and agencies.",
            keyFeatures: [
                "Everything in DVConnect Pro, plus:",
                "Customizable team members capacity",
                "Advanced team management & permissions",
                "Subteam hierarchy",
                "Advanced analytics & team dashboard",
                "Custom branding of the DVConnect mobile app & web app",
                "Personalised onboarding, setup, and team training",
                "Dedicated account manager"
            ]
        }
    };

    const handleToggleCollapse = (cardId) => {
        dispatch({type: TOGGLE_COLLAPSE, cardId});
    };

    const handleConfirm = ({totalPrice, productName, packageId}) => {
        // Check if user is on Plus plan (id: 2) and trying to downgrade to Basic plan (id: 1)
        if (user?.user?.package_id === 2 && packageId === 1) {
            setPendingDowngrade({totalPrice, productName, packageId});
            setShowDowngradeModal(true);
            return;
        }

        try {
            navigate(`/package-purchase?packageId=${packageId}&total=${totalPrice}&productname=${encodeURIComponent(productName)}`);
        } catch (error) {
            showErrorToast(error.response?.data?.message || "An error occurred");
        }
    };

    const handleDowngradeConfirm = () => {
        if (pendingDowngrade) {
            try {
                navigate(`/package-purchase?packageId=${pendingDowngrade.packageId}&total=${pendingDowngrade.totalPrice}&productname=${encodeURIComponent(pendingDowngrade.productName)}`);
            } catch (error) {
                showErrorToast(error.response?.data?.message || "An error occurred");
            }
        }
        setShowDowngradeModal(false);
        setPendingDowngrade(null);
    };

    // Calculate yearly prices (12 months with 10% discount)
    const getYearlyPrice = (card) => {
        if (card.id === 2) { // Plus plan
            // Fixed price for Plus plan
            return "120"; // Fixed yearly price for Plus plan
        } else if (card.id === 3) { // Ultimate plan
            return null; // No price for Ultimate plan
        } else {
            // Regular yearly price (monthly price * 12 with 10% discount)
            return Math.round(parseFloat(card?.price) * 12 * 0.9).toString();
        }
    };

    return (
        <>
            <Row
                ref={packagesContainerRef}
                className={`align-items-start mt-3 content-container py-5 ${
                    isMobile ? 'mobile-packages-container' : 'justify-content-around'
                }`}
                onTouchStart={isMobile ? onTouchStart : undefined}
                onTouchMove={isMobile ? onTouchMove : undefined}
                onTouchEnd={isMobile ? onTouchEnd : undefined}
            >
                {loading ? <FetchingDataLoading/> : packages?.map((card) => (
                    <Col
                        lg={3}
                        md={6}
                        sm={12}
                        key={`card${card.id}`}
                        className={`pricing-card px-0 mb-2 shadow-lg ${isMobile ? 'mobile-pricing-card' : ''}`}
                        onMouseEnter={card.id !== 2 ? () => handleToggleCollapse(`card${card.id}`) : undefined}
                        onMouseLeave={card.id !== 2 ? () => handleToggleCollapse(`card${card.id}`) : undefined}
                    >
                        <div>
                            <div className={"p-4"}>
                                <p className={"fs-3 fw-bold"}>
                                    {getYearlyPrice(card) ? (
                                        <>
                                            {getYearlyPrice(card)} <span className={"text-muted fs-6"}>/year</span>
                                        </>
                                    ) : (
                                        <span>Custom Pricing</span>
                                    )}
                                </p>
                                {card.id === 2 && (
                                    <div className="discount-badge mb-2">
                                        <span className="original-price">$300</span>
                                        <span className="discount-text">60% OFF</span>
                                    </div>
                                )}
                                <p className={"fw-bold fs-5"}>{card?.title}</p>
                                <p className={"text-muted"}>{packageFeatures[card.id]?.subtitle}</p>
                                <hr style={{borderTop: "2px solid #ECECEC"}}/>
                                <p className={"mb-2 fs-6"}>{packageFeatures[card.id]?.description}</p>
                                <p className={"fw-bold mt-3 mb-2"}>Key Features:</p>
                                <ul
                                    style={{listStyle: "none", paddingLeft: "0", paddingRight: "0"}}
                                    className={"grey-suit feature-list"}
                                >
                                    {packageFeatures[card.id]?.keyFeatures.map((feature, index) => (
                                        <li key={index} className={"mb-2"}>
                                            <div className="d-flex">
                                                <span className="me-2 checkmark-icon">✓</span>
                                                <span>{feature}</span>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            <Collapse
                                in={collapseState[`card${card.id}`]}
                                className={`pricing-card${card.id}-collapse`}
                            >
                                <div id={`pricing-card${card.id}-collapse`} className={"pb-3"}>
                                    <div
                                        className={"d-flex align-items-center justify-content-center"}
                                    >
                                        <button
                                            className={`mt-3 outline-card${card.id}-btn`}
                                            onClick={() => {
                                                if (card.id === 3) {
                                                    // Redirect to external sales contact page for Ultimate plan
                                                    window.open('https://www.dvconnect.info/en/contact', '_blank');
                                                } else {
                                                    handleConfirm({
                                                        totalPrice: getYearlyPrice(card), productName: card?.title, packageId: card?.id,
                                                    });
                                                }
                                            }}
                                        >
                                            {card.id === 3 ? 'Contact Sales' : 'Choose plan'}
                                        </button>
                                    </div>
                                </div>
                            </Collapse>
                        </div>
                    </Col>
                ))}
            </Row>

            <Modal
                show={showDowngradeModal}
                onHide={() => setShowDowngradeModal(false)}
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>Warning: Plan Downgrade</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>You are about to downgrade from Plus plan to Basic plan. This will result in:</p>
                    <ul>
                        <li>Limited to 4 automatic leads per day</li>
                        <li>Limited to 4 messages per day</li>
                        <li>Loss of unlimited lead engagement</li>
                        <li>Loss of advanced reporting features</li>
                        <li>Limited team member capacity</li>
                    </ul>
                    <p>Are you sure you want to continue?</p>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowDowngradeModal(false)}>
                        Cancel
                    </Button>
                    <Button variant="danger" onClick={handleDowngradeConfirm}>
                        Confirm Downgrade
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default YearlyTabComponent;
