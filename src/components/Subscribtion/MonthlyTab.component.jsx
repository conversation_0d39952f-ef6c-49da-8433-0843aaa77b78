import {<PERSON>, <PERSON>lapse, <PERSON>, Mo<PERSON>, <PERSON><PERSON>} from "react-bootstrap";
import {useReducer, useState, useRef} from "react";
import {useNavigate} from "react-router-dom";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import { useSelector } from "react-redux";
import { useSwipe } from '../../hooks/useSwipe';
import { useIsMobile } from '../../utils/useIsMobile';
import './Subscription.css';
import { showErrorToast } from "../../utils/toast-success-error";

const TOGGLE_COLLAPSE = "TOGGLE_COLLAPSE";

const collapseReducer = (state, action) => {
    switch (action.type) {
        case TOGGLE_COLLAPSE:
            return {...state, [action.cardId]: !state[action.cardId]};
        default:
            return state;
    }
};

const MonthlyTabComponent = ({packages, loading}) => {
    const { user } = useSelector((state) => state.auth);
    const [showDowngradeModal, setShowDowngradeModal] = useState(false);
    const [pendingDowngrade, setPendingDowngrade] = useState(null);
    const initialState = {
        card1: false, card2: true, card3: false,
    };
    const [collapseState, dispatch] = useReducer(collapseReducer, initialState);
    const navigate = useNavigate();
    const packagesContainerRef = useRef(null);
    const isMobile = useIsMobile();
    const { onTouchStart, onTouchMove, onTouchEnd } = useSwipe(packagesContainerRef);

    // Package feature descriptions
    const packageFeatures = {
        1: {
            subtitle: "Up to 3 team members.",
            description: "Basic access with daily limitations on leads and messaging.",
            keyFeatures: [
                "Unlimited lead source integrations",
                "Only 4 automatic leads per day",
                "Unlimited manual leads",
                "Upload bulk of leads up to 50 leads per month",
                "Add 3 team members per month",
                "Only sending 3 messages per day",
                "Display only 5 activities of day (agenda)",
                "No Marketing statistics available",
                "Available reports for lead assignment",
                "No marketing details access"
            ]
        },
        2: {
            subtitle: "Up to 20 team members.",
            description: "Unlimited pro features for salespeople, marketers, and growing teams.",
            keyFeatures: [
                "Unlimited lead source integrations",
                "Unlimited automatic retrieve leads",
                "Unlimited manual leads",
                "Upload bulk of leads with no limitations",
                "Add up to 20 team members per month",
                "Send and receive unlimited messages",
                "Show all activities of day (agenda)",
                "Keep track of all marketing statistics",
                "Follow your team progress via team member reports",
                "Follow your lead conversion via lead assignment reports",
                "Follow your sales performance via sales performance reports",
            ]
        },
        3: {
            subtitle: "Customizable team capacity.",
            description: "Unlock the full power of DVConnect for larger teams and agencies.",
            keyFeatures: [
                "Unlimited lead source integrations",
                "Unlimited automatic retrieve leads",
                "Unlimited manual leads",
                "Upload bulk of leads with no limitations",
                "Add customizable team members capacity",
                "Send and receive unlimited messages",
                "Show all activities of day (agenda)",
                "Keep track of all marketing statistics",
                "Follow your team progress via team member reports",
                "Follow your lead conversion via lead assignment reports",
                "Follow your sales performance via sales performance reports",
                "Customize new features for your system"
            ]
        }
    };

    const handleToggleCollapse = (cardId) => {
        dispatch({type: TOGGLE_COLLAPSE, cardId});
    };

    const handleConfirm = ({totalPrice, productName, packageId}) => {
        // Check if user is on Plus plan (id: 2) and trying to downgrade to Basic plan (id: 1)
        if (user?.user?.package_id === 2 && packageId === 1) {
            setPendingDowngrade({totalPrice, productName, packageId});
            setShowDowngradeModal(true);
            return;
        }

        try {
            navigate(`/package-purchase?packageId=${packageId}&total=${totalPrice}&productname=${encodeURIComponent(productName)}`);
        } catch (error) {
            showErrorToast(error.response?.data?.message || "An error occurred");
          }
      };

    const handleDowngradeConfirm = () => {
        if (pendingDowngrade) {
            try {
                navigate(`/package-purchase?packageId=${pendingDowngrade.packageId}&total=${pendingDowngrade.totalPrice}&productname=${encodeURIComponent(pendingDowngrade.productName)}`);
            } catch (error) {
                showErrorToast(error.response?.data?.message || "An error occurred");
              }
          }
        setShowDowngradeModal(false);
        setPendingDowngrade(null);
    };

    return (
        <>
            <Row
                ref={packagesContainerRef}
                className={`align-items-start mt-3 py-5 ${
                    isMobile ? 'mobile-packages-container' : 'justify-content-around'
                }`}
                onTouchStart={isMobile ? onTouchStart : undefined}
                onTouchMove={isMobile ? onTouchMove : undefined}
                onTouchEnd={isMobile ? onTouchEnd : undefined}
            >
                {loading ? <FetchingDataLoading/> : packages?.map((card) => (
                    <Col
                        lg={3}
                        md={6}
                        sm={12}
                        key={`card${card.id}`}
                        className={`pricing-card px-0 mb-2 shadow-lg ${isMobile ? 'mobile-pricing-card' : ''}`}
                        onMouseEnter={card.id !== 2 ? () => handleToggleCollapse(`card${card.id}`) : undefined}
                        onMouseLeave={card.id !== 2 ? () => handleToggleCollapse(`card${card.id}`) : undefined}
                    >
                        <div>
                            <div className={"p-4"}>
                                <p className={"fs-3 fw-bold"}>
                                    {card.id === 3 ? <span>Custom Pricing</span> : card.id === 2 ? "150" : card?.price} {card.id !== 3 && <span className={"text-muted fs-6"}>/month</span>}
                                </p>
                                {card.id === 2 && (
                                    <div className="discount-badge mb-2">
                                        <span className="original-price">$300</span>
                                        <span className="discount-text">50% OFF</span>
                                    </div>
                                )}
                                <p className={"fw-bold fs-5"}>{card?.title}</p>
                                <p className={"text-muted"}>{packageFeatures[card.id]?.subtitle}</p>
                                <hr style={{borderTop: "2px solid #ECECEC"}}/>
                                <p className={"mb-2 fs-6"}>{packageFeatures[card.id]?.description}</p>
                                <p className={"fw-bold mt-3 mb-2"}>Key Features:</p>
                                <ul
                                    style={{listStyle: "none", paddingLeft: "0", paddingRight: "0"}}
                                    className={"grey-suit feature-list"}
                                >
                                    {packageFeatures[card.id]?.keyFeatures.map((feature, index) => (
                                        <li key={index} className={"mb-2"}>
                                            <div className="d-flex">
                                                <span className="me-2 checkmark-icon">✓</span>
                                                <span>{feature}</span>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            <Collapse
                                in={collapseState[`card${card.id}`]}
                                className={`pricing-card${card.id}-collapse`}
                            >
                                <div id={`pricing-card${card.id}-collapse`} className={"pb-3"}>
                                    <div
                                        className={"d-flex align-items-center justify-content-center"}
                                    >
                                        <button
                                            className={`mt-3 outline-card${card.id}-btn`}
                                            onClick={() => {
                                                if (card.id === 3) {
                                                    window.open('https://www.dvconnect.info/en/contact', '_blank');
                                                } else {
                                                    handleConfirm({
                                                        totalPrice: card.id === 2 ? "150" : card?.price,
                                                        productName: card?.title,
                                                        packageId: card?.id,
                                                    });
                                                }
                                            }}
                                        >
                                            {card.id === 3 ? 'Contact Sales' : 'Choose plan'}
                                        </button>
                                    </div>
                                </div>
                            </Collapse>
                        </div>
                    </Col>
                ))}
            </Row>

            <Modal
                show={showDowngradeModal}
                onHide={() => setShowDowngradeModal(false)}
                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>Warning: Plan Downgrade</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>You are about to downgrade from Plus plan to Basic plan. This will result in:</p>
                    <ul>
                        <li>Limited to 4 automatic leads per day</li>
                        <li>Limited to 4 messages per day</li>
                        <li>Loss of unlimited lead engagement</li>
                        <li>Loss of advanced reporting features</li>
                        <li>Limited team member capacity</li>
                    </ul>
                    <p>Are you sure you want to continue?</p>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowDowngradeModal(false)}>
                        Cancel
                    </Button>
                    <Button variant="danger" onClick={handleDowngradeConfirm}>
                        Confirm Downgrade
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default MonthlyTabComponent;
