import { Col, Row } from "react-bootstrap";
import { FaCircleXmark } from "react-icons/fa6";
import { HiArrowDownTray } from "react-icons/hi2";
import { SiGoogleforms } from "react-icons/si";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import useIntegration from "../../hooks/useIntegration";

const IntegrationPagesTikTok = () => {
  const {
    tiktokPages,
    loadingPages,
    selectedAdvertiser,
    handleDownloadLeadsTiktok,
    advertisers,
  } = useIntegration();
  return advertisers?.length !== 0 && selectedAdvertiser ? (
    <>
      <Row
        className={
          "mt-4 justify-content-evenly connected-pages column-gap-1 p-2"
        }
        id={"formsSection"}
      >
        {loadingPages ? (
          <FetchingDataLoading />
        ) : tiktokPages?.length === 0 ? (
          <h5 className={"text-center mainColor my-4 py-4"}>
            There are no forms here, try selecting another page
          </h5>
        ) : (
          <>
            <h5 className={"text-center fw-bold mt-4"}>Forms For Each Page</h5>
            <div className={"green-label"}>
              ({tiktokPages?.length}) lead forms found
            </div>
            {tiktokPages?.map((tiktokPage, index) => (
              <Col
                title={"Get Leads Form"}
                role={"button"}
                xl={4}
                md={4}
                sm={6}
                xs={12}
                className={
                  "d-flex justify-content-between flex-column connected-page-content px-0 my-4"
                }
                style={{
                  border:
                    tiktokPage?.status === "PUBLISHED"
                      ? "3px solid rgb(146, 192, 32)"
                      : "3px solid rgb(255, 0, 0)",
                }}
                key={tiktokPage?.id}
              >
                <div
                  className={`${
                    tiktokPage?.status === "PUBLISHED"
                      ? "mainColor"
                      : "text-danger"
                  } pt-2`}
                >
                  <SiGoogleforms
                    size={25}
                    color={`${
                      tiktokPage?.status === "PUBLISHED"
                        ? "rgb(165, 211, 52)"
                        : "rgb(255, 0, 0)"
                    } `}
                  />{" "}
                  Page {index + 1}
                </div>
                <div className={"fs-5 fw-bold px-2"}>{tiktokPage?.title}</div>
                <div
                  className={"form-icon-import py-2"}
                  style={{
                    backgroundColor: `${
                      tiktokPage?.status === "PUBLISHED"
                        ? "rgb(165, 211, 52)"
                        : "rgb(255, 0, 0)"
                    }`,
                  }}
                >
                  {tiktokPage?.status === "PUBLISHED" ? (
                    <HiArrowDownTray
                      color={"#FFF"}
                      role={"button"}
                      size={40}
                      title={"Get Leads Form"}
                      onClick={() =>
                        handleDownloadLeadsTiktok(
                          selectedAdvertiser?.advertiser_id,
                          tiktokPage?.page_id
                        )
                      }
                    />
                  ) : (
                    <FaCircleXmark color={"#FFFFFF"} size={35} />
                  )}
                </div>
              </Col>
            ))}
          </>
        )}
      </Row>
    </>
  ) : (
    advertisers?.length !== 0 && (
      <div className={"content-container mt-4"}>
        <h5 className={"text-center mainColor my-4"}>
          Select a page to get forms and start importing leads
        </h5>
      </div>
    )
  );
};

export default IntegrationPagesTikTok;
