/* Comments Modal Backdrop */
.comments-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Comments Modal Container - Made bigger for chat-like experience */
.comments-modal-container {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 800px; /* Increased from 600px */
  max-height: 85vh; /* Increased from 80vh */
  display: flex;
  flex-direction: column;
  outline: none;
  animation: modalSlideIn 0.3s ease-out;
}

/* Modal Animation */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Comments Modal Content */
.comments-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* Comments Modal Header - Enhanced for chat-like experience */
.comments-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 2px solid #e9ecef;
  background: linear-gradient(263deg, #92C020 -9.91%, #CAD511 128.31%);
    border-radius: 16px 16px 0 0;
  flex-shrink: 0;
  position: relative;
}

.comments-modal-title {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.comments-modal-subtitle {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  margin-top: 4px;
}

.comments-modal-close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 1.2rem;
  color: white;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  backdrop-filter: blur(10px);
}

.comments-modal-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.comments-modal-close-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Comments Modal Body */
.comments-modal-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

/* Comments Container - Scrollable with chat-like styling */
.comments-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
  min-height: 300px;
  max-height: calc(85vh - 220px);
  background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
  scroll-behavior: smooth;
}

.comments-list {
  display: flex;
  flex-direction: column;
}

.comments-placeholder,
.comments-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  text-align: center;
  padding: 40px 20px;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state-title {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #495057;
}

.empty-state-subtitle {
  margin: 0;
  font-size: 0.9rem;
  color: #6c757d;
  max-width: 300px;
  line-height: 1.4;
}

.comments-loading p {
  margin: 0;
  font-size: 1rem;
  color: #6c757d;
}

/* Enhanced Loading State with Skeleton */
.comments-loading-skeleton {
  padding: 20px 0;
}

.comment-skeleton {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 0;
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

.comment-skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonShimmer 1.5s infinite;
}

.comment-skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.comment-skeleton-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-skeleton-name {
  width: 120px;
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeletonShimmer 1.5s infinite;
}

.comment-skeleton-time {
  width: 60px;
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeletonShimmer 1.5s infinite;
}

.comment-skeleton-text {
  width: 80%;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeletonShimmer 1.5s infinite;
}

.comment-skeleton-text.short {
  width: 60%;
}

@keyframes skeletonShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes skeletonPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Loading Spinner Enhancement */
.comments-loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  min-height: 150px;
}

.comments-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.comments-loading-text {
  color: #6c757d;
  font-size: 14px;
  margin: 0;
}

/* Comment Input Container - Fixed at bottom with enhanced styling */
.comment-input-container {
  border-top: 2px solid #e9ecef;
  background: linear-gradient(to right, #f8f9fa 0%, #ffffff 100%);
  padding: 20px 32px;
  border-radius: 0 0 16px 16px;
  flex-shrink: 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.comment-input-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.comment-input-placeholder p {
  margin: 0;
  font-size: 0.9rem;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .comments-modal-backdrop {
    padding: 10px;
  }

  .comments-modal-container {
    max-width: 100%;
    max-height: 90vh;
    border-radius: 12px;
  }

  .comments-modal-header {
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
  }

  .comments-modal-title {
    font-size: 1.2rem;
  }

  .comments-modal-subtitle {
    font-size: 0.8rem;
  }

  .comments-container {
    padding: 20px 24px;
    max-height: calc(90vh - 200px);
  }

  .comment-input-container {
    padding: 16px 24px;
    border-radius: 0 0 12px 12px;
  }

  .empty-state-icon {
    font-size: 2.5rem;
  }

  .empty-state-title {
    font-size: 1.1rem;
  }

  .empty-state-subtitle {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .comments-modal-backdrop {
    padding: 5px;
  }

  .comments-modal-container {
    border-radius: 8px;
    max-height: 95vh;
  }

  .comments-modal-header {
    padding: 12px 16px;
    border-radius: 8px 8px 0 0;
  }

  .comments-modal-title {
    font-size: 1rem;
  }

  .comments-container {
    padding: 12px 16px;
    max-height: calc(95vh - 160px);
  }

  .comment-input-container {
    padding: 10px 16px;
    border-radius: 0 0 8px 8px;
  }
}

/* Scrollbar Styling */
.comments-container::-webkit-scrollbar {
  width: 6px;
}

.comments-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.comments-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.comments-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus trap styling */
.comments-modal-container:focus {
  outline: none;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .comments-modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .comments-modal-container {
    background-color: #2d3748;
    color: #e2e8f0;
  }

  .comments-modal-header {
    background-color: #4a5568;
    border-bottom-color: #4a5568;
  }

  .comments-modal-title {
    color: white;
  }

  .comments-modal-close-btn {
    color: #a0aec0;
  }

  .comments-modal-close-btn:hover {
    background-color: #4a5568;
    color: #e2e8f0;
  }

  .comment-input-container {
    background-color: #4a5568;
    border-top-color: #4a5568;
  }

  .comments-loading-text,
  .comments-placeholder p {
    color: #a0aec0;
  }

  .comments-spinner {
    border-color: #4a5568;
    border-top-color: #4dabf7;
  }

  .comment-skeleton-avatar,
  .comment-skeleton-name,
  .comment-skeleton-time,
  .comment-skeleton-text {
    background: linear-gradient(90deg, #4a5568 25%, #2d3748 50%, #4a5568 75%);
    background-size: 200% 100%;
  }
}

/* Enhanced error states */
.comments-modal-error {
  padding: 16px 24px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  margin: 16px 24px;
  color: #721c24;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.comments-modal-error.dark {
  background-color: #2d1b1b;
  border-color: #5a2d2d;
  color: #f56565;
}

/* Connection status indicator */
.comments-connection-status {
  position: absolute;
  top: 8px;
  right: 50px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
}

.connection-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #28a745;
  animation: connectionPulse 2s infinite;
}

.connection-dot.disconnected {
  background-color: #dc3545;
  animation: none;
}

@keyframes connectionPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .comments-modal-container {
    animation: none;
  }

  .comment-skeleton,
  .comment-skeleton-avatar,
  .comment-skeleton-name,
  .comment-skeleton-time,
  .comment-skeleton-text {
    animation: none;
  }

  .comments-spinner {
    animation: none;
  }

  .connection-dot {
    animation: none;
  }

  .comments-modal-error {
    animation: none;
  }
}

.comment-item:hover .read-receipts-icon {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
}
