import { HiOutlineUsers } from "react-icons/hi";
import { RiListSettingsLine } from "react-icons/ri";
import { BiCheckShield } from "react-icons/bi";
import { PiOfficeChairBold, PiSquaresFourFill } from "react-icons/pi";
import "./Sidebar.css";
import { Dropdown, Nav } from "react-bootstrap";
import { FiSettings } from "react-icons/fi";
import { NavLink } from "react-router-dom";
import { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { GrDocumentPerformance } from "react-icons/gr";
import { LuFileKey2 } from "react-icons/lu";
import { IoPricetags } from "react-icons/io5";
import { useSelector } from "react-redux";
import { FaMeta, FaUserClock, FaBullhorn } from "react-icons/fa6";
import { BsThreeDots } from "react-icons/bs";
import { GiHamburgerMenu } from "react-icons/gi";
import {
  TbLayoutSidebarLeftCollapse,
  TbLayoutSidebarLeftExpand,
} from "react-icons/tb";
import { Tooltip } from "react-tooltip";
import { MdOutlineRocketLaunch, MdSupportAgent } from "react-icons/md"; // Added MdSupportAgent
import LanguageSwitcher from "../../LanguageSwitcher";
import { useTranslation } from "react-i18next";
import { LiaFileInvoiceDollarSolid } from "react-icons/lia";
import { FaChevronDown, FaChartLine } from "react-icons/fa";
import { Fragment } from "react";
import { shouldShowPackageFeatures } from "../../../config/packageVisibility";

const SidebarComponent = ({
  setIsExpanded,
  iconSize,
  mobileSidebar,
  isExpanded,
}) => {
  const { currentUserPermissions, user } = useSelector((state) => state.auth);
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === "ar";
  const menuRef = useRef(null);
  const [isHoverSupported, setIsHoverSupported] = useState(true);
  const [tooltipVisible, setTooltipVisible] = useState({});
  const [visibleMenuItems, setVisibleMenuItems] = useState([]);
  const [hiddenMenuItems, setHiddenMenuItems] = useState([]);
  const [leadsOpen, setLeadsOpen] = useState(false);

  const handleTouchStart = useCallback((index) => {
    // Show the tooltip when the user touches the item
    setTooltipVisible((prevState) => ({ ...prevState, [index]: true }));

    // Hide the tooltip after 2 seconds
    setTimeout(() => {
      setTooltipVisible((prevState) => ({ ...prevState, [index]: false }));
    }, 2000);
  }, []);

  const toggleSidebar = () => {
    setIsExpanded((prevState) => !prevState);
  };

  // Handle hover detection
  useEffect(() => {
    const mediaQuery = window.matchMedia("(hover: hover) and (pointer: fine)");
    setIsHoverSupported(mediaQuery.matches);

    const handleChange = () => setIsHoverSupported(mediaQuery.matches);
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  // Create menu items with memoization to prevent unnecessary recalculations
  const mainMenuItems = useMemo(
    () =>
      [
        {
          id: 1,
          icon: <PiSquaresFourFill size={iconSize} />,
          label: t("sidebar.dashboard"),
          link: "",
        },
        {
          id: 2,
          icon: <HiOutlineUsers size={iconSize} />,
          label: t("sidebar.leads"),
          link: "leads",
        },
        {
          id: 3,
          icon: <PiOfficeChairBold size={iconSize} />,
          label: t("sidebar.team"),
          link: "team",
          permission: "member",
        },
        {
          id: 4,
          icon: <RiListSettingsLine size={iconSize} />,
          label: t("sidebar.integrations"),
          link: "integrations",
          permission: "integration",
          accessbaleForTM: true,
        },
        {
          id: 5,
          icon: <FaMeta size={iconSize} />,
          label: t("sidebar.metaBusiness"),
          link: "meta-business-suite",
        },
        {
          id: 6,
          icon: <FaBullhorn size={iconSize} />,
          label: t("sidebar.ads"),
          link: "ads",
          accessbaleForTM: true,
        },
        {
          id: 7,
          icon: <FaChartLine size={iconSize} />,
          label: t("sidebar.campaigns"),
          link: "campaigns",
          accessbaleForTM: true,
        },
        {
          id: 8,
          icon: <GrDocumentPerformance size={iconSize} />,
          label: t("sidebar.reports"),
          link: "reports",
          permission: "report",
          accessbaleForTM: true,
        },
        {
          id: 9,
          icon: <LuFileKey2 size={iconSize} />,
          label: t("sidebar.rolesPermissions"),
          link: "permissions",
          permission: "role",
          accessbaleForTM: true,
        },
        {
          id: 10,
          icon: <MdOutlineRocketLaunch size={iconSize} />,
          label: t("sidebar.marketing"),
          link: "marketing",
          permission: "marketing",
          accessbaleForTM: true,
        },
        {
          id: 11,
          icon: <IoPricetags size={iconSize} />,
          label: t("sidebar.packages"),
          link: "packages",
          accessbaleForTM: true,
        },
        {
          id: 12,
          icon: <LiaFileInvoiceDollarSolid size={iconSize} />,
          label: t("sidebar.subscription"),
          link: "invoices-and-subscriptions",
          accessbaleForTM: true,
        },
        {
          id: 13, // Adjusted ID to be unique
          icon: <MdSupportAgent size={iconSize} />,
          label: t("sidebar.support"),
          link: "support",
          accessbaleForTM: true, // Assuming support is accessible to team members
        },
      ]
        .filter((item) => {
          if (item.accessbaleForTM === false && user?.user?.parent_id) {
            return false;
          }

          if (item.permission) {
            const permissions = [
              "list",
              "create",
              "edit",
              "delete",
              "config",
              "restore",
              "export",
              "config",
              "import",
              "export",
            ];
            return permissions.some(
              (permission) =>
                currentUserPermissions.includes(
                  `${item.permission}-${permission}`
                ) || user?.user?.role === "Owner"
            );
          }
          return true;
        })
        // Filter out package-related menu items for excluded users
        .filter((item) => {
          // Check if this is a package-related menu item (IDs 10 and 11)
          if (item.id === 11 || item.id === 10) {
            // Only show if user should see package features
            return shouldShowPackageFeatures(user?.user?.id);
          }
          return true;
        })
        // Remove the default leads item – handled separately by collapsible menu
        .filter((item) => item.link !== "leads"),
    [iconSize, t, currentUserPermissions, user]
  );

  // Define Leads parent item for mobile footer navigation
  const leadsParentMobileItem = useMemo(
    () => ({
      id: 998, // unique id for mobile menu
      icon: <HiOutlineUsers size={iconSize} />,
      label: t("sidebar.leads"),
      link: "leads",
    }),
    [iconSize, t]
  );

  // Define No Communication Leads item for mobile footer
  const noCommMobileItem = useMemo(
    () => ({
      id: 999,
      icon: <FaUserClock size={iconSize} />,
      label: t("sidebar.followUpLeads"),
      link: "no-communication-leads",
    }),
    [iconSize, t]
  );

  const sideBarFooterItems = [
    {
      id: 12, // Adjusted ID due to new main menu item
      icon: <BiCheckShield size={iconSize} />,
      label: t("sidebar.privacyPolicy"),
      link: "privacy",
    },
    {
      id: 13, // Adjusted ID
      icon: <BiCheckShield size={iconSize} />,
      label: t("sidebar.termsConditions"),
      link: "terms",
    },
    {
      id: 14, // Adjusted ID
      icon: <FiSettings size={iconSize} />,
      label: t("sidebar.accountSettings"),
      link: "account-settings",
    },
  ];

  // Handle icons layout with memoized callback
  const handleIcons = useCallback(() => {
    if (!menuRef.current) return;
    const containerWidth = menuRef?.current?.offsetWidth;
    const itemWidth = 90;
    const visibleItemsCount = Math.floor(containerWidth / itemWidth);

    // Base items list depending on direction
    let items = isRTL ? [...mainMenuItems].reverse() : [...mainMenuItems];
    items.splice(1, 0, leadsParentMobileItem, noCommMobileItem);

    setVisibleMenuItems(items.slice(0, visibleItemsCount));
    setHiddenMenuItems(items.slice(visibleItemsCount));
  }, [mainMenuItems, isRTL, leadsParentMobileItem, noCommMobileItem]);

  // Handle initial setup and resize
  useEffect(() => {
    // Add a small delay to ensure DOM is fully rendered
    const timer = setTimeout(() => {
      handleIcons();
    }, 100);

    window.addEventListener("resize", handleIcons);

    return () => {
      window.removeEventListener("resize", handleIcons);
      clearTimeout(timer);
    };
  }, [handleIcons]);

  // Handle language changes and mobile sidebar rendering
  useEffect(() => {
    handleIcons();
  }, [i18n.language, handleIcons, mobileSidebar]);

  // Force recalculation when mobile sidebar is active
  useEffect(() => {
    if (mobileSidebar && menuRef.current) {
      // Use the memoized handleIcons function instead of duplicating logic
      handleIcons();
      const timer = setTimeout(handleIcons, 300);

      return () => clearTimeout(timer);
    }
  }, [mobileSidebar, handleIcons]);

  // Update Dropdown positioning based on RTL
  const dropdownProps = {
    drop: "up",
    align: isRTL ? "end" : "start",
  };

  return mobileSidebar ? (
    <div
      className={`mobileFooter ${isRTL ? "rtl" : ""}`}
      ref={menuRef}
      // Add this to force layout calculation when the component mounts
      onLoad={() => handleIcons()}
    >
      {visibleMenuItems.length > 0
        ? // Render visible items only if they exist
          visibleMenuItems.map((item) => (
            <div key={item?.id}>
              <Nav.Link
                as={NavLink}
                to={`/${item.link}`}
                className="menu-item-mobile"
                id={`navlink-${item?.id}`}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(item?.id)
                }
              >
                <div className="menu-icon-label-container">
                  <div className="icon-container">{item?.icon}</div>
                  <div className="label-container text-center">
                    {item?.label}
                  </div>
                </div>
              </Nav.Link>
              {!isExpanded && (
                <Tooltip
                  anchorSelect={`#navlink-${item?.id}`}
                  content={item?.label}
                  className="bg-dark text-white"
                  isOpen={
                    isHoverSupported ? undefined : tooltipVisible[item?.id]
                  }
                  place={isRTL ? "right" : "left"}
                />
              )}
            </div>
          ))
        : // Fallback if no visible items are calculated yet
          mainMenuItems.slice(0, 4).map((item) => (
            <div key={item?.id}>
              <Nav.Link
                as={NavLink}
                to={`/${item.link}`}
                className="menu-item-mobile"
                id={`navlink-${item?.id}`}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(item?.id)
                }
              >
                <div className="menu-icon-label-container">
                  <div className="icon-container">{item?.icon}</div>
                  <div className="label-container text-center">
                    {item?.label}
                  </div>
                </div>
              </Nav.Link>
            </div>
          ))}

      <Dropdown {...dropdownProps}>
        <Dropdown.Toggle id="dropdown-autoclose-true" variant="none">
          <GiHamburgerMenu size={iconSize} />
        </Dropdown.Toggle>

        <Dropdown.Menu
          className={`mobile-dropdown-menu ${isRTL ? "dropdown-menu-rtl" : ""}`}
        >
          {hiddenMenuItems.map((item) => (
            <Dropdown.Item
              key={item?.id}
              as={NavLink}
              to={`/${item.link}`}
              className="dropdown-item"
              id={`dropdown-navlink-${item?.id}`}
              onTouchStart={() =>
                !isHoverSupported && handleTouchStart(item?.id)
              }
            >
              <div className="menu-icon-label-container">
                <div>{item.icon}</div>
                <div className="label-container">{item?.label}</div>
              </div>
              {!isExpanded && (
                <Tooltip
                  anchorSelect={`#dropdown-navlink-${item?.id}`}
                  content={item.label}
                  className="bg-dark text-white"
                  isOpen={
                    isHoverSupported ? undefined : tooltipVisible[item?.id]
                  }
                  place={isRTL ? "right" : "left"}
                />
              )}
            </Dropdown.Item>
          ))}
          {sideBarFooterItems.map((item) => (
            <Dropdown.Item
              key={item?.id}
              as={NavLink}
              to={`/${item.link}`}
              className="dropdown-item"
              id={`dropdown-footer-${item?.id}`}
              onTouchStart={() =>
                !isHoverSupported && handleTouchStart(item?.id)
              }
            >
              <div className="menu-icon-label-container">
                <div>{item.icon}</div>
                <div className="label-container">{item?.label}</div>
              </div>
              {!isExpanded && (
                <Tooltip
                  anchorSelect={`#dropdown-footer-${item?.id}`}
                  content={item.label}
                  className="bg-dark text-white"
                  isOpen={
                    isHoverSupported ? undefined : tooltipVisible[item?.id]
                  }
                  place={isRTL ? "right" : "left"}
                />
              )}
            </Dropdown.Item>
          ))}
          <Dropdown.Item>
            <div className="menu-icon-label-container">
              <LanguageSwitcher />
            </div>
          </Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
    </div>
  ) : (
    <div className={`sidebarBS ${isExpanded ? "expanded" : ""}`}>
      {/* Desktop sidebar content reuses existing structure from earlier version unchanged */}
      {/* For brevity, replicate main menu mapping with leads group injection */}
      <div className={"text-center sidebar-top-section position-relative"}>
        {isExpanded ? (
          <h2 className={"main-menu"}>{t("sidebar.mainMenu")}</h2>
        ) : (
          <div className={"d-flex flex-column align-items-center"}>
            <TbLayoutSidebarLeftExpand
              role={"button"}
              onClick={toggleSidebar}
              size={30}
            />
            <BsThreeDots className={"my-3"} />
          </div>
        )}
        {isExpanded ? (
          <TbLayoutSidebarLeftCollapse
            role={"button"}
            onClick={toggleSidebar}
            size={30}
            className={"collapse-sidebar-icon"}
          />
        ) : null}
      </div>
      <div className="sidebar-content sidebar-nav-container">
        <Nav className="flex-column align-items-center" variant={"pills"}>
          {/* main menu items mapping with leads group injected after dashboard */}
          {mainMenuItems.map((item, index) => (
            <Fragment key={`desk-${index}`}>
              <Nav.Item>
                <Nav.Link
                  as={NavLink}
                  to={`/${item.link}`}
                  className="menu-item py-1"
                  id={`desk-navlink-${index}`}
                >
                  <div className="text-center">{item.icon}</div>
                  <div className="menu-item_label ms-3">{item.label}</div>
                  {isExpanded ? null : (
                    <Tooltip
                      anchorSelect={`#desk-navlink-${index}`}
                      content={item.label}
                      className={"bg-dark text-white"}
                      openOnClick={true}
                    />
                  )}
                </Nav.Link>
              </Nav.Item>

              {item.link === "" && (
                <Fragment key="desk-leads-group">
                  <Nav.Item>
                    <div
                      className="menu-item my-1 d-flex align-items-center lead-toggle"
                      onClick={() => setLeadsOpen((prev) => !prev)}
                      role="button"
                      id="desk-navlink-leads-parent"
                    >
                      <div className="text-center">
                        <HiOutlineUsers size={iconSize} />
                      </div>
                      {isExpanded && (
                        <div className="menu-item_label ms-3 flex-grow-1">
                          {t("sidebar.leads")}
                        </div>
                      )}
                      <FaChevronDown
                        className={`ms-auto ${leadsOpen ? "rotate-180" : ""}`}
                        size={14}
                      />
                    </div>
                    {!isExpanded && (
                      <Tooltip
                        anchorSelect="#desk-navlink-leads-parent"
                        content={t("sidebar.leads")}
                        className="bg-dark text-white"
                        openOnClick={true}
                      />
                    )}
                  </Nav.Item>
                  {leadsOpen && (
                    <>
                      <Nav.Item className="submenu-indent">
                        <Nav.Link
                          as={NavLink}
                          to="/leads"
                          className="menu-item my-1 py-2"
                          id="submenu-all-leads"
                        >
                          <div className="text-center">
                            <HiOutlineUsers size={iconSize - 2} />
                          </div>
                          {isExpanded && (
                            <div className="menu-item_label ms-2">
                              {t("sidebar.allLeads")}
                            </div>
                          )}
                          {!isExpanded && (
                            <Tooltip
                              anchorSelect="#submenu-all-leads"
                              content={t("sidebar.allLeads")}
                              className="bg-dark text-white"
                              openOnClick={true}
                            />
                          )}
                        </Nav.Link>
                      </Nav.Item>
                      <Nav.Item className="submenu-indent">
                        <Nav.Link
                          as={NavLink}
                          to="/no-communication-leads"
                          className="menu-item my-1 py-2"
                          id="submenu-no-comm"
                        >
                          <div className="text-center">
                            <FaUserClock size={iconSize - 2} />
                          </div>
                          {isExpanded && (
                            <div className="menu-item_label ms-2">
                              {t("sidebar.followUpLeads")}
                            </div>
                          )}
                          {!isExpanded && (
                            <Tooltip
                              anchorSelect="#submenu-no-comm"
                              content={t("sidebar.followUpLeads")}
                              className="bg-dark text-white"
                              openOnClick={true}
                            />
                          )}
                        </Nav.Link>
                      </Nav.Item>
                    </>
                  )}
                </Fragment>
              )}
            </Fragment>
          ))}
          <Nav.Item className="menu-item" role={"button"}>
            <LanguageSwitcher />
          </Nav.Item>
        </Nav>

        {/* Useful links footer */}
        <div className={"mt-3"}>
          <div className={"text-center sidebar-top-section"}>
            {isExpanded ? (
              <h2 className={"main-menu"}>{t("sidebar.usefulLinks")}</h2>
            ) : (
              <BsThreeDots className={"my-3"} />
            )}
          </div>
          <Nav className="flex-column" variant={"pills"}>
            {sideBarFooterItems.map((item, index) => (
              <Nav.Item key={`footer-${index}`}>
                <Nav.Link
                  as={NavLink}
                  to={`/${item.link}`}
                  className="menu-item py-1"
                  id={`footer-navlink-${index}`}
                >
                  <div className="text-center">{item.icon}</div>
                  <div className="menu-item_label ms-3">{item.label}</div>
                  {isExpanded ? null : (
                    <Tooltip
                      anchorSelect={`#footer-navlink-${index}`}
                      content={item.label}
                      className={"bg-dark text-white"}
                      openOnClick={true}
                    />
                  )}
                </Nav.Link>
              </Nav.Item>
            ))}
          </Nav>
        </div>
      </div>
    </div>
  );
};

export default SidebarComponent;
