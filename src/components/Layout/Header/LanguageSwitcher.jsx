import React from 'react';
import { useTranslation } from 'react-i18next';
import { Dropdown } from 'react-bootstrap';
import { IoLanguage } from 'react-icons/io5';

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  return (
    <Dropdown>
      <Dropdown.Toggle
        variant="link"
        id="language-dropdown"
        className="text-white d-flex align-items-center"
        style={{ textDecoration: 'none' }}
      >
        <IoLanguage size={20} className="me-1" />
        {i18n.language === 'ar' ? 'العربية' : 'English'}
      </Dropdown.Toggle>

      <Dropdown.Menu>
        <Dropdown.Item
          onClick={() => changeLanguage('en')}
          active={i18n.language === 'en'}
        >
          English
        </Dropdown.Item>
        <Dropdown.Item
          onClick={() => changeLanguage('ar')}
          active={i18n.language === 'ar'}
        >
          العربية
        </Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default LanguageSwitcher;
