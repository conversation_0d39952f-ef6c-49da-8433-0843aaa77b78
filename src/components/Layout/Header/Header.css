.header-container {
    top: 0;
    width: 100%;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    background: #000;
    padding: 10px 20px;
    z-index: 100;
    position: sticky;
}

.search-box {
    width: 25%;
}

.search-box_input input {
    border-radius: 28px;
    background: #FFF;
}

.profile-picture {
    width: 2.5rem;
    border-radius: 28px;
    border: 2px solid #92C020;
}

.search-box_input {
    position: relative;
}

.search-box_input .search-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50px;
    background: linear-gradient(252deg, #92C020 6.99%, #CAD511 87.97%);
    padding: 6px;
}

.profile-dropdown-container .dropdown-toggle, .profile-dropdown-container .btn.show, .profile-dropdown-container .btn:hover {
    background-color: unset;
    border: unset;
}

.profile-dropdown-button.dropdown-toggle:after, .profile-dropdown-button.dropend .dropdown-toggle::after {
    display: none !important;
}

.profile-dropdown-container .profile-dropdown-menu {
    transform: translate(0%, 15%) !important;
    border-radius: 18px;
    background: #FFF;
    box-shadow: 0 4px 48px 0 rgba(0, 0, 0, 0.10);
    min-width: 150px;
    text-align: center;
}

.profile-dropdown-menu p {
    color: #000;
    font-size: 1rem;
    font-weight: 700;
    margin: 5px 0;
}

.pricing-button {
    color: white;
    border: unset;
    padding: 3px 10px;
    border-radius: 29px;
    background: linear-gradient(259deg, #92C020 -4.26%, #CAD511 101.68%);
    font-size: 0.75rem;
    font-weight: 700;
}

.profile-picture-dropdown {
    width: 3.625rem;
    border-radius: 50%;
    border: 2px solid #92C020;
}

.new-client-btn {
    border-radius: 25px;
    background: linear-gradient(265deg, #92C020 -23.12%, #CAD511 103.2%);
    border: unset;
    width: fit-content;
}

.new-client-btn.dropdown-toggle:after {
    display: none;
}

.notification-dropdown {
    color: white;
    position: relative;
}

.notification-dropdown button {
    background-color: transparent;
    border: unset;
    padding: 0;
}

.notification-dropdown button:after, .notification-dropdown.dropstart .dropdown-toggle::before {
    display: none;
}

.notification-dropdown button:hover, .notification-dropdown button:focus {
    background-color: transparent;
    border: unset;
}

/*.notification-dropdown:after {*/
/*    content: ".\2022";*/
/*    position: absolute;*/
/*    top: 0;*/
/*    right: 0;*/
/*    background: linear-gradient(#92C020, #CAD511);*/
/*    border-radius: 50%;*/
/*    width: 50%;*/
/*    font-size: 8px;*/
/*}*/

.notification-dropdown .dropdown-toggle .notification-badge {
    position: absolute;
    top: -20%;
    right: -20%;
    width: 17px;
    height: 17px;
    font-size: 10px !important;
    background: linear-gradient(#92C020, #CAD511);
    padding: 5px 0 0;
}

/* Notifications modal styles */
.notifications-modal .modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.notifications-modal .modal-dialog {
    width: 450px;
    max-width: 90vw;
    margin: 1rem auto;
}

.notifications-modal .modal-body {
    max-height: 80vh;
    overflow-y: auto;
    padding: 0;
}

/* Notification items styling */
.notification-item {
    padding: 0.875rem;
    border-bottom: 1px solid #eee;
    font-size: 0.95rem;
}

.notification-item:last-child {
    border-bottom: none;
}

/* Empty state styling */
.empty-placeholder {
    padding: 1.5rem;
    text-align: center;
}

/* Notification badge */
.notification-dropdown .notification-badge {
    position: absolute;
    top: -20%;
    right: -20%;
    width: 17px;
    height: 17px;
    font-size: 10px !important;
    background: linear-gradient(#92C020, #CAD511);
    padding: 5px 0 0;
}

/* Bell icon container */
.notification-dropdown {
    position: relative;
    display: inline-flex;
    align-items: center;
}

/* Mobile styles */
@media (max-width: 576px) {
    .notifications-modal .modal-dialog {
        width: 340px;
        margin: 0.5rem auto;
    }
    
    .notifications-modal .modal-body {
        max-height: 70vh;
    }
}

/* Add backdrop */
.notification-dropdown.show::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1040;
}

/* Notification items styling */
.notification-item {
    padding: 0.875rem;
    border-bottom: 1px solid #eee;
    font-size: 0.95rem;
}

.notification-item:last-child {
    border-bottom: none;
}

/* Empty state styling */
.empty-placeholder {
    padding: 1.5rem;
    text-align: center;
}

/* Notification badge */
.notification-dropdown .dropdown-toggle .notification-badge {
    position: absolute;
    top: -20%;
    right: -20%;
    width: 17px;
    height: 17px;
    font-size: 10px !important;
    background: linear-gradient(#92C020, #CAD511);
    padding: 5px 0 0;
}

/* Ensure the notification badge stays in position */
.notification-dropdown .dropdown-toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
}

.notification-item.dropdown-item .spark-icon {
    color: #92C020;
}

.notification-item.dropdown-item:hover .spark-icon {
    color: #FFFFFF;
}

.empty-placeholder {
    min-width: 300px;
    margin: 20px;
}

@media only screen and (max-width: 768px) {
    .search-box {
        width: 40%;
    }
}

.notification-item.dropdown-item:focus, .notification-item.dropdown-item:hover {
    color: var(--bs-dropdown-link-hover-color);
    background-color: var(--bs-dropdown-link-hover-bg);
}

.greenDot {
    padding: 0 0 0 0.5rem;
    display: inline-block;
    line-height: 0;
}

.greenDot:after {
    content: "•";
    font-size: 2rem;
    line-height: 1;
    display: inline-block;
    vertical-align: middle;
}

/* Add these styles for the sticky header */
.notifications-modal .modal-body {
    padding: 0 !important;
    max-height: 80vh;
    overflow-y: auto;
}

.notifications-header {
    position: sticky;
    top: 0;
    background: white;
    z-index: 1020;
    border-bottom: 1px solid #eee;
    padding: 1rem;
}

/* Add shadow effect when scrolling */
.notifications-header.shadow {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notifications-content {
    padding: 1rem;
}

/* Dark mode support */
.dark .notifications-header {
    background: #242424;
    border-bottom-color: #444;
}

/* RTL support */
[dir="rtl"] .notifications-header {
    left: 0;
    right: 0;
}
