import DataTableFollowUpsComponent from "./DataTableFollowUps.component";
import {useMemo} from "react";
import clientPic from "../../../assets/media/Icons/Ellipse2.svg";
import clientPic2 from "../../../assets/media/Icons/Ellipse4.svg";
import clientPic3 from "../../../assets/media/Icons/Ellipse4.svg";
import clientPic4 from "../../../assets/media/Icons/Ellipse5.svg";
import clientPic5 from "../../../assets/media/Icons/Ellipse6.svg";
import clientPic6 from "../../../assets/media/Icons/Ellipse7.svg";
import {ReactSVG} from "react-svg";

const FollowUpsComponent = () => {
    const columns = useMemo(() => [
        {
            Header: ' ',
            Cell: ({ row }) => (
                <ReactSVG src={row.original.profilePic} />
            ),
        },
        {
            Header: "Follow Up", accessor: "follow_up",
        }, {
            Header: "Name", accessor: "contactName",
        }, {
            Header: "Details", accessor: "details",
        }], []);

    const data = useMemo(
        () => [
            {
                id: 1,
                follow_up: "Follow Up 1",
                contactName: "John Doe",
                details: "Details 1",
                profilePic: clientPic,
                time: "2023-11-10",
            },
            {
                id: 2,
                follow_up: "Follow Up 2",
                contactName: "Jane Smith",
                details: "Details 2",
                profilePic: clientPic2,
                time: "2023-11-03",
            },
            {
                id: 3,
                follow_up: "Follow Up 3",
                contactName: "Tom Johnson",
                details: "Details 3",
                profilePic: clientPic3,
                time: "2023-11-15",
            },
            {
                id: 4,
                follow_up: "Follow Up 4",
                contactName: "Sara Lee",
                details: "Details 4",
                profilePic: clientPic4,
                time: "2023-11-10",
            },
            {
                id: 5,
                follow_up: "Follow Up 5",
                contactName: "Mark Davis",
                details: "Details 5",
                profilePic: clientPic5,
                time: "2023-11-10",
            },
            {
                id: 6,
                follow_up: "Follow Up 6",
                contactName: "Lisa Brown",
                details: "Details 6",
                profilePic: clientPic6,
                time: "2023-12-03",
            },
        ],
        []
    );

    return (<DataTableFollowUpsComponent data={data} columns={columns} />);
};

export default FollowUpsComponent;
