import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ReactSVG } from "react-svg";
import { format } from "date-fns";
import { MdAssignmentTurnedIn, MdEdit } from "react-icons/md";
import { Tooltip } from "react-tooltip";
import { PiTrashFill } from "react-icons/pi";
import { FaCalendar, FaEnvelope, FaPhone, FaUserPlus } from "react-icons/fa6";
import { FaUserCog } from "react-icons/fa";
import { Dropdown } from "react-bootstrap";
import { Link } from "react-router-dom";
import DataTableComponent from "../../CustomDataTable/DataTable.component";
import { sourceToIcon } from "../../../constants/sourceIcons";
import getAllTeamMembers from "../../../services/teams/get-teams.api";
import CenteredModal from "../../Shared/modals/CenteredModal/CenteredModal";
import DeleteLeadFromTable from "../../Modals/DeleteLeadFromTable";
import {
  setTeamMembers,
  setDisableAddMember,
  handleAssignTeamMemberThunk,
} from "../../../redux/features/clientSlice";
import { showErrorToast } from "../../../utils/toast-success-error";
import { useTranslation } from "react-i18next";

const FilterTableNoCommLeadsComponent = ({ loading, data, hideNoData }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );
  const { currentUserPermissions } = useSelector((state) => state.auth);
  const user = useSelector((state) => state.auth.user);

  const [teamMembers, setLocalTeamMembers] = useState([]);
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState(null);

  // Tooltip helpers
  const [isHoverSupported, setIsHoverSupported] = useState(true);
  const [tooltipVisible, setTooltipVisible] = useState({});

  const handleTouchStart = (index) => {
    setTooltipVisible((prevState) => ({ ...prevState, [index]: true }));
    setTimeout(() => {
      setTooltipVisible((prevState) => ({ ...prevState, [index]: false }));
    }, 2000);
  };

  const handleDelete = (id) => {
    setSelectedLeadId(id);
    setShowCenteredModal(true);
  };

  // Fetch team members once
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const teamsData = await getAllTeamMembers();
        const members = teamsData?.data?.members || [];
        setLocalTeamMembers(members);
        dispatch(setTeamMembers(members));
        dispatch(setDisableAddMember(teamsData?.data?.quota === 0));
      } catch (error) {
        showErrorToast(
          error.response?.data?.message || t("messages.fetchTeamError")
        );
      }
    };
    fetchTeamMembers();

    const mediaQuery = window.matchMedia("(hover: hover) and (pointer: fine)");
    setIsHoverSupported(mediaQuery.matches);
    const handleChange = () => setIsHoverSupported(mediaQuery.matches);
    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [dispatch, t]);

  // Data transformation
  const tableData = useMemo(() => {
    return (Array.isArray(data) ? data : []).map((lead, index) => ({
      id: lead?.id,
      clientId: index + 1 + (currentPage - 1) * recordsPerPage,
      contactName: lead?.name,
      email: lead?.email,
      phone: lead?.phone,
      source: lead?.source,
      service: lead?.service,
      pageName: lead?.page_name,
      logs: lead?.latestActivity || lead?.activities,
      assignedTo: lead?.assignedTo || lead?.assigned_to?.name,
      createdAt: lead?.date || lead?.created_at,
      updatedAt: lead?.updated_at,
      status: lead?.status,
    }));
  }, [data, currentPage, recordsPerPage]);

  const filteredTeamMembers = useMemo(
    () => teamMembers?.filter((member) => member.status !== "deactive"),
    [teamMembers]
  );

  const isChild = user?.user?.parent_id != null;
  const initialSortBy = useMemo(
    () => (isChild ? [{ id: "updatedAt", desc: true }] : []),
    [isChild]
  );
  const hiddenColumns = useMemo(() => ["clientId", "updatedAt"], []);

  const columns = useMemo(
    () => [
      // Hidden ID column (clientId) no longer displayed
      {
        id: "clientId",
        accessor: "clientId",
        Header: "ID",
        Cell: () => null,
      },
      // Hidden updatedAt column for sorting
      {
        id: "updatedAt",
        accessor: "updatedAt",
        Header: "UpdatedAt",
        Cell: () => null,
      },
      {
        Header: t("leadsTable.columns.contactName"),
        accessor: "contactName",
        Cell: ({ row }) => {
          const name = row.original.contactName;
          return (
            <>
              <Link
                to={`/leads/${row.original.id}`}
                className={`d-flex align-items-center gap-1 lead-name${row.original.id} justify-content-center`}
                style={{ maxWidth: "200px" }}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(row.original.id)
                }
              >
                <span className="one-line">{name}</span>
              </Link>
              <Tooltip
                anchorSelect={`.lead-name${row.original.id}`}
                content={name}
                className="bg-dark text-white"
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
              />
            </>
          );
        },
      },
      {
        Header: t("leadsTable.columns.phone"),
        accessor: "phone",
        Cell: ({ row }) => {
          const phone = row.original.phone;
          return (
            <>
              <Link
                to={`/leads/${row.original.id}`}
                className={`d-flex align-items-center gap-1 lead-phone${row.original.id} justify-content-center`}
                style={{ maxWidth: "200px" }}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(row.original.id)
                }
              >
                <span className="one-line">{phone}</span>
              </Link>
              <Tooltip
                anchorSelect={`.lead-phone${row.original.id}`}
                content={phone}
                className="bg-dark text-white"
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
              />
            </>
          );
        },
      },
      {
        Header: t("leadsTable.columns.assignedTo"),
        accessor: "assignedTo",
        Cell: ({ row }) => {
          const assignedTo = row.original.assignedTo;
          return assignedTo ? (
            <>
              <Link
                to={`/leads/${row.original.id}`}
                className={`d-flex align-items-center gap-1 lead-assignedTo${row.original.id} justify-content-center`}
                style={{ maxWidth: "200px" }}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(row.original.id)
                }
              >
                <span className="one-line">{assignedTo}</span>
              </Link>
              <Tooltip
                anchorSelect={`.lead-assignedTo${row.original.id}`}
                content={assignedTo}
                className="bg-dark text-white"
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
              />
            </>
          ) : (
            <Dropdown>
              <Dropdown.Toggle
                variant="light"
                id="dropdown-basic"
                className={"team-actions-button p-0 rounded-3"}
              >
                <div className={"assign-client-icon m-0"}>
                  <FaUserPlus size={20} />
                </div>
              </Dropdown.Toggle>
              <Dropdown.Menu className={"team-actions-menu"} container="body">
                {filteredTeamMembers?.length > 0 ? (
                  filteredTeamMembers?.map((member) => (
                    <Dropdown.Item
                      key={member.id}
                      className={
                        "d-flex justify-content-between align-items-center text-secondary"
                      }
                      onClick={() =>
                        dispatch(
                          handleAssignTeamMemberThunk({
                            leadId: row.original.id,
                            memberId: member.id,
                          })
                        )
                      }
                    >
                      {member.name}
                    </Dropdown.Item>
                  ))
                ) : (
                  <Dropdown.Item disabled>
                    {t("leadsTable.columns.noTeamMembers")}
                  </Dropdown.Item>
                )}
              </Dropdown.Menu>
            </Dropdown>
          );
        },
      },
      // Only show reassign column for admin users (users without parent_id)
      ...(user?.user && !user.user.parent_id
        ? [
            {
              Header: t("leadsTable.columns.reassign"),
              accessor: "reassign",
              Cell: ({ row }) => {
                const assignedTo = row.original.assignedTo;

                if (!assignedTo) {
                  return "-";
                }

                if (!currentUserPermissions?.includes("lead-edit")) {
                  return assignedTo;
                }

                const availableMembers = filteredTeamMembers?.filter(
                  (m) => m.name !== assignedTo
                );

                return (
                  <Dropdown>
                    <Dropdown.Toggle
                      variant="light"
                      id="reassign-dropdown"
                      className={"team-actions-button p-0 rounded-3"}
                    >
                      <div className={"assign-client-icon m-0"}>
                        <FaUserCog size={20} />
                      </div>
                    </Dropdown.Toggle>
                    <Dropdown.Menu
                      className={"team-actions-menu"}
                      container="body"
                    >
                      {availableMembers && availableMembers.length > 0 ? (
                        availableMembers.map((member) => (
                          <Dropdown.Item
                            key={member.id}
                            className={
                              "d-flex justify-content-between align-items-center text-secondary"
                            }
                            onClick={() =>
                              dispatch(
                                handleAssignTeamMemberThunk({
                                  leadId: row.original.id,
                                  memberId: member.id,
                                })
                              )
                            }
                          >
                            {member.name}
                          </Dropdown.Item>
                        ))
                      ) : (
                        <Dropdown.Item disabled>
                          {t("leadsTable.columns.noTeamMembers")}
                        </Dropdown.Item>
                      )}
                    </Dropdown.Menu>
                  </Dropdown>
                );
              },
            },
          ]
        : []),
      {
        Header: t("leadsTable.columns.source"),
        accessor: "source",
        Cell: ({ row }) => {
          const src = row.original.source;
          const IconComponent = sourceToIcon[src];
          return src ? (
            <Link
              to={`/leads/${row.original.id}`}
              className={"mx-auto social-icon-container"}
            >
              {IconComponent && <ReactSVG src={IconComponent} />}
            </Link>
          ) : (
            "-"
          );
        },
      },
      {
        Header: t("leadsTable.columns.status"),
        accessor: "status",
        Cell: ({ row }) => {
          const status = row?.original?.status;
          const statusMapping = {
            0: {
              label: t("status.pending"),
              className: "status-badge--pending",
            },
            1: {
              label: t("status.inProgress"),
              className: "status-badge--in-progress",
            },
            2: {
              label: t("status.completed"),
              className: "status-badge--completed",
            },
            3: {
              label: t("status.rejected"),
              className: "status-badge--rejected",
            },
            4: {
              label: t("status.wrongLead"),
              className: "status-badge--wrong-lead",
            },
            5: {
              label: t("status.notQualified"),
              className: "status-badge--not-qualified",
            },
            6: {
              label: t("status.noCommunication"),
              className: "status-badge--no-communication",
            },
            7: { label: t("status.booked"), className: "status-badge--booked" },
            8: {
              label: t("status.bookedReserved"),
              className: "status-badge--booked-reserved",
            },
            9: {
              label: t("status.canceled"),
              className: "status-badge--canceled",
            },
            10: {
              label: t("status.quotation"),
              className: "status-badge--quotation-sent",
            },
            11: {
              label: t("status.assigned"),
              className: "status-badge--in-progress",
            },
          };

          const { label, className } = statusMapping[status] || {
            label: "Unknown",
            className: "status-badge--unknown",
          };

          return (
            <Link
              to={`/leads/${row.original.id}`}
              className={`status-badge ${className} rounded-pill p-1`}
              style={{ fontSize: "0.875rem", fontWeight: "600" }}
            >
              {label}
            </Link>
          );
        },
      },
      {
        Header: t("leadsTable.columns.pageName"),
        accessor: "pageName",
        Cell: ({ row }) => {
          const pageName = row.original.pageName;
          return (
            <>
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line page-name${row.original.id} mx-auto`}
                style={{ maxWidth: "150px" }}
                onTouchStart={() =>
                  !isHoverSupported && handleTouchStart(row.original.id)
                }
              >
                {pageName}
              </Link>
              <Tooltip
                anchorSelect={`.page-name${row.original.id}`}
                content={pageName}
                className={"bg-dark text-white"}
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
              />
            </>
          );
        },
      },
      {
        Header: t("leadsTable.columns.lastActivity"),
        accessor: "lastActivity",
        Cell: ({ row }) => {
          const tooltipId = `lastActivity_${row.id}`;
          const logs = Array.isArray(row.original.logs)
            ? row.original.logs.slice(-3)
            : [row.original.logs];
          const lastLog = logs[0];
          if (!lastLog) return null;
          return (
            <Link
              to={`/leads/${row.original.id}`}
              data-for={tooltipId}
              data-tooltip-id={tooltipId}
              onTouchStart={() =>
                !isHoverSupported && handleTouchStart(row.original.id)
              }
            >
              <div className={"activity-button"}>
                {lastLog?.action === 1 && <MdAssignmentTurnedIn size={20} />}
                {lastLog?.action === 2 && <FaPhone size={20} />}
                {lastLog?.action === 3 && <FaCalendar size={20} />}
                {lastLog?.action === 4 && <FaEnvelope size={20} />}
              </div>
              <Tooltip
                id={tooltipId}
                isOpen={
                  isHoverSupported ? undefined : tooltipVisible[row.original.id]
                }
                className={"logs-tooltip-container"}
                content={
                  <div className={"logs-list-container"}>
                    {logs?.map((log, index) => (
                      <div key={index} className={"log-container"}>
                        <div className={"d-flex justify-content-center my-2"}>
                          {log?.action === 1 && (
                            <MdAssignmentTurnedIn
                              size={20}
                              className={"mainColor"}
                            />
                          )}
                          {log?.action === 2 && (
                            <FaPhone size={20} className={"mainColor"} />
                          )}
                          {log?.action === 3 && (
                            <FaCalendar size={20} className={"mainColor"} />
                          )}
                          {log?.action === 4 && (
                            <FaEnvelope size={20} className={"mainColor"} />
                          )}
                        </div>
                        <p className={"opacity-50"}>
                          {log?.result || log?.note}
                        </p>
                      </div>
                    ))}
                  </div>
                }
                place={"left-start"}
                events={["hover"]}
              />
            </Link>
          );
        },
      },
      {
        Header: t("leadsTable.columns.createdAt"),
        accessor: "createdAt",
        Cell: ({ value, row }) => {
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              const formattedDate = format(parsedDate, "yyyy-MM-dd HH:mm:ss");
              return (
                <Link to={`/leads/${row.original.id}`}>{formattedDate}</Link>
              );
            }
          }
          return <Link to={`/leads/${row.original.id}`}>{value}</Link>;
        },
      },
      {
        Header: t("leadsTable.columns.actions"),
        Cell: ({ row }) => (
          <div className={"d-flex justify-content-center"}>
            {currentUserPermissions?.includes("lead-edit") && (
              <Link
                to={`/leads/${row.original.id}`}
                className={"me-3 shadow-sm rounded-2 p-1"}
              >
                <MdEdit size={20} className={"text-dark"} />
              </Link>
            )}
            {currentUserPermissions?.includes("lead-delete") && (
              <div className={"shadow-sm rounded-2 p-1"}>
                <PiTrashFill
                  onClick={() => handleDelete(row.original.id)}
                  size={20}
                  className={"text-danger"}
                />
              </div>
            )}
          </div>
        ),
      },
    ],
    [
      teamMembers,
      t,
      filteredTeamMembers,
      isHoverSupported,
      tooltipVisible,
      currentUserPermissions,
      dispatch,
      user,
    ]
  );

  return (
    <div className="content-container p-4">
      <DataTableComponent
        columns={columns}
        data={tableData}
        initialSortBy={initialSortBy}
        hiddenColumns={hiddenColumns}
        loading={loading}
        hideNoData={hideNoData}
      />
      <CenteredModal
        show={showCenteredModal}
        children={
          <DeleteLeadFromTable
            leadId={selectedLeadId}
            setShowCenteredModal={setShowCenteredModal}
          />
        }
        onHide={() => {
          setShowCenteredModal(false);
          setSelectedLeadId(null);
        }}
      />
    </div>
  );
};

export default FilterTableNoCommLeadsComponent;
