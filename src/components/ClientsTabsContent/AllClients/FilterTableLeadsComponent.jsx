import React, { useEffect, useMemo, useState } from "react";
import DataTableComponent from "../../CustomDataTable/DataTable.component";
import { useSelector, useDispatch } from "react-redux";
import { useTranslatedColumns } from "../../Reports/ColumnsForTables.module";
import CenteredModal from "../../Shared/modals/CenteredModal/CenteredModal";
import DeleteLeadFromTable from "../../Modals/DeleteLeadFromTable";
import getAllTeamMembers from "../../../services/teams/get-teams.api";
import {
  setTeamMembers,
  setDisableAddMember,
  handleAssignTeamMemberThunk,
} from "../../../redux/features/clientSlice";
import { useTranslation } from "react-i18next";
import { showErrorToast } from "../../../utils/toast-success-error";

function FilterTableLeadsComponent({
  abort<PERSON>ontroller,
  setAbortController,
  handleFilterStatus,
  loading,
  handleFilterLeads,
  applyFilters,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [teamMembers, setLocalTeamMembers] = useState([]);
  const [leadStatusCounts, setLeadStatusCounts] = useState({});
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );
  const { filterStatus, selectedSource } = useSelector((state) => state.client);
  const leads = useSelector((state) => state.client.leads);
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState(null);
  const { currentUserPermissions } = useSelector((state) => state.auth);
  const filteredTeamMembers = teamMembers?.filter(
    (teamMember) => teamMember.status !== "deactive"
  );

  const data = useMemo(() => {
    return (Array.isArray(leads) ? leads : []).map((lead, index) => ({
      id: lead?.id,
      contactName: lead?.name,
      email: lead?.email,
      source: lead?.source,
      phone: lead?.phone,
      createdBy: lead?.created_by,
      logs: lead?.latestActivity || lead?.activities,
      assignedTo: lead?.assignedTo || lead?.assigned_to?.name,
      createdAt: lead?.date || lead?.created_at,
      updatedAt: lead?.updated_at,
      status: lead?.status,
      pageName: lead?.page_name,
      formName: lead?.form_name,
      clientId: index + 1,
      service: lead?.service,
    }));
  }, [leads]);

  const [isHoverSupported, setIsHoverSupported] = useState(true);
  const [tooltipVisible, setTooltipVisible] = useState({}); // Manage which tooltip is visible

  // Get current user
  const user = useSelector((state) => state.auth.user);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const teamsData = await getAllTeamMembers();
        const members = teamsData?.data?.members || [];
        setLocalTeamMembers(members);
        dispatch(setTeamMembers(members));
        dispatch(setDisableAddMember(teamsData?.data?.quota === 0));
      } catch (error) {
        showErrorToast(
          error.response?.data?.message || t("messages.fetchTeamError")
        );
      }
    };
    fetchTeamMembers();
  }, [dispatch, t]);

  useEffect(() => {
    // Delegate abort-controller handling to applyFilters to avoid creating multiple controllers per fetch
    applyFilters(filterStatus, selectedSource);

    // Detect hover capability (unrelated to data fetching)
    const mediaQuery = window.matchMedia("(hover: hover) and (pointer: fine)");
    setIsHoverSupported(mediaQuery.matches);

    const handleChange = () => setIsHoverSupported(mediaQuery.matches);
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [currentPage, recordsPerPage, filterStatus, selectedSource]);

  const handleTouchStart = (index) => {
    // Show the tooltip when the user touches the item
    setTooltipVisible((prevState) => ({ ...prevState, [index]: true }));

    // Hide the tooltip after 2 seconds (or any duration)
    setTimeout(() => {
      setTooltipVisible((prevState) => ({ ...prevState, [index]: false }));
    }, 2000); // 2 seconds delay
  };

  const handleDelete = (id) => {
    setSelectedLeadId(id);
    setShowCenteredModal(true);
  };

  // Shared columns from ColumnsForTables.module.js
  const { filterTableLeadsColumns } = useTranslatedColumns();

  const columns = useMemo(
    () =>
      filterTableLeadsColumns({
        filteredTeamMembers,
        currentUserPermissions,
        dispatch,
        handleAssignTeamMemberThunk,
        handleDelete,
        isHoverSupported,
        handleTouchStart,
        tooltipVisible,
        user,
      }),
    [
      filteredTeamMembers,
      currentUserPermissions,
      dispatch,
      handleAssignTeamMemberThunk,
      handleDelete,
      isHoverSupported,
      handleTouchStart,
      tooltipVisible,
    ]
  );

  // Determine default sort: non-admin (has parent_id) => sort updatedAt desc
  const initialSortBy = [];

  const hiddenColumns = useMemo(() => ["updatedAt", "clientId"], []);

  return (
    <div className="content-container p-4">
      <DataTableComponent
        columns={columns}
        data={data}
        initialSortBy={initialSortBy}
        hiddenColumns={hiddenColumns}
        handleFilterStatus={handleFilterStatus}
        leadStatusCounts={leadStatusCounts}
        loading={loading}
        handleFilterLeads={handleFilterLeads}
        setLeadStatusCounts={setLeadStatusCounts}
      />
      <CenteredModal
        show={showCenteredModal}
        children={
          <DeleteLeadFromTable
            leadId={selectedLeadId}
            setShowCenteredModal={setShowCenteredModal}
          />
        }
        onHide={() => {
          setShowCenteredModal(false);
          setSelectedLeadId(null);
        }}
      />
    </div>
  );
}

export default FilterTableLeadsComponent;
