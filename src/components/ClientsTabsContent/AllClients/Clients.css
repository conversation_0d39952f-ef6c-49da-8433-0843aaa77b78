.leads-tabs-navs {
    border-radius: 50px;
    background: #FFF;
    box-shadow: 0 4px 22px 0 rgba(0, 0, 0, 0.10);
    width: fit-content;
}

.leads-tabs-navs.nav-pills .nav-link {
    border-radius: 50px;
    color: #000;
    font-size: 16px;
    font-weight: 400;
    padding: 17px 32px;
}

.leads-tabs-navs.nav-pills .nav-link.active {
    background: #000;
    color: #FFF;
    font-weight: 900;
    transition: ease-in-out all 0.25s;
}

.all-leads-table {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    margin-top: 1rem;
    margin-bottom: 1rem;
}


.leads-table-navs .nav-link.active, .btn-link.active {
    color: #92C020;
    font-family: Kumbh Sans, sans-serif;
    font-weight: 400;
}

.leads-table-navs .nav-link, .btn-link {
    color: #000;
    font-size: 0.8rem;
    font-weight: 300;
}

.all-leads-table .search-input {
    border-radius: 39px;
    border: 1px solid #DFDFDF;
    background: #FAFAFA;
}

.all-leads-table .search-icon {
    position: absolute;
    right: 2%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.admin-theme .leads-table-navs .nav-link.active, .all-leads-table .btn-link.active, .all-leads-table .custom-button.active {
    color: #92C020;
    background: rgba(146, 192, 32, 0.08);
    border: 1px solid;
}

.all-leads-table .btn-group .custom-button {
    font-size: 0.8rem;
    font-weight: 500;
    line-height: 12px;
    letter-spacing: 0;
    text-align: left;
    text-transform: capitalize;
    text-decoration: none;
    border-radius: 50px;
    @media only screen and (max-width: 768px) {
       border: 1px solid;
    }
}

/* Common Styles for Button */
.status-button {
    border-radius: 39px;
    padding: 8px 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease-in-out;
    user-select: none;
    background: transparent;
}

[dir="rtl"] .status-button {
    gap: 4px;
}

/* Default State */
.status-button-default {
    background: rgba(0, 0, 0, 0.05); /* Light placeholder */
    border-color: transparent;
    color: inherit; /* Ensures dynamic text color */
}

/* Active State */
.status-button-active {
    color: #fff !important; /* White for text and icons */
    background: inherit;    /* Dynamically set color */
    border-color: inherit;  /* Matches dynamic color */
}

/* Hover Effects */
.status-button-default:hover {
    background: rgba(0, 0, 0, 0.1); /* Subtle hover for inactive */
    border-color: currentColor;     /* Shows border in text/icon color */
}

.status-button-active:hover {
    background: rgba(255, 255, 255, 0.2); /* Slightly brightened for active */
    border-color: inherit;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Subtle glow */
}

.status-btns.btn-group > .btn:nth-child(n+3), .btn-group > :not(.btn-check) + .btn{
    border-radius: 50px;
}


.all-leads-table .btn-group>.btn-group:not(:last-child)>.btn, .all-leads-table .btn-group>.btn.dropdown-toggle-split:first-child, .all-leads-table .btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
    border-radius: 50px;
    margin-right: 8px;
    margin-bottom: 8px;
    text-align: center;
}

.all-leads-table .btn-group>.btn:last-child {
    margin-right: 8px;
    margin-bottom: 8px;
    text-align: center;
}

.all-leads-table .btn-group .custom-button:hover {
    color: #444444;
    background: rgba(146, 192, 32, 0.08);
    border: 1px solid;
}

.all-leads-table.admin-theme .leads-table-navs .nav-link, .all-leads-table.admin-theme .btn-link {
    color: #FFFFFF;
    font-size: 0.8rem;
    font-weight: 300;
}

.clear-filters {
    border-radius: 39px;
    border: 1px solid #92C020;
    background: rgba(156, 196, 29, 0.30);
    color: #000;
}

.sort-dropdown-button {
    border-radius: 39px;
    border: 1px solid #DFDFDF;
    background: #FAFAFA;
    width: 100%;
    color: #000;
    font-weight: 400;
}

.sort-dropdown-button.dropdown-toggle::after {
    display: none;
    content: unset;
}

.social-filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    @media (max-width: 576px) {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
}

.social-filter-container .social-icon {
    max-width: 30px;
    max-height: 30px;
    cursor: pointer;
}

.social-filter-container .social-icon svg {
    width: 100%;
    height: 100%;
    min-width: 1.5rem;
    min-height: 1.5rem;
    padding: 4px;
}

.social-filter-container .social-icon.selected {
    border-radius: 39px;
    background: #000;
    transition: ease-in-out all 0.25s;
}

.social-filter-wrapper {
    border-radius: 39px;
    border: 1px solid #DFDFDF;
    background: #FAFAFA;
    padding: 0 10px 0 0;
}

.reset {
    border-radius: 39px;
    background: #FFFFFF;
    color: #000;
    padding: 9px 10px;
    transition: ease-in-out all 0.25s;
    font-size: 12px;
    font-weight: 700;
    cursor: pointer;
}

.reset.reset-selected {
    background: #000;
    color: #FFFFFF;
    padding: 9px 20px;
    transition: ease-in-out all 0.25s;
}

.client-table-row {
    cursor: pointer;
}

tr.client-table-row:hover td {
    background: #F4FFDA;
}

.data-table-pagination .page-link {
    margin: 10px 5px;
    color: rgba(0, 0, 0, 0.50);
    font-size: 1.25rem;
    font-weight: 400;
    border: unset;
    border-radius: 7px;
}

.data-table-pagination .active>.page-link {
    background: linear-gradient(220deg, #92C020 -9.71%, #CAD511 117.08%);
    color: #FFF;
}

.follow-up-filter.btn-group {
    display: flex;
    justify-content: space-evenly;
}

.follow-up-filter.btn-group .btn.btn-secondary {
    border-radius: 39px !important;
    background: rgba(146, 192, 32, 0.12);
    color: #5B5B5B;
    font-size: 1rem;
    font-weight: 400;
    margin: 0 10px;
    border: unset;
}

.follow-up-filter.btn-group .btn.btn-primary {
    border-radius: 39px;
    background: #92C020;
    color: #FFF;
    border: unset;
}

.filter-table-rows a{
    text-decoration: none;
    color: black;
}

.logs-tooltip-container {
    width: 250px !important;
    max-width: 300px !important;
    z-index: 999;
}

.log-container, .log-container-profile {
    position: relative;
    border-radius: 6px;
    background: #FFF;
    box-shadow: 0 0 32px -5.435px rgba(0, 0, 0, 0.14);
    color: #000;
    text-align: left;
    padding: 2px 8px;
    margin-bottom: 10px;
}

.log-container::after {
    content: url("../../../assets/media/li-marker.svg");
    position: absolute;
    top: 50%;
    right: 101%;
    transform: translate(-50%, -50%);
}

.note-dot {
    content: url("../../../assets/media/li-marker.svg");
    width: 20px;
    height: 20px;
    position: absolute;
    top: 100%;
    bottom: 0;
    right: 45%;
    transform: translate(-50%, -50%);
}

.logs-list-container {
    padding-left: 15px;
    border-left: 2px solid white;
}

.log-list-container-profile {
    padding-bottom: 20px;
    margin-bottom: 0;
    position: relative;
}

.activity-button {
    color: #000;
    border-radius: 39px;
    padding: 10px 15px;
}

.activity-button:hover, .activity-button:focus {
    background: #9CC41D;
    color: #FFFFFF;
    transition: all ease-in-out 0.25s;
}

.all-leads-table .table>:not(caption)>*>* {
    vertical-align: middle;
}

.admin-theme .import-leads, .admin-theme .export-leads, .admin-theme .clear-filter {
        color: #FFFFFF;
}

.admin-theme .import-leads:hover, .import-leads:focus, .export-leads:hover, .export-leads:focus, .clear-filter:hover, .clear-filter:focus{
        background: #FFFFFF;
        color: #242424;
        transition: all 0.25s ease-in-out;

}

.import-leads, .export-leads, .clear-filter {
    color: #000;
    border-radius: 10px;
    padding: 0.5rem;
    cursor: pointer;
    box-shadow: rgba(149, 157, 165, 0.2) 0 8px 24px;
}

.import-leads:hover, .import-leads:focus, .export-leads:hover, .export-leads:focus, .clear-filter:hover, .clear-filter:focus {
    background: #9CC41D;
    color: #FFFFFF;
    transition: all ease-in-out 0.25s;
}

.add-lead {
    border-radius: 10px;
    padding: 0.5rem;
    color: #9CC41D;
    cursor: pointer;
    box-shadow: rgba(149, 157, 165, 0.2) 0 8px 24px;
}

.export-leads {
    margin: 0 20px;
}

.import-leads {
    margin: 0 0 0 20px;
}

.add-lead:hover, .add-lead:focus {
    background: #9CC41D;
    color: #FFFFFF;
    transition: all ease-in-out 0.25s;
}

.admin-theme {
    border: 1px solid #444;
    border-radius: 12px;
    box-shadow: 0 5px 49px -6.18px rgba(0, 0, 0, 0.15);
    background: rgb(36, 36, 36);
}

.assign-client-icon {
    padding: 5px;
    margin: 0 auto 9px;
    border-radius: 10px;
    border: 1px solid #97C21F;
    background: #FFF;
    box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.10);
    cursor: pointer;
    width: fit-content;
}

.team-actions-button:after {
    content: unset;
}

.team-actions-menu {
    border-radius: 12px;
    background: #FFF;
    box-shadow: 0 4px 24px -7px rgba(0, 0, 0, 0.20);
    min-width: 195px;
    max-height: 200px;
    overflow-y: scroll;
}

/* Fix: allow assign/reassign dropdowns to overflow their small table container */
.all-leads-table,
.all-leads-table .table-responsive,
.all-leads-table table {
    overflow-x: auto !important; /* keep horizontal scroll for responsiveness */
    overflow-y: visible !important; /* dropdowns can still overflow vertically */
    -webkit-overflow-scrolling: touch;
}

/* Raise z-index so the menu sits above surrounding elements */
.team-actions-menu {
    z-index: 1070; /* Bootstrap modal is 1055, so stay on top but under modal */
}
