import { Navigate } from 'react-router-dom';
import { useSelector } from "react-redux";

function ProtectedRouteAdmin({ isSignedIn,
                                 // requiredRoles,
                                 children }) {
    const { user } = useSelector((state) => state.auth);
    // const userRole = user.user.role;
    if (isSignedIn) {
        if (user?.user?.flag === "user"){
            return <Navigate to="/"/>;
        }
        return children;
    } else {
        return <Navigate to="/admin/login"/>;
    }
}

export default ProtectedRouteAdmin;
