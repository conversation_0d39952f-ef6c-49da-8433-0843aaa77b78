import {Calendar, momentLocalizer} from "react-big-calendar";
import moment from "moment";
import withDragAndDrop from "react-big-calendar/lib/addons/dragAndDrop";
import "react-big-calendar/lib/addons/dragAndDrop/styles.css";
import "react-big-calendar/lib/css/react-big-calendar.css";
import {useState} from "react";
import {Button, Col, Modal, Row} from "react-bootstrap";
import {IoChevronBack, IoChevronForward} from "react-icons/io5";
import Form from "react-bootstrap/Form";
import CreatableSelect from "react-select/creatable";
import {BiSolidTrash} from "react-icons/bi";
import { useTranslation } from 'react-i18next';

const localizer = momentLocalizer(moment);

const initialEvents = [
    {
        id: 1, // Add a unique ID to the event
        start: new Date(),
        end: new Date(new Date().getTime() + 60 * 60 * 1000),
        title: "special event",
    },
];

const DnDCalendar = withDragAndDrop(Calendar);

const CustomToolbar = (toolbar) => {
    const { t } = useTranslation();
    
    const goToBack = () => {
        toolbar.onNavigate("PREV");
    };

    const goToNext = () => {
        toolbar.onNavigate("NEXT");
    };

    const goToToday = () => {
        toolbar.onNavigate("TODAY");
    };

    const goToWeek = () => {
        toolbar.onView("week");
    };

    const goToDay = () => {
        toolbar.onView("day");
    };

    const goToMonth = () => {
        toolbar.onView("month");
    };

    return (<div className="rbc-toolbar justify-content-around">
                <button type="button" className="btn btn-secondary" onClick={goToToday}>
                        {t('meeting.calendarView.today')}
                      </button>
                <div className="btn-group ml-2" role="group">
                      <button type="button" className="btn btn-secondary" onClick={goToWeek}>
                        {t('meeting.calendarView.week')}
                      </button>
                      <button type="button" className="btn btn-secondary" onClick={goToDay}>
                        {t('meeting.calendarView.day')}
                      </button>
                      <button type="button" className="btn btn-secondary" onClick={goToMonth}>
                        {t('meeting.calendarView.month')}
                      </button>
                </div>

        <div className={"d-flex justify-content-between align-items-center"}>
            <div role={"button"} onClick={goToBack}>
                <IoChevronBack size={30} className={"bg-white rounded-circle p-1 shadow"}/>
            </div>
            <span className="rbc-toolbar-label">{toolbar.label}</span>
            <div role={"button"} onClick={goToNext}>
                <IoChevronForward size={30} className={"bg-white rounded-circle p-1 shadow"}/>
            </div>
        </div>

    </div>);
};

const MeetingComponent = (props) => {
    const { t } = useTranslation();
    
    const durationOptions = [
        { value: 30, label: t('meeting.durations.thirtyMin') },
        { value: 60, label: t('meeting.durations.oneHour') },
        { value: 90, label: t('meeting.durations.oneHourThirty') },
    ];

    const addressOptions = [
        { value: '1', label: t('meeting.locations.mainStreet') },
        { value: '2', label: t('meeting.locations.oakAvenue') },
    ];

    const locationOptions = [
        { value: 'A', label: t('meeting.rooms.conferenceRoomA') },
        { value: 'B', label: t('meeting.rooms.conferenceRoomB') },
    ];

    const attendeesOptions = [
        { value: 'user1', label: t('meeting.attendees.johnDoe') },
        { value: 'user2', label: t('meeting.attendees.janeSmith') },
    ];
    
    const initialNewEvent = {
        host: "",
        title: "",
        start: new Date(),
        end: new Date(new Date().getTime() + 60 * 60 * 1000),
        duration: durationOptions[0].value,
        attendees: [],
        location: null,
        address: null,
    };
    const [events, setEvents] = useState(initialEvents);
    const [newEvent, setNewEvent] = useState(initialNewEvent);

    const handleEventModalClose = () => {
        setNewEvent({
            ...initialNewEvent,
            end: new Date(new Date().getTime() + 60 * 60 * 1000),
        });
    };



    const onEventDrop = ({ start, end, resourceId, event }) => {
        const updatedEnd = moment(end).isBefore(moment(start).endOf('day'))
            ? end
            : moment(start).endOf('day').toDate();

        const updatedEvent = {
            ...event,
            start,
            end: updatedEnd,
            resourceId,
        };

        setEvents((prevEvents) =>
            prevEvents.map((prevEvent) =>
                prevEvent.id === updatedEvent.id ? { ...updatedEvent } : prevEvent
            )
        );
    };

    const onEventResize = ({ start, end, resourceId, event }) => {
        const updatedEnd = moment(end).isBefore(moment(start).endOf('day'))
            ? end
            : moment(start).endOf('day').toDate();

        const updatedEvent = {
            ...event,
            start,
            end: updatedEnd,
            resourceId,
        };

        setEvents((prevEvents) =>
            prevEvents.map((prevEvent) =>
                prevEvent.id === updatedEvent.id ? { ...updatedEvent } : prevEvent
            )
        );
    };



    const handleFormChange = (name, value) => {
        setNewEvent((prevEvent) => ({
            ...prevEvent,
            [name]: value,
        }));
    };

    const handleSelectChange = (name, selectedOption) => {
        setNewEvent((prevEvent) => ({
            ...prevEvent,
            [name]: selectedOption ? selectedOption.value : null,
        }));
    };

    const handleFormSubmit = (e) => {
        e.preventDefault();
        setEvents((prevEvents) => [
            ...prevEvents,
            {
                ...newEvent,
                start: new Date(newEvent.start),
                end: new Date(newEvent.end),
            },
        ]);
        handleEventModalClose();
    };

    return (<Modal
            {...props}
            size="xl"
            aria-labelledby="contained-modal-title-vcenter"
            centered
        >
                <Form onSubmit={handleFormSubmit} className={"meeting-modal-container row"}>
                <Col lg={4} style={{borderRight: "1px solid black"}} className={"ps-4 pt-4"}>
                        <Form.Group controlId="eventHost">
                            <Form.Label>{t('meeting.host')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="host"
                                placeholder={t('meeting.enterHost')}
                                value={newEvent.title}
                                onChange={(e) => handleFormChange("host", e.target.value)}
                            />
                        </Form.Group>
                        <Form.Group controlId="eventTitle" className={"mt-3"}>
                            <Form.Label>{t('meeting.title')}</Form.Label>
                            <Form.Control
                                type="text"
                                name="title"
                                placeholder={t('meeting.enterTitle')}
                                value={newEvent.title}
                                onChange={(e) => handleFormChange("title", e.target.value)}
                            />
                        </Form.Group>
                        <Form.Group controlId="eventStart" className={"mt-3"}>
                            <Form.Label>{t('meeting.startTime')}</Form.Label>
                            <Form.Control
                                type="datetime-local"
                                name="start"
                                value={moment(newEvent.start).format("YYYY-MM-DDTHH:mm")}
                                onChange={(e) => handleFormChange("start", e.target.value)}
                            />
                        </Form.Group>
                        <Form.Group controlId="eventEnd" className={"mt-3"}>
                            <Form.Label>{t('meeting.endTime')}</Form.Label>
                            <Form.Control
                                type="datetime-local"
                                name="end"
                                value={moment(newEvent.end).format("YYYY-MM-DDTHH:mm")}
                                onChange={(e) => handleFormChange("end", e.target.value)}
                            />
                        </Form.Group>
                        <Row className={"mt-3"}>
                            <Col lg={6}>
                                <Form.Group controlId="eventDuration">
                                    <Form.Label>{t('meeting.duration')}</Form.Label>
                                    <CreatableSelect
                                        className="basic-single"
                                        classNamePrefix="select"
                                        defaultValue={durationOptions[0]}
                                        isClearable
                                        isSearchable
                                        name="duration"
                                        options={durationOptions}
                                        onChange={(selectedOption) => handleSelectChange("duration", selectedOption)}
                                    />
                                </Form.Group>
                            </Col>
                            <Col lg={6}>
                                <Form.Group controlId="eventAttendees">
                                    <Form.Label>{t('meeting.attendees')}</Form.Label>
                                    <CreatableSelect
                                        className="basic-single"
                                        classNamePrefix="select"
                                        defaultValue={null}
                                        isClearable
                                        isSearchable
                                        isMulti
                                        name="attendees"
                                        options={attendeesOptions}
                                        onChange={(selectedOption) => handleSelectChange("attendees", selectedOption)}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className={"mt-3"}>
                            <Col lg={12}>
                                <Form.Group controlId="eventLocation">
                                    <Form.Label>{t('meeting.location')}</Form.Label>
                                    <CreatableSelect
                                        className="basic-single"
                                        classNamePrefix="select"
                                        defaultValue={null}
                                        isClearable
                                        isSearchable
                                        name="location"
                                        options={locationOptions}
                                        onChange={(selectedOption) => handleSelectChange("location", selectedOption)}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                        <Row className={"mt-3"}>
                            <Col lg={12}>
                                <Form.Group controlId="eventAddress">
                                    <Form.Label>{t('meeting.address')}</Form.Label>
                                    <CreatableSelect
                                        className="basic-single"
                                        classNamePrefix="select"
                                        defaultValue={null}
                                        isClearable
                                        isSearchable
                                        name="address"
                                        options={addressOptions}
                                        onChange={(selectedOption) => handleSelectChange("address", selectedOption)}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                    </Col>
                        <Col lg={8} className={"p-4"}>
                            <Row>
                                <Col lg={12} className={"mt-3"}>
                                    <div className={"border p-3 rounded"}>
                                        <p>{t('meeting.scheduledReminder')}</p>
                                        <div className={"d-flex justify-content-end align-items-center"}>
                                            <Form.Check type="checkbox" id={`reminder-check`}/>
                                        </div>
                                    </div>
                                </Col>
                                <Col lg={12} className={"mt-3"}>
                                    <div className={"border p-3 rounded"}>
                                        <p>{t('meeting.attendeeDescription')}</p>
                                        <Form.Control placeholder={t('meeting.description')} as="textarea" rows={3} />
                                    </div>
                                </Col>
                            </Row>

                            <div className={"d-flex justify-content-between mt-3"}>
                                <Button variant={"secondary"} onClick={props.onHide}>Close</Button>
                                <Button variant={"danger"} type={"button"} className={"d-flex align-items-center gap-1"}>
                                    <BiSolidTrash size={13}/>
                                    Delete
                                </Button>
                                <Button variant={"primary"} type={"submit"}>Save</Button>
                            </div>
                        </Col>

            </Form>
        </Modal>
    );
};

export default MeetingComponent;
