.email-to-select > * {
    border-style: unset !important;
}

.email-row {
    border-bottom: 1px solid #00000080;
}

.email-row > * > * {
    border: unset;
}

.multi-select-attendees .select__value-container {
    overflow-y: scroll;
    height: 28px;
}

.section-with-divider {
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.border-right-secondary {
    border-right: 1px solid rgba(0, 0, 0, 0.30);
}

.meeting-actions-row {
    border-radius: 22px;
    background: #FFF;
    box-shadow: 0 4px 54px 0 rgba(0, 0, 0, 0.15);
}

.page-number-input {
    width: fit-content;
    max-width: 50px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #9DC41D;
    background: #FFF;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
    padding: 10px 0 10px 10px;
    margin: 0 5px;
}

.records-buttons-container {
    border: 1px solid #9DC41D;
    border-radius: 5px;
    padding: 4px;
}

.record-button-selected {
    border: 1px solid #9DC41D;
    background: #9DC41D;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.24);
    color: #FFFFFF;
    padding: 6px 10px;
    border-radius: 4px;
}

.record-button {
    border-radius: 4px;
    color: #000;
    padding: 6px 10px;
}

.rbc-time-view {
    height: 100%;
}

.activity-collapse-btn {
    border-radius: 14px;
    background: #F2F7E4;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0.8rem;
}

.activity-collapse-btn-collapsed {
    border-radius: 14px;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0.8rem;
}

.activity-collapse-container {
    background: #F2F7E4;
    border-radius: 14px;
}

.activity-description {
    margin: 48px 0 20px;
    border-radius: 14px;
    background: #FFF;
    box-shadow: 0 4px 58px -7px rgba(0, 0, 0, 0.10);
    padding: 12px;
}


input.activity-date-picker {
    display: inline-block;
    position: relative;
}

input[type="date"].activity-date-picker::-webkit-calendar-picker-indicator {
    background: transparent;
    bottom: 0;
    color: transparent;
    cursor: pointer;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
}

input.activity-date-picker:after {
    content: '\1F4C5';
}

.swiper.lead-overview-swiper {
    /*height: 160px;*/
    /*padding-bottom: 20px;*/
    border-bottom: 2px solid rgba(90, 90, 90, 0.2);
}

/*.swiper.lead-overview-swiper .swiper-wrapper {*/
/*    border-bottom: 2px solid rgba(90, 90, 90, 0.2);*/
/*}*/

.swiper.lead-overview-swiper .swiper-slide {
    height: 100%;
}

.log-container-profile {
    height: 60%;
    min-height: 130px;
}

.line-clamp {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.completedStatus {
    background: #F2F7E4;
    color: #92C020;
}

.activitySlice {
    border-radius: 10px;
    padding: 2px;
   ::-webkit-scrollbar {
        display: none;
    }
}

.admin-theme .activitySlice {
    border: 1px solid #444;
    background: #242424;
    color: white;
}

.note-dot {
    content: url("../../assets/media/li-marker.svg");
    width: 20px;
    height: 20px;
    position: absolute;
    top: 100%;
    bottom: 0;
    right: 45%;
    transform: translate(-50%, -50%);
}

.activitySlice:hover {
    border: 2px solid #9DC41D;
    transition: border-color ease-in-out 0.25s;
}

.swiper.activities-swiper {
    border-bottom: 2px solid rgba(90, 90, 90, 0.2);
}

.activities-swiper .swiper-button-next,.activities-swiper .swiper-button-prev {
    border-radius: 50%;
    background-color: #9DC41D;
    color: #FFFFFF;
    height: 40px;
    right: 7px;
    top: 55%;
    width: 40px;
}

.activities-swiper .swiper-button-next:after,.activities-swiper .swiper-button-prev:after {
 font-size: 1rem !important;
}

.activities-swiper .swiper-button-next {
    margin-right: -.5rem;
}

.activities-swiper .swiper-button-prev {
    margin-left: -.5rem;
}

.overview-detail-card {
    padding: 1rem 0;
    box-shadow: rgba(149, 157, 165, 0.2) 0 8px 24px;
    margin: 1rem 0;
    border-radius: 25px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 7.5rem;
}

.lead-source-icon svg {
    height: 20px;
    width: 20px;
}

.dropzone {
    border: 2px dashed #ccc;
    padding: 20px;
    text-align: center;
}

.dropzone.error {
    border-color: #FF0000;
    background-color: #FFDADA;
    color: red;
}

.dropzone.active {
    border-color: #9DC41D;
    background-color: #DAFFB3;
    color: #9DC41D;
}