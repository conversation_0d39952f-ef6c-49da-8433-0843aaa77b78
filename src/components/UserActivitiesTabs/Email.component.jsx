import { useState } from 'react';
import makeAnimated from 'react-select/animated';
import {Col, Row, Form, FloatingLabel} from 'react-bootstrap';
import CreatableSelect from "react-select/creatable";

const animatedComponents = makeAnimated();

const EmailComponent = () => {
    const [toOptions, setToOptions] = useState([]);
    const [ccOptions, setCcOptions] = useState([]);
    const [bccOptions, setBccOptions] = useState([]);
    const [fromOptions, setFromOptions] = useState([]);
    const [isCcVisible, setIsCcVisible] = useState(false);
    const [isBccVisible, setIsBccVisible] = useState(false);

    const loginOptions = [
        { value: 'user1', label: 'User 1' },
        { value: 'user2', label: 'User 2' },
        { value: 'user3', label: 'User 3' },
    ];

    return (
        <section className="p-2">
            <Row className="justify-content-start align-items-center email-row mx-4">
                <Col lg={1}>To</Col>
                <Col lg={9}>
                    <CreatableSelect
                        closeMenuOnSelect={false}
                        components={animatedComponents}
                        isMulti
                        options={toOptions}
                        className="email-to-select"
                        isClearable
                    />
                </Col>
                <Col lg={2} className="d-flex justify-content-start">
                    <div onClick={() => setIsCcVisible(!isCcVisible)}>Cc</div>
                    <div className="ms-1" onClick={() => setIsBccVisible(!isBccVisible)}>
                        Bcc
                    </div>
                </Col>
            </Row>

            {isCcVisible && (
                <Row className="justify-content-start align-items-center email-row mx-4">
                    <Col lg={1}>Cc</Col>
                    <Col lg={9}>
                        <CreatableSelect
                            closeMenuOnSelect={false}
                            components={animatedComponents}
                            isMulti
                            className="email-to-select"
                            isClearable
                            options={ccOptions}
                        />
                    </Col>
                </Row>
            )}

            {isBccVisible && (
                <Row className="justify-content-start align-items-center email-row mx-4">
                    <Col lg={1}>Bcc</Col>
                    <Col lg={9}>
                        <CreatableSelect
                            closeMenuOnSelect={false}
                            components={animatedComponents}
                            isMulti
                            className="email-to-select"
                            isClearable
                            options={bccOptions}
                        />
                    </Col>
                </Row>
            )}

            <Row className="justify-content-start align-items-center email-row mx-4">
                <Col lg={1}>From</Col>
                <Col lg={11}>
                    <CreatableSelect
                        closeMenuOnSelect={false}
                        components={animatedComponents}
                        isMulti
                        options={fromOptions}
                        className="email-to-select"
                        isClearable
                    />
                </Col>
            </Row>
            <Row className={"justify-content-start align-items-center email-row mx-4"}>
                <Col lg={2}>
                    <Form.Label htmlFor={"subject"}>Subject</Form.Label>
                </Col>
                <Col lg={10}>
                        <Form.Control
                            aria-label="subject"
                            aria-describedby="inputGroup-subject"
                            id={"subject"}
                        />
                </Col>
            </Row>
            <div className={"mx-4"}>
                <FloatingLabel controlId="Message" label="Message">
                    <Form.Control
                        as="textarea"
                        placeholder="Leave a comment here"
                        style={{ height: '150px' }}
                    />
                </FloatingLabel>
            </div>
            <Row>
                <button className="btn submit-button mt-3">Send Note</button>
            </Row>
        </section>
    );
};

export default EmailComponent;
