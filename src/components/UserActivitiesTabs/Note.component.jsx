import Form from "react-bootstrap/Form";
import { useTranslation } from 'react-i18next';

const NoteComponent = () => {
    const { t } = useTranslation();
    
    return (
        <>
        <Form.Group className="mb-3" controlId="exampleForm.ControlTextarea1">
            <Form.Control placeholder={t('forms.placeholder.typeNote')} as="textarea" rows={8} />
        </Form.Group>
            <button className="btn submit-button">{t('common.submit')}</button>
        </>
    );
};

export default NoteComponent;
