import { useEffect, useRef, useState } from "react";
import { Col, Row } from "react-bootstrap";
import { Line, Doughnut } from "react-chartjs-2";
import {
  getLeadsSourceAPI,
  getTeamCountAPI,
  getTotalLeadsStatusAPI,
} from "../../services/reports/get-leads-reports.api";
import { useTranslation } from "react-i18next";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
} from "chart.js";

// Register Chart.js components (once per bundle)
ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement
);

const ChartsForLeadAssignment = ({ activeTab }) => {
  const { t } = useTranslation();
  const [pieChartRoundedData, setPieChartRoundedData] = useState([]);
  const [pieChartLeadsStatus, setPieChartLeadsStatus] = useState([]);
  const [salesData, setSalesData] = useState([]);
  const fetchTotalLeadsAbortController = useRef(null);
  const fetchLeadsSourceAbortController = useRef(null);
  const fetchTeamCountAbortController = useRef(null);
  useEffect(() => {
    if (activeTab !== "assignment") {
      return;
    }

    const fetchData = async () => {
      // Abort any ongoing requests to prevent overlapping API calls
      if (fetchTotalLeadsAbortController.current) {
        fetchTotalLeadsAbortController.current.abort();
      }
      if (fetchLeadsSourceAbortController.current) {
        fetchLeadsSourceAbortController.current.abort();
      }
      if (fetchTeamCountAbortController.current) {
        fetchTeamCountAbortController.current.abort();
      }

      // Create new abort controllers for each API call
      fetchTotalLeadsAbortController.current = new AbortController();
      fetchLeadsSourceAbortController.current = new AbortController();
      fetchTeamCountAbortController.current = new AbortController();

      const totalLeadsSignal = fetchTotalLeadsAbortController.current.signal;
      const leadsSourceSignal = fetchLeadsSourceAbortController.current.signal;
      const teamCountSignal = fetchTeamCountAbortController.current.signal;

      try {
        const results = await Promise.allSettled([
          getTotalLeadsStatusAPI({ signal: totalLeadsSignal }),
          getLeadsSourceAPI({ signal: leadsSourceSignal }),
          getTeamCountAPI({ signal: teamCountSignal }),
        ]);

        const [totalLeadsResult, leadsSourceResult, teamCountResult] = results;

        // Handle the total leads response
        if (totalLeadsResult.status === "fulfilled") {
          setPieChartLeadsStatus(totalLeadsResult.value?.result);
        } else {
          console.error(
            "Error fetching total leads data:",
            totalLeadsResult.reason
          );
        }

        // Handle the leads source response
        if (leadsSourceResult.status === "fulfilled") {
          // Check if the response contains an error message about division by zero
          if (leadsSourceResult.value?.exception === "DivisionByZeroError") {
            console.error("Division by zero error in leads source data");
            setPieChartRoundedData([]);
          } else {
            setPieChartRoundedData(leadsSourceResult.value?.result || []);
          }
        } else {
          console.error(
            "Error fetching leads source data:",
            leadsSourceResult.reason
          );
        }

        // Handle the team count response
        if (teamCountResult.status === "fulfilled") {
          setSalesData(teamCountResult.value?.result);
        } else {
          console.error(
            "Error fetching team count data:",
            teamCountResult.reason
          );
        }
      } catch (error) {
        if (error.name === "CanceledError") {
          console.log("Fetch aborted");
        } else {
          console.error("Error fetching Lead Assignment Data:", error);
        }
      }
    };

    fetchData();

    // Cleanup function to abort the fetch if the component unmounts or the tab changes
    return () => {
      if (fetchTotalLeadsAbortController.current) {
        fetchTotalLeadsAbortController.current.abort();
      }
      if (fetchLeadsSourceAbortController.current) {
        fetchLeadsSourceAbortController.current.abort();
      }
      if (fetchTeamCountAbortController.current) {
        fetchTeamCountAbortController.current.abort();
      }
    };
  }, [activeTab]);

  const getColor = (label) => {
    switch (label) {
      case "facebook":
        return "#1b47f8";
      case "linked":
        return "#0077B5";
      case "tiktok":
        return "#191919";
      case "snapchat":
        return "#BED754";
      case "insta":
        return "red";
      case "global":
        return "#750E21";
      case "google":
        return "#EA4335";
      case "phone":
        return "gray";
      case "whatsapp":
        return "#075E54";
      default:
        return "black";
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "green";
      case "Completed":
        return "green";
      case "In Progress":
        return "blue";
      case "Pending":
        return "orange";
      case "Rejected":
        return "red";
      default:
        return "gray";
    }
  };

  const totalYValues = pieChartLeadsStatus?.reduce(
    (sum, data) => sum + parseFloat(data.y),
    0
  );
  return (
    <Row className="justify-content-center justify-content-md-evenly">
      <Col
        lg={6}
        md={12}
        className="content-container lead-chart-container"
        style={{ height: "350px" }}
      >
        <div className="d-flex justify-content-between">
          <div className={"fs-5 fw-bold text-muted"}>
            {t("reports.charts.leadAssignment.title")}
          </div>
          <div className={"fs-5 fw-bold"}>{totalYValues}</div>
        </div>
        <div
          className="position-relative mx-auto"
          style={{ width: "200px", height: "200px" }}
        >
          <Doughnut
            data={{
              labels: pieChartLeadsStatus?.map((datum) => datum.x),
              datasets: [
                {
                  data: pieChartLeadsStatus?.map((datum) => datum.y),
                  backgroundColor: pieChartLeadsStatus?.map((datum) =>
                    getStatusColor(datum.x)
                  ),
                  borderColor: "#fff",
                  borderWidth: 4,
                },
              ],
            }}
            options={{
              responsive: true,
              plugins: {
                tooltip: {
                  enabled: true,
                  boxPadding: 8,
                  callbacks: {
                    label: function (context) {
                      const label = context.label || "";
                      const value = context.raw || 0;
                      return `${label}: ${value}`;
                    },
                  },
                },
                legend: {
                  display: false,
                },
              },
              elements: {
                arc: {
                  borderRadius: 20,
                  borderWidth: 4,
                },
              },
              layout: {
                padding: {
                  top: 20,
                  bottom: 20,
                  left: 20,
                  right: 20,
                },
              },
              cutout: "80%",
            }}
          />
          <div
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              textAlign: "center",
              fontSize: "20px",
              fontWeight: "bold",
              color: "#333",
              zIndex: 10,
            }}
          >
            {pieChartLeadsStatus?.length > 0
              ? `${
                  pieChartLeadsStatus?.reduce((a, b) => (a.y > b.y ? a : b)).x
                }: ${
                  pieChartLeadsStatus?.reduce((a, b) => (a.y > b.y ? a : b)).y
                }`
              : "No data available"}
          </div>
        </div>
        <div
          className="labels-container text-capitalize"
          style={{ maxHeight: "100px", overflowY: "auto" }}
        >
          {pieChartLeadsStatus?.map((datum, index) => (
            <div key={index} className="label-item">
              <span
                className="dot"
                style={{ backgroundColor: getStatusColor(datum.x) }}
              />
              {datum.x}
            </div>
          ))}
        </div>
      </Col>
      <Col
        lg={6}
        md={12}
        className="content-container lead-chart-container"
        style={{ height: "350px" }}
      >
        <div className="d-flex justify-content-between">
          <div className={"fs-5 fw-bold text-muted"}>
            {t("reports.charts.leadAssignment.source")}
          </div>
          <div className={"fs-5 fw-bold"}>{pieChartRoundedData?.length}</div>
        </div>
        <div
          className="position-relative mx-auto"
          style={{ width: "200px", height: "200px" }}
        >
          <Doughnut
            data={{
              labels: pieChartRoundedData?.map((datum) => datum.x),
              datasets: [
                {
                  data: pieChartRoundedData?.map((datum) => datum.y),
                  backgroundColor: pieChartRoundedData?.map((datum) =>
                    getColor(datum.x)
                  ),
                  borderColor: "#fff",
                  borderWidth: 4,
                },
              ],
            }}
            options={{
              responsive: true,
              plugins: {
                tooltip: {
                  enabled: true,
                  boxPadding: 8,
                  callbacks: {
                    label: function (context) {
                      const label = context.label || "";
                      const value = context.raw || 0;
                      return `${label}: ${value}%`;
                    },
                  },
                },
                legend: {
                  display: false,
                },
              },
              elements: {
                arc: {
                  borderRadius: 20,
                  borderWidth: 4,
                },
              },
              layout: {
                padding: {
                  top: 20,
                  bottom: 20,
                  left: 20,
                  right: 20,
                },
              },
              cutout: "80%",
            }}
          />
          <div
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              textAlign: "center",
              fontSize: "20px",
              fontWeight: "bold",
              color: "#333",
              zIndex: 10,
            }}
          >
            {pieChartRoundedData?.length > 0
              ? `${
                  pieChartRoundedData?.reduce((a, b) => (a.y > b.y ? a : b)).x
                }: ${
                  pieChartRoundedData?.reduce((a, b) => (a.y > b.y ? a : b)).y
                }%`
              : "No data available"}
          </div>
        </div>
        <div
          className="labels-container"
          style={{ maxHeight: "100px", overflowY: "auto" }}
        >
          {pieChartRoundedData?.map((datum, index) => (
            <div key={index} className="label-item">
              <span
                className="dot"
                style={{ backgroundColor: getColor(datum.x) }}
              />
              {datum.x}
            </div>
          ))}
        </div>
      </Col>
      <Col
        lg={6}
        md={12}
        className={"content-container lead-chart-container"}
        style={{ height: "350px" }}
      >
        <div className={"d-flex justify-content-between flex-column"}>
          <div className={"fs-5 fw-bold text-muted"}>
            {t("reports.charts.leadAssignment.status")}
          </div>
        </div>
        <Line
          data={{
            labels: salesData?.map((item) => item.month),
            datasets: [
              {
                label: "Moderator",
                backgroundColor: "rgba(72,128,255,0.5)",
                borderColor: "rgb(72,128,255)",
                data: salesData?.map((item) => item.moderator),
                fill: false,
                tension: 0.4,
                pointRadius: 6,
                pointHoverRadius: 8,
              },
              {
                label: "Sales",
                backgroundColor: "rgba(255,99,132,0.5)",
                borderColor: "rgb(255,99,132)",
                data: salesData?.map((item) => item.sales),
                fill: false,
                tension: 0.4,
                pointRadius: 6,
                pointHoverRadius: 8,
              },
            ],
          }}
          options={{
            responsive: true,
            plugins: {
              tooltip: {
                enabled: true,
                boxPadding: 8,
                callbacks: {
                  label: function (context) {
                    const label = context.dataset.label || "";
                    const value = context.raw || 0;
                    return `${label}: ${value}`;
                  },
                },
              },
              legend: {
                display: true,
              },
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function (value) {
                    return value;
                  },
                },
              },
            },
          }}
        />
      </Col>
    </Row>
  );
};

export default ChartsForLeadAssignment;
