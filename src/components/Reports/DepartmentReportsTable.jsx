import { useState, useEffect, useMemo, useCallback } from "react";
import { useTable, useSortBy } from "react-table";
import {
  getLeadsPlatformAPI,
  getLeadsServiceAPI,
} from "../../services/reports/get-leads-reports.api.js";
import { Table, Form, Row, Col, Button } from "react-bootstrap";
import { FaCalendarAlt } from "react-icons/fa";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
  format,
  startOfDay,
  endOfDay,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  subDays,
  subWeeks,
  subMonths,
} from "date-fns";
import { setDepartmentFilters } from "../../redux/features/reportsSlice";
import { TiArrowForward } from "react-icons/ti";
import { useRef } from "react";
import {
  ExportLeadsPlatformReportApi,
  ExportLeadsServiceReportApi,
} from "../../services/reports/export-reports.api";

// Add responsive styles for department reports table inspired by clean table approach
const tableStyles = `
  .department-reports-table {
    font-size: 0.875rem;
    margin: 0;
  }

  .department-reports-table [role="region"][aria-labelledby][tabindex] {
    width: 100%;
    max-height: 90vh;
    overflow: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .department-reports-table [role="region"][aria-labelledby][tabindex]:focus {
    box-shadow: 0 0 0.5em rgba(0, 0, 0, 0.5);
    outline: 0;
  }

  .department-reports-table .table {
    white-space: nowrap;
    margin: 0;
    border: none;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: auto;
    border: 1px solid #dee2e6;
    min-width: max-content;
    width: 100%;
  }

  .department-reports-table .table td,
  .department-reports-table .table th {
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
  }

  .department-reports-table .table thead th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #343a40 !important;
    color: white !important;
    border-color: #495057 !important;
    font-weight: 600;
    text-align: center;
    min-width: 120px;
    white-space: nowrap;
    padding: 0.75rem 0.5rem;
  }

  /* Action 2 main header styling - column span header */
  .department-reports-table .table thead tr:first-child th.action2-main-header {
    background-color: #495057 !important;
    color: white !important;
    font-weight: 700;
    font-size: 0.9rem;
    border: 2px solid #6c757d !important;
    text-align: center;
    vertical-align: middle;
  }

  /* Action 2 sub-headers styling - second row headers */
  .department-reports-table .table thead tr:nth-child(2) th.action2-sub-header {
    background-color: #6c757d !important;
    color: white !important;
    font-weight: 600;
    font-size: 0.85rem;
    border: 1px solid #adb5bd !important;
    text-align: center;
  }

  /* Ensure regular headers span both rows when Action 2 is present */
  .department-reports-table .table thead tr:first-child th:not(.action2-main-header) {
    vertical-align: middle;
  }

  /* Regular header styling */
  .department-reports-table .table thead th.regular-header {
    background-color: #343a40 !important;
    color: white !important;
    font-weight: 600;
    text-align: center;
  }



  /* Ensure Action 2 columns have adequate width */
  .department-reports-table .table td,
  .department-reports-table .table th {
    min-width: 100px;
    max-width: none;
    width: auto;
  }

  /* Specific width for first column (Department/Service) */
  .department-reports-table .table thead th:first-child,
  .department-reports-table .table tbody td:first-child {
    min-width: 200px;
    width: 200px;
    max-width: 200px;
  }

  /* Specific width for Total column only */
  .department-reports-table .table thead th.total-column-header,
  .department-reports-table .table tbody td.total-column-cell {
    min-width: 100px;
    width: 100px;
    max-width: 100px;
  }

  /* Action 2 cell styling - special background for data cells */
  .department-reports-table .table tbody td.action2-cell {
    background-color: #e8f5e8 !important;
    border: 1px solid #92C020 !important;
  }

  /* Action 2 cell styling for Grand Total row */
  .department-reports-table .table-warning td.action2-cell {
    background-color: #d4edda !important;
    border: 1px solid #92C020 !important;
    font-weight: 600;
  }

  /* Override for mobile to prevent extreme compression */
  @media (min-width: 992px) {
    .department-reports-table .table {
      min-width: 1400px;
    }

    .department-reports-table .table thead th {
      min-width: 120px;
    }

    .department-reports-table .table thead tr:nth-child(2) th {
      min-width: 120px !important;
      width: 120px;
    }
  }

  .department-reports-table .table thead th:first-child:not(.action2-sub-header) {
    position: sticky;
    left: 0;
    z-index: 11;
    min-width: 200px;
    width: 200px;
    text-align: left;
    background-color: #343a40 !important;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  }

  /* Only apply sticky positioning to the Total column specifically */
  .department-reports-table .table thead th.total-column-header {
    position: sticky;
    right: 0;
    z-index: 11;
    min-width: 100px;
    background-color: #343a40 !important;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
  }

  .department-reports-table .table td {
    background: #fff;
    text-align: center;
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
  }

  /* First column body cells - sticky positioning */
  .department-reports-table .table tbody td:first-child,
  .department-reports-table .table tbody td.first-column-cell {
    position: sticky;
    left: 0;
    background: white;
    z-index: 9;
    min-width: 200px;
    width: 200px;
    max-width: 200px;
    font-weight: 500;
    text-align: left;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  }

  /* Only apply sticky positioning to the Total column cells specifically */
  .department-reports-table .table tbody td.total-column-cell {
    position: sticky;
    right: 0;
    background: white;
    z-index: 9;
    min-width: 100px;
    width: 100px;
    max-width: 100px;
    font-weight: 600;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
  }

  /* Grand Total row styling */
  .department-reports-table .table-warning td {
    background-color: #fff3cd !important;
  }

  .department-reports-table .table-warning td:first-child {
    background-color: #fff3cd !important;
  }

  .department-reports-table .table-warning td.total-column-cell {
    background-color: #fff3cd !important;
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .department-reports-table {
      font-size: 0.8rem;
    }

    .department-reports-table .table td,
    .department-reports-table .table th {
      padding: 0.5rem 0.75rem;
    }

    .department-reports-table .table thead th:first-child,
    .department-reports-table .table tbody td:first-child {
      min-width: 180px;
      width: 180px;
    }
  }

  @media (max-width: 576px) {
    .department-reports-table {
      font-size: 0.75rem;
    }

    .department-reports-table .table td,
    .department-reports-table .table th {
      padding: 0.375rem 0.5rem;
    }

    .department-reports-table .table thead th:first-child,
    .department-reports-table .table tbody td:first-child {
      min-width: 160px;
      width: 160px;
    }
  }
`;

const DepartmentReportsTable = ({ activeTab, reportType = "sources" }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const savedFilters = useSelector((state) => state.reports.departmentFilters);

  // State management
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState(
    savedFilters?.dateRange || "custom"
  );
  const [startDate, setStartDate] = useState(
    savedFilters?.startDate ? new Date(savedFilters.startDate) : null
  );
  const [endDate, setEndDate] = useState(
    savedFilters?.endDate ? new Date(savedFilters.endDate) : null
  );

  // Dynamic data arrays - will be populated from API responses
  const [departments, setDepartments] = useState([]);
  const [leadStatuses, setLeadStatuses] = useState([]);

  // Date range options
  const dateRangeOptions = [
    { value: "today", label: t("filters.dateRanges.today", "Today") },
    {
      value: "yesterday",
      label: t("filters.dateRanges.yesterday", "Yesterday"),
    },
    { value: "thisWeek", label: t("filters.dateRanges.thisWeek", "This Week") },
    { value: "lastWeek", label: t("filters.dateRanges.lastWeek", "Last Week") },
    {
      value: "thisMonth",
      label: t("filters.dateRanges.thisMonth", "This Month"),
    },
    {
      value: "lastMonth",
      label: t("filters.dateRanges.lastMonth", "Last Month"),
    },
    { value: "thisYear", label: t("filters.dateRanges.thisYear", "This Year") },
    { value: "custom", label: t("filters.dateRanges.custom", "Custom Range") },
  ];

  // Handle date range change
  const handleDateRangeChange = (value) => {
    setDateRange(value);
    if (value === "custom") {
      // Keep current custom dates if they exist
      return;
    }

    const now = new Date();

    switch (value) {
      case "today":
        setStartDate(startOfDay(now));
        setEndDate(endOfDay(now));
        break;
      case "yesterday":
        const yesterday = subDays(now, 1);
        setStartDate(startOfDay(yesterday));
        setEndDate(endOfDay(yesterday));
        break;
      case "thisWeek":
        setStartDate(startOfWeek(now));
        setEndDate(endOfWeek(now));
        break;
      case "lastWeek":
        const lastWeekStart = startOfWeek(subWeeks(now, 1));
        const lastWeekEnd = endOfWeek(subWeeks(now, 1));
        setStartDate(lastWeekStart);
        setEndDate(lastWeekEnd);
        break;
      case "thisMonth":
        setStartDate(startOfMonth(now));
        setEndDate(endOfMonth(now));
        break;
      case "lastMonth":
        const lastMonthStart = startOfMonth(subMonths(now, 1));
        const lastMonthEnd = endOfMonth(subMonths(now, 1));
        setStartDate(lastMonthStart);
        setEndDate(lastMonthEnd);
        break;
      case "thisYear":
        setStartDate(startOfYear(now));
        setEndDate(endOfYear(now));
        break;
      default:
        break;
    }
  };

  // Define columns based on report type
  const columns = useMemo(() => {
    const baseColumns = [
      {
        Header:
          reportType === "sources"
            ? t("tables.headers.source", "Source")
            : t("tables.headers.department", "Department/Service"),
        accessor: "sourceOrStatus",
        Cell: ({ value, row }) => (
          <span
            className={
              row.original.sourceOrStatus === "Grand Total" ? "fw-bold" : ""
            }
          >
            {value}
          </span>
        ),
      },
    ];

    // For sources table: departments as columns
    // For status table: statuses as columns with Action 2 grouping
    const dataColumns =
      reportType === "sources"
        ? departments.length > 0
          ? departments.map((department) => ({
              Header: department,
              accessor: department,
              Cell: ({ value, row }) => (
                <span
                  className={
                    row.original.sourceOrStatus === "Grand Total"
                      ? "fw-bold"
                      : ""
                  }
                >
                  {value || 0}
                </span>
              ),
            }))
          : []
        : leadStatuses.length > 0
        ? leadStatuses.map((status) => {
            // Find the index of "Sent SMS" to determine where Action 2 starts
            const sentSMSIndex = leadStatuses.findIndex(
              (s) => s === "Sent SMS"
            );
            const isAction2Column =
              leadStatuses.indexOf(status) >= sentSMSIndex && sentSMSIndex >= 0;

            return {
              Header: status,
              accessor: status,
              id: status,
              headerClassName: isAction2Column
                ? "action2-sub-header"
                : "regular-header",
              Cell: ({ value, row }) => (
                <span
                  className={
                    row.original.sourceOrStatus === "Grand Total"
                      ? isAction2Column
                        ? "fw-bold action2-cell"
                        : "fw-bold"
                      : isAction2Column
                      ? "action2-cell"
                      : ""
                  }
                >
                  {value || 0}
                </span>
              ),
            };
          })
        : [];

    const totalColumn = {
      Header: t("tables.headers.total", "Total"),
      accessor: "total",
      headerClassName: "total-column-header",
      Cell: ({ value, row }) => (
        <span
          className={
            row.original.sourceOrStatus === "Grand Total" ? "fw-bold" : ""
          }
        >
          {value || 0}
        </span>
      ),
    };

    return [...baseColumns, ...dataColumns, totalColumn];
  }, [reportType, t, departments, leadStatuses]);

  // Helper function to check if this component should be active
  const isComponentActive = useCallback(() => {
    const shouldFetchForSources =
      activeTab === "departmentSources" && reportType === "sources";
    const shouldFetchForStatuses =
      activeTab === "departmentStatuses" && reportType === "statuses";
    return shouldFetchForSources || shouldFetchForStatuses;
  }, [activeTab, reportType]);

  // Transform API response data for table display
  const transformApiData = useCallback(
    (apiResponse, type) => {
      // Handle different possible response structures
      const result = apiResponse?.result || apiResponse;

      if (!result) {
        return [];
      }

      if (type === "sources") {
        // For platform API - data is structured as sources -> departments
        // Transform to sources as rows, departments as columns
        const transformedData = [];

        // Extract sources and departments dynamically from the API response
        const availableSources = Object.keys(result);

        // Get all unique departments from the first source
        const firstSource = Object.values(result)[0];
        const availableDepartments = firstSource
          ? Object.keys(firstSource).filter((key) => key !== "total")
          : [];
        setDepartments(availableDepartments);

        // Create a row for each source
        availableSources.forEach((source) => {
          const row = { sourceOrStatus: source };
          const sourceData = result[source];

          // For each department, get the value for this source
          availableDepartments.forEach((department) => {
            if (sourceData && sourceData[department] !== undefined) {
              row[department] = sourceData[department] || 0;
            } else {
              row[department] = 0;
            }
          });

          // Calculate total for this source
          row.total = availableDepartments.reduce(
            (sum, department) => sum + (row[department] || 0),
            0
          );

          transformedData.push(row);
        });

        // Add Grand Total row
        const grandTotal = { sourceOrStatus: "Grand Total" };
        availableDepartments.forEach((department) => {
          grandTotal[department] = transformedData.reduce(
            (sum, row) => sum + (row[department] || 0),
            0
          );
        });
        grandTotal.total = transformedData.reduce(
          (sum, row) => sum + (row.total || 0),
          0
        );
        transformedData.push(grandTotal);

        return transformedData;
      } else {
        // For service API - data is structured as departments -> statuses
        // Keep departments as rows, statuses as columns (opposite of sources table)
        const transformedData = [];
        const availableDepartments = Object.keys(result);
        setDepartments(availableDepartments);

        // Extract statuses dynamically from the first department
        const firstDepartment = Object.values(result)[0];
        const availableStatuses = firstDepartment
          ? Object.keys(firstDepartment).filter((key) => key !== "total")
          : [];
        setLeadStatuses(availableStatuses);

        // Create a row for each department
        availableDepartments.forEach((department) => {
          const row = { sourceOrStatus: department };
          const deptData = result[department];

          if (deptData && typeof deptData === "object") {
            // For each status, get the value for this department
            availableStatuses.forEach((status) => {
              row[status] = deptData[status] || 0;
            });
            row.total = deptData.total || 0;
          }

          transformedData.push(row);
        });

        // Add Grand Total row for statuses
        const grandTotal = { sourceOrStatus: "Grand Total" };
        availableStatuses.forEach((status) => {
          grandTotal[status] = transformedData.reduce(
            (sum, row) => sum + (row[status] || 0),
            0
          );
        });
        grandTotal.total = transformedData.reduce(
          (sum, row) => sum + (row.total || 0),
          0
        );
        transformedData.push(grandTotal);

        return transformedData;
      }
    },
    [setDepartments, setLeadStatuses]
  );

  // Fetch data from API
  const fetchData = useCallback(
    async (params = {}) => {
      setLoading(true);
      try {
        let response;

        // Build API parameters
        const apiParams = {
          ...params,
        };

        if (reportType === "sources") {
          // Call platform API for department by sources
          response = await getLeadsPlatformAPI({ params: apiParams });
        } else {
          // Call service API for department by statuses
          response = await getLeadsServiceAPI({ params: apiParams });
        }

        // Try different possible response structures
        const apiData = response?.data || response?.result || response;

        if (apiData) {
          const transformedData = transformApiData(apiData, reportType);
          setData(transformedData);
        } else {
          setData([]);
        }
      } catch (error) {
        console.error("Error fetching department reports:", error);
        toast.error(
          t("errors.fetchFailed", "Failed to fetch department reports")
        );
        setData([]);
      } finally {
        setLoading(false);
      }
    },
    [reportType, t, transformApiData]
  );

  // Main data fetching effect
  useEffect(() => {
    // Only fetch data if this component is currently active
    if (!isComponentActive()) {
      return;
    }

    const params = {
      from: startDate ? format(startDate, "yyyy-MM-dd") : null,
      to: endDate ? format(endDate, "yyyy-MM-dd") : null,
    };
    fetchData(params);
  }, [activeTab, reportType, fetchData, isComponentActive]);

  // Effect to save filters to Redux
  useEffect(() => {
    dispatch(
      setDepartmentFilters({
        dateRange,
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
      })
    );
  }, [dateRange, startDate, endDate, dispatch]);

  // React Table instance
  const { getTableProps, getTableBodyProps, rows, prepareRow } = useTable(
    {
      columns,
      data,
    },
    useSortBy
  );

  // Handle filter submission
  const handleFilter = () => {
    // Only fetch data if this component is currently active
    if (!isComponentActive()) {
      return;
    }

    const params = {
      from: startDate ? format(startDate, "yyyy-MM-dd") : null,
      to: endDate ? format(endDate, "yyyy-MM-dd") : null,
    };
    fetchData(params);
  };

  // Handle filter reset
  const handleReset = () => {
    setDateRange("custom");
    setStartDate(null);
    setEndDate(null);

    // Only fetch data if this component is currently active
    if (!isComponentActive()) {
      return;
    }

    const params = {
      from: null,
      to: null,
    };
    fetchData(params);
  };

  const activeFiltersRef = useRef({
    activeFromDateLeads: null,
    activeToDateLeads: null,
  });

  const setActiveStatesForExport = () => {
    activeFiltersRef.current = {
      activeFromDateLeads: startDate,
      activeToDateLeads: endDate,
    };
  };

  const handleExport = () => {
    setActiveStatesForExport();
    const params = {
      from: activeFiltersRef.current.activeFromDateLeads,
      to: activeFiltersRef.current.activeToDateLeads,
    };
    reportType === "leads-service"
      ? ExportLeadsServiceReportApi(params)
      : ExportLeadsPlatformReportApi(params);
  };

  return (
    <div className="department-reports-table">
      <style>{tableStyles}</style>

      {/* Filter Section */}
      <div className="filters-card p-3 mb-3 bg-light border rounded">
        <h5 className="mb-3">{t("filters.title", "Filters")}</h5>
        <Row className="g-3">
          <Col lg={3} md={6} sm={12}>
            <Form.Group>
              <Form.Label>{t("filters.dateRange", "Date Range")}</Form.Label>
              <Form.Select
                value={dateRange}
                onChange={(e) => handleDateRangeChange(e.target.value)}
              >
                {dateRangeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>

          <Col lg={3} md={6} sm={12}>
            <Form.Group>
              <Form.Label>{t("filters.startDate", "Start Date")}</Form.Label>
              <div className="input-group">
                <span className="input-group-text">
                  <FaCalendarAlt />
                </span>
                <Form.Control
                  type="date"
                  value={startDate ? format(startDate, "yyyy-MM-dd") : ""}
                  onChange={(e) =>
                    setStartDate(
                      e.target.value ? new Date(e.target.value) : null
                    )
                  }
                />
              </div>
            </Form.Group>
          </Col>

          <Col lg={3} md={6} sm={12}>
            <Form.Group>
              <Form.Label>{t("filters.endDate", "End Date")}</Form.Label>
              <div className="input-group">
                <span className="input-group-text">
                  <FaCalendarAlt />
                </span>
                <Form.Control
                  type="date"
                  value={endDate ? format(endDate, "yyyy-MM-dd") : ""}
                  onChange={(e) =>
                    setEndDate(e.target.value ? new Date(e.target.value) : null)
                  }
                />
              </div>
            </Form.Group>
          </Col>
        </Row>

        <Row className="mt-3">
          <Col className="d-flex gap-2">
            <Button
              variant="primary"
              onClick={handleFilter}
              className="d-flex align-items-center gap-2"
            >
              <TiArrowForward />
              {t("buttons.filter", "Filter")}
            </Button>
            <Button variant="outline-secondary" onClick={handleReset}>
              {t("buttons.reset", "Reset")}
            </Button>
            <Button
              variant="dark"
              onClick={handleExport}
              className="d-flex align-items-center gap-2"
            >
              <TiArrowForward />
              {t("filters.export", "Export")}
            </Button>
          </Col>
        </Row>
      </div>

      {/* Table Section */}
      <div
        role="region"
        aria-labelledby="department-reports-table"
        tabIndex="0"
      >
        <Table
          {...getTableProps()}
          striped
          bordered
          hover
          className="align-middle"
        >
          <thead>
            {/* Custom header rendering to handle both report types */}
            {(() => {
              if (reportType === "sources") {
                // Sources report: simple single row with Source, Departments, Total
                const firstRowHeaders = [];

                // Add Source column
                firstRowHeaders.push(
                  <th key="source" className="regular-header" title="Source">
                    {t("tables.headers.source", "Source")}
                    <span></span>
                  </th>
                );

                // Add department columns
                departments.forEach((department) => {
                  firstRowHeaders.push(
                    <th
                      key={department}
                      className="regular-header"
                      title={department}
                    >
                      {department}
                      <span></span>
                    </th>
                  );
                });

                // Add Total column
                firstRowHeaders.push(
                  <th
                    key="total"
                    className="regular-header total-column-header"
                    title="Total"
                  >
                    {t("tables.headers.total", "Total")}
                    <span></span>
                  </th>
                );

                return <tr key="header-row-1">{firstRowHeaders}</tr>;
              } else {
                // Statuses report: complex structure with Action 2 grouping
                const action2StartIndex = leadStatuses.findIndex(
                  (status) => status === "Sent SMS"
                );
                const hasAction2 = action2StartIndex >= 0;
                const action2Count = hasAction2
                  ? leadStatuses.length - action2StartIndex
                  : 0;

                // First row headers
                const firstRowHeaders = [];

                // Add Department/Service column
                firstRowHeaders.push(
                  <th
                    key="department"
                    rowSpan={hasAction2 ? 2 : 1}
                    className="regular-header"
                    title="Department/Service"
                  >
                    {t("tables.headers.department", "Department/Service")}
                    <span></span>
                  </th>
                );

                // Add regular status columns (before Action 2)
                if (hasAction2) {
                  for (let i = 0; i < action2StartIndex; i++) {
                    const status = leadStatuses[i];
                    firstRowHeaders.push(
                      <th
                        key={status}
                        rowSpan={2}
                        className="regular-header"
                        title={status}
                      >
                        {status}
                        <span></span>
                      </th>
                    );
                  }
                } else {
                  // No Action 2, add all statuses as regular columns
                  leadStatuses.forEach((status) => {
                    firstRowHeaders.push(
                      <th
                        key={status}
                        className="regular-header"
                        title={status}
                      >
                        {status}
                        <span></span>
                      </th>
                    );
                  });
                }

                // Add Action 2 parent header
                if (hasAction2) {
                  firstRowHeaders.push(
                    <th
                      key="action2-parent"
                      colSpan={action2Count}
                      className="action2-main-header"
                      title="Action 2"
                    >
                      Action 2<span></span>
                    </th>
                  );
                }

                // Add Total column
                firstRowHeaders.push(
                  <th
                    key="total"
                    rowSpan={hasAction2 ? 2 : 1}
                    className="regular-header total-column-header"
                    title="Total"
                  >
                    {t("tables.headers.total", "Total")}
                    <span></span>
                  </th>
                );

                // Second row headers (Action 2 sub-columns only)
                const secondRowHeaders = [];
                if (hasAction2) {
                  for (
                    let i = action2StartIndex;
                    i < leadStatuses.length;
                    i++
                  ) {
                    const status = leadStatuses[i];
                    secondRowHeaders.push(
                      <th
                        key={`action2-${status}`}
                        className="action2-sub-header"
                        title={status}
                        style={{ cursor: "pointer" }}
                      >
                        {status}
                        <span></span>
                      </th>
                    );
                  }
                }

                return (
                  <>
                    <tr key="header-row-1">{firstRowHeaders}</tr>
                    {hasAction2 && (
                      <tr key="header-row-2">{secondRowHeaders}</tr>
                    )}
                  </>
                );
              }
            })()}
          </thead>
          <tbody {...getTableBodyProps()}>
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="text-center">
                  {t("messages.loading", "Loading...")}
                </td>
              </tr>
            ) : rows.length > 0 ? (
              rows.map((row) => {
                prepareRow(row);
                const isGrandTotal =
                  row.original.sourceOrStatus === "Grand Total";
                return (
                  <tr
                    {...row.getRowProps()}
                    className={isGrandTotal ? "table-warning" : ""}
                  >
                    {row.cells.map((cell) => {
                      // Check if this cell is an Action 2 cell by checking if it's a sub-column of Action 2
                      const isAction2Cell =
                        cell.column.parent &&
                        cell.column.parent.headerClassName ===
                          "action2-main-header";

                      // Check if this is the Total column
                      const isTotalCell =
                        cell.column.accessor === "total" ||
                        cell.column.id === "total";

                      // Check if this is the first column (Department/Service)
                      const isFirstCell =
                        cell.column.accessor === "sourceOrStatus";

                      let cellClassName = "";
                      if (isAction2Cell) {
                        cellClassName = "action2-cell";
                      } else if (isTotalCell) {
                        cellClassName = "total-column-cell";
                      } else if (isFirstCell) {
                        cellClassName = "first-column-cell";
                      }

                      return (
                        <td
                          {...cell.getCellProps()}
                          title={cell.value}
                          className={cellClassName}
                        >
                          {cell.render("Cell")}
                        </td>
                      );
                    })}
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={columns.length} className="text-center">
                  {t("messages.noData", "No data available")}
                </td>
              </tr>
            )}
          </tbody>
        </Table>
      </div>
    </div>
  );
};

export default DepartmentReportsTable;
