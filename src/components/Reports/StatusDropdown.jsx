import React from "react";

const StatusDropdown = ({
  ticketId,
  currentStatus,
  onStatusChange,
  isUpdating,
}) => {
  return (
    <div className="text-center d-flex justify-content-center">
      <div className="position-relative w-50">
        <select
          className="form-select form-select-sm"
          value={currentStatus}
          onChange={(e) => {
            onStatusChange(ticketId, e.target.value);
          }}
          disabled={isUpdating}
        >
          <option value="open">Open</option>
          <option value="inprogress">In Progress</option>
          <option value="closed">Closed</option>
        </select>

        {isUpdating && (
          <div className="position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-light bg-opacity-75">
            <div
              className="spinner-border spinner-border-sm text-primary"
              role="status"
            >
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(StatusDropdown);
