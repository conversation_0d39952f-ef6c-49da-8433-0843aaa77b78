import { Pagination } from "react-bootstrap";

const PaginationRecordsForReports = ({
  onPageChange,
  links,
  handlePageSizeChange,
  per_page,
  to,
  total,
  currentPage,
  className,
  pageSizeOptions = [10, 20, 30, 40, 50],
}) => {
  // Use the currentPage prop if provided, otherwise calculate it
  const activePage = currentPage || (to ? Math.ceil(to / per_page) : 1);

  // Update the renderPaginationLinks function to handle all link types
  const renderPaginationLinks = () => {
    const processedLinks = ensureLinksHaveRecordsPerPage(links, per_page);

    return processedLinks?.map((link, index) => {
      if (link.label.includes("Previous")) {
        return (
          <Pagination.Prev
            key={index}
            onClick={() => link.url && onPageChange(link.url)}
            disabled={!link.url}
          >
            <span dangerouslySetInnerHTML={{ __html: link.label }} />
          </Pagination.Prev>
        );
      } else if (link.label.includes("Next")) {
        return (
          <Pagination.Next
            key={index}
            onClick={() => link.url && onPageChange(link.url)}
            disabled={!link.url}
          >
            <span dangerouslySetInnerHTML={{ __html: link.label }} />
          </Pagination.Next>
        );
      } else {
        // For numbered links, check if it's the active page
        const pageNumber = parseInt(link.label, 10);
        const isActive = pageNumber === activePage;

        return (
          <Pagination.Item
            key={index}
            active={isActive || link.active}
            onClick={() => link.url && onPageChange(link.url)}
            disabled={!link.url}
          >
            <span dangerouslySetInnerHTML={{ __html: link.label }} />
          </Pagination.Item>
        );
      }
    });
  };

  const roundToNearestHigherMultiple = (value, multiples) => {
    for (let multiple of multiples) {
      if (value <= multiple) {
        return multiple;
      }
    }
    return multiples[multiples.length - 1]; // In case value is greater than all multiples, return the highest one
  };

  // Calculate remaining records
  const remainingRecords = total - to;

  // Round remaining records to the nearest higher multiple
  const roundedRemainingRecords = roundToNearestHigherMultiple(
    remainingRecords,
    pageSizeOptions
  );

  // Calculate the maximum allowed page size based on the roundedRemainingRecords
  const maxPageSize = Math.min(
    50,
    roundedRemainingRecords > 0 ? roundedRemainingRecords : 10
  );

  // Determine the page size options based on the maxPageSize
  const filteredPageSizeOptions = pageSizeOptions.filter(
    (size) => size <= maxPageSize
  );

  // Add this function to modify links to include the current records per page
  const ensureLinksHaveRecordsPerPage = (links, per_page) => {
    if (!links) return [];

    return links.map((link) => {
      if (!link.url) return link;

      // Check if the URL already has number_of_records parameter
      if (!link.url.includes("number_of_records")) {
        // Add the current records per page to the URL
        const separator = link.url.includes("?") ? "&" : "?";
        link.url = `${link.url}${separator}number_of_records=${per_page}`;
      }

      return link;
    });
  };

  return (
    <div
      className={`d-flex justify-content-center justify-content-lg-between flex-column flex-lg-row align-items-center my-4 mx-3 ${
        className ? className : ""
      }`}
    >
      <div className="d-flex justify-content-between flex-column align-content-center align-items-center pagination-title">
        <div className="mb-1 pagination_title">Records Per Page</div>
        <div className="btn-group records-buttons-container" role="group">
          {filteredPageSizeOptions?.map((pageSizeOption) => (
            <div
              key={pageSizeOption}
              role="button"
              className={`${
                per_page === pageSizeOption
                  ? "record-button-selected"
                  : "record-button"
              }`}
              onClick={() => handlePageSizeChange(pageSizeOption)}
            >
              {pageSizeOption}
            </div>
          ))}
        </div>
      </div>
      <Pagination className="data-table-pagination no-margin">
        {renderPaginationLinks()}
      </Pagination>
    </div>
  );
};
export default PaginationRecordsForReports;
