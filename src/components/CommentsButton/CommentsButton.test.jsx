import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import CommentsButton from "./CommentsButton";
import metaBusinessChatSlice from "../../redux/features/metaBusinessChatSlice";

// Mock store setup
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      metaBusinessSuite: metaBusinessChatSlice,
    },
    preloadedState: {
      metaBusinessSuite: {
        comments: [],
        commentsModal: {
          isOpen: false,
          unreadCount: 0,
          loading: false,
        },
        commentsListeners: {
          unsubscribe: null,
        },
        ...initialState,
      },
    },
  });
};

describe("CommentsButton", () => {
  test("renders without crashing", () => {
    const store = createMockStore();
    render(
      <Provider store={store}>
        <CommentsButton />
      </Provider>
    );

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass("comments-button");
  });

  test("shows unread count badge when there are unread comments", () => {
    const store = createMockStore({
      commentsModal: {
        isOpen: false,
        unreadCount: 5,
        loading: false,
      },
    });

    render(
      <Provider store={store}>
        <CommentsButton />
      </Provider>
    );

    const badge = screen.getByText("5");
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass("notification-badge");

    const button = screen.getByRole("button");
    expect(button).toHaveClass("has-unread");
  });

  test("shows 99+ when unread count exceeds 99", () => {
    const store = createMockStore({
      commentsModal: {
        isOpen: false,
        unreadCount: 150,
        loading: false,
      },
    });

    render(
      <Provider store={store}>
        <CommentsButton />
      </Provider>
    );

    const badge = screen.getByText("99+");
    expect(badge).toBeInTheDocument();
  });

  test("calls onClick handler when provided", () => {
    const mockOnClick = jest.fn();
    const store = createMockStore();

    render(
      <Provider store={store}>
        <CommentsButton onClick={mockOnClick} />
      </Provider>
    );

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  test("is disabled when disabled prop is true", () => {
    const store = createMockStore();

    render(
      <Provider store={store}>
        <CommentsButton disabled={true} />
      </Provider>
    );

    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
  });

  test("shows active state when modal is open", () => {
    const store = createMockStore({
      commentsModal: {
        isOpen: true,
        unreadCount: 0,
        loading: false,
      },
    });

    render(
      <Provider store={store}>
        <CommentsButton />
      </Provider>
    );

    const button = screen.getByRole("button");
    expect(button).toHaveClass("active");
  });
});
