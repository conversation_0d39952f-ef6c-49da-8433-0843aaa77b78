import { Pagination } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import {
  setCurrentPage,
  setRecordsPerPage,
} from "../../redux/features/leadsPaginationSlice";

const PaginationComponent = () => {
  const dispatch = useDispatch();
  const { totalPages, currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      dispatch(setCurrentPage(page));
    }
  };

  const handlePageSizeChange = (size) => {
    dispatch(setRecordsPerPage(size));
  };

  return (
    <div className="d-flex justify-content-between flex-column flex-md-row align-items-center my-4 mx-3">
      {/* Records Per Page */}
      <div className="d-flex flex-column justify-content-center align-items-center">
        <div className="mb-1">Records Per Page</div>
        <div className="btn-group records-buttons-container" role="group">
          {[10, 20, 30, 40, 50].map((size) => (
            <div
              key={size}
              role="button"
              className={`${
                recordsPerPage === size
                  ? "record-button-selected"
                  : "record-button"
              }`}
              onClick={() => handlePageSizeChange(size)}
            >
              {size}
            </div>
          ))}
        </div>
      </div>

      {/* Pagination Controls */}
      <Pagination className="data-table-pagination">
        <Pagination.Prev
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        />
        {Array.from({ length: totalPages }).map((_, index) => {
          const page = index + 1;
          if (
            totalPages <= 5 ||
            page === 1 ||
            page === totalPages ||
            (page >= currentPage - 2 && page <= currentPage + 2)
          ) {
            return (
              <Pagination.Item
                key={page}
                active={currentPage === page}
                onClick={() => handlePageChange(page)}
              >
                {page}
              </Pagination.Item>
            );
          } else if (
            (page === 2 && currentPage > 4) ||
            (page === totalPages - 1 && currentPage < totalPages - 3)
          ) {
            return <Pagination.Ellipsis key={page} />;
          }
          return null;
        })}
        <Pagination.Next
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        />
      </Pagination>
    </div>
  );
};

export default PaginationComponent;
