import { use<PERSON><PERSON>back, useState, useMemo } from "react";
import {
  Button,
  ButtonGroup,
  Col,
  Row,
  OverlayTrigger,
  Tooltip as BSTooltip,
} from "react-bootstrap";
import SearchComponent from "./SearchComponent";
import { IoReturnUpBackOutline } from "react-icons/io5";
import { ReactSVG } from "react-svg";
import { FaFilterCircleXmark, FaUserClock, FaUserPlus } from "react-icons/fa6";
import { MdAssignmentTurnedIn, MdPendingActions } from "react-icons/md";
import { BsGearFill } from "react-icons/bs";
import { LuImport } from "react-icons/lu";
import { PiExportBold } from "react-icons/pi";
import { Tooltip } from "react-tooltip";
import CreateClientModal from "../Modals/CreateClientModal";
import { MdDoneAll, MdCancel } from "react-icons/md";
import { useSelector, useDispatch } from "react-redux";
import ImportLeadsDropZone from "../Modals/ImportLeadsModal";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import "./StatusButtons.css";
import {
  getOrderedSourceKeys,
  getAvailableSources,
} from "../../constants/sourceIcons";
import useClient from "../../redux/hooks/useClient";
import { HiPhoneMissedCall } from "react-icons/hi";
import { TbUserDollar } from "react-icons/tb";
import { getVisibleStatusOptions } from "../../config/leadStatusConfig";
import { isUserExcluded } from "../../config/packageVisibility";

const TableControllers = ({
  abortController,
  setAbortController,
  fetchData,
  handleFilterLeads,
  handleFilterStatus,
  setLoading,
  handleSourceFilter,
  minimal = false,
  nocommunication = false,
  onClearAllFilters,
  loading = false,
}) => {
  // Fetch pagination data from dedicated slice to avoid redundant re-renders and API calls
  const {
    filterStatus,
    leadStatusCounts,
    selectedSource,
    setFilterStatus,
    setSelectedSource,
    handleExportLeads: handleExportLeadsThunk,
    handleImportLeads: handleImportLeadsThunk,
    filterLeadsBySource,
  } = useClient();

  // Keep pagination state source-of-truth in leadsPagination slice
  const { currentPage, recordsPerPage } = useSelector(
    (state) => state.leadsPagination
  );

  const [showImportLeadModal, setShowImportLeadModal] = useState(false);
  const { currentUserPermissions } = useSelector((state) => state.auth);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const handleClose = () => setShowCreateModal(false);
  const handleShow = () => setShowCreateModal(true);
  const { t } = useTranslation();

  const handleImportLeads = (data) => {
    handleImportLeadsThunk(data);
  };

  // Get current user from Redux store
  const { user } = useSelector((state) => state.auth);
  const userId = user?.user?.id;

  // Get user-specific sources (memoized to prevent infinite re-renders)
  const availableSources = useMemo(() => getAvailableSources(userId), [userId]);
  const userOrderedSourceKeys = useMemo(
    () => getOrderedSourceKeys(userId),
    [userId]
  );

  // Get visible status options based on user role
  const visibleStatusOptions = getVisibleStatusOptions(userId);

  // Helper function to get icon for a status
  const getStatusIcon = (statusValue) => {
    const numericStatus = parseInt(statusValue);
    switch (numericStatus) {
      case 0:
        return <MdPendingActions size={20} />;
      case 1:
        return <BsGearFill size={20} />;
      case 2:
        return <MdDoneAll size={20} />;
      case 3:
        return <MdCancel size={20} />;
      case 4:
        return <span style={{ fontWeight: 600 }}>WL</span>;
      case 5:
        return <span style={{ fontWeight: 600 }}>NQ</span>;
      case 6:
        return <HiPhoneMissedCall size={20} />;
      case 7:
        return <TbUserDollar size={20} />;
      case 8:
        return <span style={{ fontWeight: 600 }}>BR</span>;
      case 9:
        return <MdCancel size={20} style={{ transform: "rotate(45deg)" }} />;
      case 10:
        return <span style={{ fontWeight: 600 }}>QT</span>;
      case 11:
        return <MdAssignmentTurnedIn size={20} />;
      // Add icons for new statuses (12-26) as needed
      case 12:
        return <span style={{ fontWeight: 600 }}>UN</span>;
      case 13:
        return <span style={{ fontWeight: 600 }}>AP</span>;
      case 14:
        return <span style={{ fontWeight: 600 }}>FU</span>;
      case 15:
        return <span style={{ fontWeight: 600 }}>NI</span>;
      case 16:
        return <span style={{ fontWeight: 600 }}>JK</span>;
      case 17:
        return <span style={{ fontWeight: 600 }}>CP</span>;
      case 18:
        return <span style={{ fontWeight: 600 }}>UC</span>;
      case 19:
        return <span style={{ fontWeight: 600 }}>CB</span>;
      case 20:
        return <span style={{ fontWeight: 600 }}>BA2</span>;
      case 21:
        return <span style={{ fontWeight: 600 }}>SMS</span>;
      case 22:
        return <span style={{ fontWeight: 600 }}>NA</span>;
      case 23:
        return <span style={{ fontWeight: 600 }}>NI2</span>;
      case 24:
        return <span style={{ fontWeight: 600 }}>WAP</span>;
      case 25:
        return <span style={{ fontWeight: 600 }}>FU2</span>;
      case 26:
        return <span style={{ fontWeight: 600 }}>CP2</span>;
      default:
        return <span style={{ fontWeight: 600 }}>?</span>;
    }
  };

  // Helper function to get color for a status
  const getStatusColor = (statusValue) => {
    const numericStatus = parseInt(statusValue);
    switch (numericStatus) {
      case 0:
        return "#ffc107"; // Pending - yellow
      case 1:
        return "#007bff"; // In Progress - blue
      case 2:
        return "#28a745"; // Completed - green
      case 3:
        return "#dc3545"; // Rejected - red
      case 4:
        return "#b8860b"; // Wrong Lead - dark goldenrod
      case 5:
        return "#6f42c1"; // Not Qualified - purple
      case 6:
        return "#17a2b8"; // No Communication - teal
      case 7:
        return "#28a745"; // Booked - green
      case 8:
        return "#20c997"; // Booked and Reserved - teal
      case 9:
        return "#adb5bd"; // Canceled - gray
      case 10:
        return "#fd7e14"; // Quotation - orange
      case 11:
        return "#6610f2"; // Assigned - indigo
      // Colors for new statuses (12-26)
      case 12:
        return "#6c757d"; // Undefined - gray
      case 13:
        return "#28a745"; // Advance Paid - green
      case 14:
        return "#007bff"; // Follow Up - blue
      case 15:
        return "#dc3545"; // Not Interested - red
      case 16:
        return "#6c757d"; // Junk - gray
      case 17:
        return "#dc3545"; // Complaints - red
      case 18:
        return "#dc3545"; // Urgent Call - red
      case 19:
        return "#ffc107"; // Call Back - yellow
      case 20:
        return "#28a745"; // Booked ACTION 2 - green
      case 21:
        return "#17a2b8"; // Sent SMS - teal
      case 22:
        return "#6c757d"; // No Action - gray
      case 23:
        return "#dc3545"; // Not interested ACTION 2 - red
      case 24:
        return "#17a2b8"; // Whatapp-NAL - teal
      case 25:
        return "#007bff"; // Follow up ACTION 2 - blue
      case 26:
        return "#dc3545"; // Complain ACTION 2 - red
      default:
        return "#6c757d"; // Default - gray
    }
  };

  // Create status options for NoCommunicationLeads
  const noCommStatusOptions = [
    {
      key: "all",
      label: t("leadsTable.columns.all"),
      value: "all",
      color: "#6c757d",
      tooltip: t("leadsTable.columns.all"),
    },
    // Filter visible statuses for no communication view
    ...Object.entries(visibleStatusOptions)
      .filter(([value]) => {
        // Include only specific statuses for no communication view
        const numericValue = parseInt(value);
        // For special users, include extended statuses that are relevant for no communication
        if (isUserExcluded(userId)) {
          // Include basic statuses and relevant extended statuses for special users
          return [1, 3, 4, 5, 6, 7, 9, 11, 14, 15, 17, 18, 19, 25, 26].includes(
            numericValue
          );
        }
        // For regular users, only include basic statuses
        return [1, 3, 4, 5, 6, 7, 9, 11].includes(numericValue);
      })
      .map(([value, label]) => {
        const numericValue = parseInt(value);
        const key = label.toLowerCase().replace(/\s+/g, "_");

        // Get count based on status value
        let count = 0;
        switch (numericValue) {
          case 1:
            count = leadStatusCounts?.inprogress ?? 0;
            break;
          case 3:
            count = leadStatusCounts?.rejected ?? 0;
            break;
          case 4:
            count = leadStatusCounts?.wrong_lead ?? 0;
            break;
          case 5:
            count = leadStatusCounts?.not_qualified ?? 0;
            break;
          case 6:
            count = leadStatusCounts?.no_communication ?? 0;
            break;
          case 7:
            count = leadStatusCounts?.booked ?? 0;
            break;
          case 9:
            count = leadStatusCounts?.canceled ?? 0;
            break;
          case 11:
            count = leadStatusCounts?.assigned ?? 0;
            break;
          // Extended statuses for special users in no communication view
          case 14:
            count = leadStatusCounts?.follow_up ?? 0;
            break;
          case 15:
            count = leadStatusCounts?.not_interested ?? 0;
            break;
          case 17:
            count = leadStatusCounts?.complaints ?? 0;
            break;
          case 18:
            count = leadStatusCounts?.urgent_call ?? 0;
            break;
          case 19:
            count = leadStatusCounts?.call_back ?? 0;
            break;
          case 25:
            count = leadStatusCounts?.follow_up_action_2 ?? 0;
            break;
          case 26:
            count = leadStatusCounts?.complain_action_2 ?? 0;
            break;
          default:
            count = 0;
        }

        return {
          key,
          label: getStatusIcon(value),
          value: numericValue,
          color: getStatusColor(value),
          count,
          tooltip: t(
            `tables.status.${label.toLowerCase().replace(/\s+/g, "")}`
          ),
        };
      }),
  ];

  // Create status options for regular view
  const statusOptions = nocommunication
    ? noCommStatusOptions
    : [
        {
          key: "all",
          label: t("leadsTable.columns.all"),
          value: "all",
          color: "#6c757d",
          tooltip: t("leadsTable.columns.all"),
        },
        // Map visible statuses to status options
        ...Object.entries(visibleStatusOptions).map(([value, label]) => {
          const numericValue = parseInt(value);
          const key = label.toLowerCase().replace(/\s+/g, "");

          // Get count based on status value
          let count = 0;
          switch (numericValue) {
            case 0:
              count = leadStatusCounts?.pendding ?? 0;
              break;
            case 1:
              count = leadStatusCounts?.inprogress ?? 0;
              break;
            case 2:
              count = leadStatusCounts?.completed ?? 0;
              break;
            case 3:
              count = leadStatusCounts?.rejected ?? 0;
              break;
            case 4:
              count = leadStatusCounts?.wrong_lead ?? 0;
              break;
            case 5:
              count = leadStatusCounts?.not_qualified ?? 0;
              break;
            case 6:
              count = leadStatusCounts?.no_communication ?? 0;
              break;
            case 7:
              count = leadStatusCounts?.booked ?? 0;
              break;
            case 8:
              count = leadStatusCounts?.booked_and_reserved ?? 0;
              break;
            case 9:
              count = leadStatusCounts?.canceled ?? 0;
              break;
            case 10:
              count = leadStatusCounts?.quotation ?? 0;
              break;
            case 11:
              count = leadStatusCounts?.assigned ?? 0;
              break;
            // Extended statuses for special users (12-26)
            case 12:
              count = leadStatusCounts?.undefined ?? 0;
              break;
            case 13:
              count = leadStatusCounts?.advance_paid ?? 0;
              break;
            case 14:
              count = leadStatusCounts?.follow_up ?? 0;
              break;
            case 15:
              count = leadStatusCounts?.not_interested ?? 0;
              break;
            case 16:
              count = leadStatusCounts?.junk ?? 0;
              break;
            case 17:
              count = leadStatusCounts?.complaints ?? 0;
              break;
            case 18:
              count = leadStatusCounts?.urgent_call ?? 0;
              break;
            case 19:
              count = leadStatusCounts?.call_back ?? 0;
              break;
            case 20:
              count = leadStatusCounts?.booked_action_2 ?? 0;
              break;
            case 21:
              count = leadStatusCounts?.sent_sms ?? 0;
              break;
            case 22:
              count = leadStatusCounts?.no_action ?? 0;
              break;
            case 23:
              count = leadStatusCounts?.not_interested_action_2 ?? 0;
              break;
            case 24:
              count = leadStatusCounts?.whatapp_nal ?? 0;
              break;
            case 25:
              count = leadStatusCounts?.follow_up_action_2 ?? 0;
              break;
            case 26:
              count = leadStatusCounts?.complain_action_2 ?? 0;
              break;
            default:
              count = 0;
          }

          return {
            key,
            label: getStatusIcon(value),
            value: numericValue,
            color: getStatusColor(value),
            count,
            tooltip: t(
              `tables.status.${label.toLowerCase().replace(/\s+/g, "")}`
            ),
          };
        }),
      ];

  // Add a state to track the search input value
  const [searchValue, setSearchValue] = useState("");

  const handleClearFilters = () => {
    setSelectedSource(null);
    setFilterStatus("all");
    setSearchValue(""); // Reset search value

    // For no communication component, use the onClearAllFilters callback
    // to avoid duplicate API calls
    if (typeof onClearAllFilters === "function") {
      onClearAllFilters();
    } else {
      // Only call fetchData directly if onClearAllFilters is not provided
      if (abortController) abortController.abort();
      const controller = new AbortController();
      setAbortController(controller);
      fetchData(controller);
    }
  };

  const handleSearch = useCallback(
    async (term, controller, isCancellation) => {
      if (term !== "" && !isCancellation) {
        // Return the promise from handleFilterLeads
        try {
          await handleFilterLeads(term, controller);
          // Search is complete, loading state should be handled by handleFilterLeads
        } catch (error) {
          if (error.name !== "AbortError") {
            console.error("Search error:", error);
          }
        }
      } else {
        // Cancellation or empty search term: reset list
        if (abortController) abortController.abort();

        const controller = new AbortController();
        setAbortController(controller);

        if (isCancellation || term === "") {
          // Always refetch original data
          await fetchData(controller);
          // Reset external search value state if provided
          setSearchValue("");
        }
      }
    },
    [abortController, handleFilterLeads, fetchData, setSearchValue]
  );

  const handleSourceIconFilterClick = async (source) => {
    setSelectedSource(source);
    // Check if handleSourceFilter exists before calling it
    if (handleSourceFilter) {
      handleSourceFilter(source);
    } else {
      // Fallback to direct API call if handleSourceFilter is not provided
      setLoading(true);
      try {
        filterLeadsBySource({
          status: filterStatus !== "all" ? filterStatus.toString() : null,
          source: source.toString(),
          recordsPerPage,
          page: currentPage,
        });
      } catch (error) {
        console.error("Error fetching leads:", error);
      } finally {
        setLoading(false);
      }
    }
  };

  // Get clientId from URL params if available (for admin context)
  const params = useParams();
  const clientId = params.id;

  const handleExportLeads = () => {
    // Pass clientId if available (admin context), otherwise undefined (regular user context)
    handleExportLeadsThunk(clientId);
  };

  // Common button render function
  const renderStatusButton = (status, isMobile = false) => {
    const isActive = filterStatus === status.value;
    const button = (
      <Button
        key={status.key}
        variant="link"
        className={`status-button ${
          isActive ? "status-button-active" : "status-button-default"
        }`}
        style={{
          background: isActive ? status.color : "transparent",
          borderColor: status.color,
          borderWidth: "1.5px",
          borderStyle: "solid",
          transition: "all 0.3s ease",
          textDecoration: "none",
        }}
        onMouseEnter={(e) => {
          if (!isActive) {
            e.currentTarget.style.background = status.color;
            e.currentTarget.style.color = "#fff";
          }
        }}
        onMouseLeave={(e) => {
          if (!isActive) {
            e.currentTarget.style.background = "transparent";
            e.currentTarget.style.color = status.color;
          }
        }}
        disabled={loading}
        onClick={() => {
          if (loading) return;
          handleFilterStatus(status.value);
          setFilterStatus(status.value);
          if (isMobile) {
            setSearchValue("");
          }
        }}
      >
        <span
          style={{
            color: isActive ? "#fff" : status.color,
            marginRight: "4px",
            display: "inline-flex",
            alignItems: "center",
          }}
        >
          {status.label}
        </span>
        <span
          style={{
            color: isActive ? "#fff" : status.color,
          }}
        >
          {status.count}
        </span>
      </Button>
    );
    return status.tooltip ? (
      <OverlayTrigger
        key={status.key}
        placement="top"
        overlay={
          <BSTooltip id={`tooltip-${status.key}`}>{status.tooltip}</BSTooltip>
        }
      >
        <span>{button}</span>
      </OverlayTrigger>
    ) : (
      button
    );
  };

  return (
    <>
      <CenteredModal
        size={"lg"}
        show={showImportLeadModal}
        children={
          <ImportLeadsDropZone
            handleClose={() => setShowImportLeadModal(false)}
            handleImportLeads={handleImportLeads}
          />
        }
        onHide={() => setShowImportLeadModal(false)}
      />
      {/* Always show status filter row */}
      <Row
        className={
          "justify-content-center align-items-center justify-content-md-between"
        }
      >
        <Col lg={9}>
          <ButtonGroup className="mb-2 flex-wrap status-btns">
            {statusOptions.map((status) => renderStatusButton(status, false))}
          </ButtonGroup>
        </Col>
        <Col lg={3}>
          <SearchComponent
            onSearch={handleSearch}
            handleFilterLeads={handleFilterLeads}
            placeholder={t("tableControls.placeholders.searchTable")}
            value={searchValue}
            setValue={setSearchValue}
            abortController={abortController}
            setAbortController={setAbortController}
            disabled={loading}
          />
        </Col>
      </Row>
      <Row className={"mt-3 justify-content-between align-items-center"}>
        <Col xl={6} lg={8} className="social-filter-wrapper my-3">
          <div className="social-filter-container">
            <div
              onClick={() => {
                if (loading) return;
                handleClearFilters();
              }}
              className={`reset${
                selectedSource === null ? " reset-selected" : ""
              }${loading ? " disabled" : ""}`}
              style={{
                pointerEvents: loading ? "none" : "auto",
                opacity: loading ? 0.5 : 1,
              }}
            >
              {selectedSource === null ? (
                t("leadsTable.columns.leadsfrom")
              ) : (
                <div
                  className={
                    "d-flex justify-content-between align-items-center"
                  }
                >
                  <div className={"me-2"}>
                    <IoReturnUpBackOutline size={20} />
                  </div>
                  <div>{t("leadsTable.columns.backToAll")}</div>
                </div>
              )}
            </div>
            {userOrderedSourceKeys.map((source, index) => (
              <div
                key={index}
                className={`social-icon${
                  selectedSource === source ? " selected" : ""
                }${loading ? " disabled" : ""}`}
                onClick={() => {
                  if (loading) return;
                  handleSourceIconFilterClick(source);
                }}
                style={{
                  pointerEvents: loading ? "none" : "auto",
                  opacity: loading ? 0.5 : 1,
                }}
              >
                <ReactSVG src={availableSources[source]} />
              </div>
            ))}
          </div>
        </Col>
        <Col
          className={
            minimal
              ? "d-flex justify-content-lg-end align-items-center"
              : "d-flex justify-content-center justify-content-lg-end align-items-center"
          }
          xl={6}
          lg={4}
        >
          {!minimal && (
            <div
              className={`clear-filter${loading ? " disabled" : ""}`}
              onClick={() => {
                if (loading) return;
                handleClearFilters();
              }}
              style={{
                pointerEvents: loading ? "none" : "auto",
                opacity: loading ? 0.5 : 1,
              }}
            >
              <FaFilterCircleXmark size={25} />
            </div>
          )}
          {!minimal && (
            <>
              {currentUserPermissions?.includes("lead-import") && (
                <span
                  className={"import-leads"}
                  onClick={() => setShowImportLeadModal(true)}
                >
                  <LuImport size={25} />
                </span>
              )}
              {currentUserPermissions?.includes("lead-export") && (
                <span className={"export-leads"} onClick={handleExportLeads}>
                  <PiExportBold size={25} />
                </span>
              )}
              {currentUserPermissions?.includes("lead-create") && (
                <div className={"add-lead"} onClick={handleShow}>
                  <FaUserPlus size={25} />
                </div>
              )}
              <Tooltip
                anchorSelect=".clear-filter"
                content={t("tableControls.tooltips.clearFilters")}
              />
              <Tooltip
                anchorSelect=".add-lead"
                content={t("tableControls.tooltips.addRecord")}
              />
              <Tooltip
                anchorSelect=".import-leads"
                content={t("tableControls.tooltips.importData")}
              />
              <Tooltip
                anchorSelect=".export-leads"
                content={t("tableControls.tooltips.exportData")}
              />
              <CreateClientModal show={showCreateModal} onHide={handleClose} />
            </>
          )}
        </Col>
      </Row>
    </>
  );
};

export default TableControllers;
