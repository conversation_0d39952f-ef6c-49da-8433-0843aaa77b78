import Container from "react-bootstrap/Container";
import Modal from "react-bootstrap/Modal";
import { Button, Row } from "react-bootstrap";
import Form from "react-bootstrap/Form";
import { Formik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useDispatch, useSelector } from "react-redux";
import { setTeamMembers } from "../../redux/features/clientSlice";
import adminService from "../../services/admin";

const EditAdminMemberModal = (props) => {
  const { showEditModal, handleClose, modalData: memberData } = props;  
  const dispatch = useDispatch();
  const { roles } = useSelector((state) => state.role);

  // First get the current team members from state
  const { teamMembers } = useSelector((state) => state.client);

  const handleSubmit = async (values, { setSubmitting }) => {
    if (!memberData) {
      handleClose();
      return;
    }

    // Check for changes in name, email, and phone
    const nameChanged = values.name !== memberData.values.name;
    const emailChanged = values.email !== memberData.values.email;
    const phoneChanged = values.phone !== memberData.values.phone;
    const roleChanged = values.role !== memberData.values.role;

    // Check if any of the fields have changed
    if (!nameChanged && !emailChanged && !phoneChanged && !roleChanged) {
      toast.info("No changes detected.", {
        position: "bottom-right",
        theme: "dark",
      });
      setSubmitting(false);
      return;
    }
    if (values.password !== "") {
      values.new_password = values.password.trim();
    } else {
      delete values.password;
    }
    await adminService.editAdminMemberApi(memberData.id, values);
    const updatedTeamMembers = teamMembers.map((teamMember) => {
      if (teamMember.id === memberData.id) {
        return {
          ...teamMember,
          name: values.name,
          email: values.email,
          phone: values.phone,
          role: values.role,
        };
      }
      return teamMember;
    });
    dispatch(setTeamMembers(updatedTeamMembers));
    handleClose();
    setSubmitting(false);
  };

  const editClientValidationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    phone: Yup.string().required("Phone is required"),
  });
  const parsedRoleNames = roles
    .filter((role) => role.show_name !== "Owner")
    .map((role) => {
      const roleMap = {
        "owner-Admin": "Admin",
        "owner-moderator": "Moderator",
        "owner-sales": "Sales",
        "owner-accountant": "Accountant",
      };
      return {
        value: role.show_name,
        name: roleMap[role.show_name] || role.show_name,
      };
    });
  return (
    <Modal
      {...props}
      show={showEditModal}
      aria-labelledby="contained-modal-title-vcenter"
      centered
      // className={"integration-modal admin-theme"}
    >
      <Formik
        initialValues={{
          name: memberData?.values?.name || "",
          email: memberData?.values?.email || "",
          phone: memberData?.values?.phone || "",
          password: "",
          role: memberData?.values?.role || "",
        }}
        onSubmit={(values, { setSubmitting }) =>
          handleSubmit(values, { setSubmitting })
        }
        validationSchema={editClientValidationSchema}
      >
        {({ handleSubmit, handleChange, values, touched, errors }) => (
          <Form
            noValidate
            onSubmit={handleSubmit}
            className={"admin-theme text-white"}
          >
            <Container>
              <Row>
                <div
                  className={
                    "d-flex align-items-center flex-column text-center my-3"
                  }
                >
                  <h2>Edit Data for</h2>
                  <span>{memberData?.name}</span>
                </div>
                <Form.Group controlId={"name"} className={"mb-3"}>
                  <Form.Label>Name</Form.Label>
                  <Form.Control
                    name={"name"}
                    className={"mb-3"}
                    type="text"
                    placeholder="Name"
                    value={values.name || ""}
                    onChange={handleChange}
                    isValid={touched.name && !errors.name}
                    isInvalid={touched.name && errors.name}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.name}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group controlId={"email"} className={"mb-3"}>
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    name={"email"}
                    className={"mb-3"}
                    type="email"
                    placeholder="Email"
                    value={values.email}
                    onChange={handleChange}
                    isValid={touched.email && !errors.email}
                    isInvalid={touched.email && errors.email}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.email}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group controlId={"phone"} className={"mb-3"}>
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    name={"phone"}
                    className={"mb-3"}
                    type="text"
                    placeholder="Phone"
                    value={values.phone || ""}
                    onChange={handleChange}
                    isValid={touched.phone && !errors.phone}
                    isInvalid={touched.phone && errors.phone}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.phone}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group>
                  <Form.Label>Password</Form.Label>
                  <Form.Control
                    name={"password"}
                    className={"mb-3"}
                    type="password"
                    placeholder="Password"
                    value={values.password}
                    onChange={handleChange}
                    isValid={touched.password && !errors.password}
                    isInvalid={touched.password && errors.password}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.password}
                  </Form.Control.Feedback>
                  <Form.Text className={"text-muted"}>
                    Leave blank if you don't want to change the password.
                  </Form.Text>
                </Form.Group>
                <Form.Group controlId={"role"}>
                  <Form.Label>Role</Form.Label>
                  <Form.Select
                    name={"role"}
                    value={values.role}
                    onChange={handleChange}
                  >
                    {parsedRoleNames.map((role) => (
                      <option key={role?.value} value={role?.value}>
                        {role?.name}
                      </option>
                    ))}
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    {errors.role}
                  </Form.Control.Feedback>
                  <Form.Text className={"text-muted"}>
                    You can't change the role of the admin.
                  </Form.Text>
                </Form.Group>
              </Row>
              <center className={"my-3"}>
                <Button
                  className={"submit-btn text-uppercase fs-6 py-2 px-3"}
                  type={"submit"}
                >
                  Save
                </Button>
                <Button
                  className={"text-uppercase ms-3 rounded-pill"}
                  variant={"outline-danger"}
                  onClick={handleClose}
                >
                  Cancel
                </Button>
              </center>
            </Container>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditAdminMemberModal;
