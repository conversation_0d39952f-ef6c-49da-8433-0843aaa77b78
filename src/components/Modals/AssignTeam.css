.blur-modal {
    background: rgba(0, 0, 0, 0.30);
    backdrop-filter: blur(25px);
}

.blur-modal .modal-content {
    border-radius: 20px;
    background: #FFF;
    padding: 1.25rem;
}

.team-member-row {
    border-radius: 24px;
    background: #FFF;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.15);
    padding: 1rem;
    cursor: pointer;
}

.team-member-row:hover {
    border-radius: 24px;
    border: 1px solid #94C11F;
    background: #FCFFF3;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.15);
}

.team-member-row.selected {
    border-radius: 24px;
    border: 1px solid #94C11F;
    background: #FCFFF3;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.15);
}

.team-member-row:hover .member-email{
    color: rgba(0, 0, 0, 0.55);
}

.confirm-button {
    border-radius: 25px;
    background: linear-gradient(259deg, #92C020 5.35%, #CAD511 193.42%);
    color: white;
    padding: 5px 10px;
    /*margin: 0 auto;*/
    width: fit-content;
    cursor: pointer;
}