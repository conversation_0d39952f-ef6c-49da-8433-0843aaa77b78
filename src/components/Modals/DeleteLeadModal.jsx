import { Button } from "react-bootstrap";
import leadService from "../../services/leads";
import { useNavigate, useParams } from "react-router-dom";

const DeleteLeadModalContent = ({ onHide }) => {
  const navigate = useNavigate();
  const params = useParams();
  return (
    <div
      className={"d-flex flex-column justify-content-around align-items-center"}
    >
      <h3 className={"fw-bold"}>Are you sure to delete this Lead?</h3>
      <div className={"d-flex justify-content-center"}>
        <Button variant={"secondary"} className={"me-2"} onClick={onHide}>
          Cancel
        </Button>
        <Button
          variant={"danger"}
          onClick={() => {
            leadService
              .deleteSingleLeadApi(params.id)
              .then(() => {
                navigate(-1);
              })
              .catch((err) => {
                console.log(err);
              });
          }}
        >
          Delete
        </Button>
      </div>
    </div>
  );
};

export default DeleteLeadModalContent;
