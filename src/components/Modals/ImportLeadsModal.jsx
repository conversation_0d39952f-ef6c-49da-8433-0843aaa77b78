import { useState } from "react";
import { useDropzone } from "react-dropzone";
import { useDispatch } from "react-redux";
import { importLeads } from "../../redux/features/clientSlice";
import { Button } from "../Styles/SignIn_SignUp/SignIn_SignUp_Client.styles";
import { FaTimes, FaFileExcel } from "react-icons/fa";
import "./import-leads-modal.css";
import { useTranslation } from "react-i18next";

const ImportLeadsDropZone = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const handleImportLeads = (data) => dispatch(importLeads(data));
  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileError, setFileError] = useState("");
  const [isFilesAccepted, setIsFilesAccepted] = useState(false);

  const handleRemoveFile = (e) => {
    e.stopPropagation();
    setUploadedFile(null);
    setIsFilesAccepted(false);
    setFileError("");
  };

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "application/vnd.ms-excel": [".csv", ".xls", ".xlsx"],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xls",
        ".xlsx",
      ],
      "text/csv": [".csv"],
    },
    onDropAccepted: (acceptedFiles) => {
      const file = acceptedFiles[0];
      if (!file) {
        setFileError(t("import.pleaseUploadFile"));
        return;
      }

      const acceptedFormats = [".xls", ".xlsx", ".csv"];
      const fileExtension = file.name.split(".").pop().toLowerCase();
      if (!acceptedFormats.includes("." + fileExtension)) {
        setFileError(t("import.pleaseUploadValidExcel"));
        setUploadedFile(null);
        setIsFilesAccepted(false);
        return;
      }

      setFileError("");
      setUploadedFile(file);
      setIsFilesAccepted(true);
    },
    onDropRejected: (fileRejections) => {
      const fileRejection = fileRejections[0];
      if (fileRejection.errors[0].code === "file-invalid-type") {
        setFileError(t("import.pleaseUploadValidExcel"));
        setUploadedFile(null);
        setIsFilesAccepted(false);
      }
    },
  });

  const handleSubmit = () => {
    if (!uploadedFile) {
      setFileError(t("import.pleaseUploadFile"));
      return;
    }
    const data = { file: uploadedFile };
    handleImportLeads(data);
  };

  const dropzoneClassName = `dropzone-container ${fileError ? "error" : ""} ${
    isFilesAccepted ? "active" : ""
  }`;

  return (
    <section className="container d-flex flex-column align-items-center justify-content-between py-4">
      <h2 className={"mb-4"}>{t("import.selectFileToImport")}</h2>
      <div {...getRootProps({ className: dropzoneClassName })}>
        <input {...getInputProps()} />
        {!uploadedFile ? (
          <div className="upload-placeholder">
            <p>{t("import.dragDrop")}</p>
            <em>({t("import.onlyAcceptFiles")})</em>
          </div>
        ) : (
          <div className="file-preview-container">
            <div className="file-header">
              <span className="file-name">{uploadedFile.name}</span>
              <button
                className="remove-file-btn"
                onClick={handleRemoveFile}
                type="button"
              >
                <FaTimes />
              </button>
            </div>
            <div className="preview-content">
              <div className="document-preview">
                <FaFileExcel size={40} className="text-success" />
                <p>{uploadedFile.name}</p>
                <small className="text-muted">
                  {(uploadedFile.size / 1024).toFixed(2)} KB
                </small>
              </div>
            </div>
          </div>
        )}
      </div>
      {fileError && <p className="error-message">{fileError}</p>}
      <Button onClick={handleSubmit}>{t("common.submit")}</Button>
    </section>
  );
};

export default ImportLeadsDropZone;
