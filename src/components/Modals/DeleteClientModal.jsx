import { But<PERSON>, Spinner } from "react-bootstrap";
import { useState } from "react";

const DeleteClientModal = (props) => {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    setLoading(true);
    try {
      await props
        .deleteClientFunction({
          clientId: props.id,
          handleClose: props.handleClose,
          afterDelete: props?.afterDelete,
        })
        .unwrap();

      // This line should close the modal after successful deletion
      // Let's make sure it's being called
      props.handleClose();
    } catch (error) {
      console.error("Error deleting client:", error);
      // Optionally show a toast or error message here
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className={"d-flex flex-column justify-content-around align-items-center"}
    >
      <h3 className={"fw-bold text-center my-5"}>{props.title}</h3>
      <div className={"d-flex justify-content-center"}>
        <Button
          variant={"secondary"}
          className={"me-3 rounded-pill"}
          onClick={() => props.handleClose()}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          variant={"danger"}
          className={"rounded-pill"}
          onClick={handleDelete}
          disabled={loading}
        >
          {loading ? <Spinner size="sm" animation="border" /> : "Delete"}
        </Button>
      </div>
    </div>
  );
};

export default DeleteClientModal;
