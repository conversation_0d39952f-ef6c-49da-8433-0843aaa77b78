import { Button, Modal } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { FaExclamationTriangle } from "react-icons/fa";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";

const SubscriptionRequiredModal = ({ show, onHide }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { user } = useSelector((state) => state.auth);

  // Don't show modal for excluded users
  if (!shouldShowPackageFeatures(user?.user?.id)) {
    return null;
  }

  const handleRedirectToPackages = () => {
    navigate("/packages");
    if (onHide) onHide();
  };

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      backdrop="static"
      keyboard={false}
    >
      <Modal.Header>
        <Modal.Title className="d-flex align-items-center">
          <FaExclamationTriangle className="text-warning me-2" />
          {t("subscription.requiredModal.title") || "Subscription Required"}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p>
          {t("subscription.requiredModal.message") ||
            "You need to subscribe to a package to use the system."}
        </p>
        <p>
          {t("subscription.requiredModal.subMessage") ||
            "Please choose a subscription package to continue using all features."}
        </p>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={handleRedirectToPackages}>
          {t("subscription.requiredModal.viewPackages") || "View Packages"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default SubscriptionRequiredModal;
