import { <PERSON><PERSON>, Container, Modal } from "react-bootstrap";
import "./CreateClient.css";
import Form from "react-bootstrap/Form";
import { ReactSVG } from "react-svg";
import { useDispatch, useSelector } from "react-redux";
import { isUserExcluded } from "../../config/packageVisibility";
import {
  getOrderedSourceKeys,
  getAvailableSources,
} from "../../constants/sourceIcons";
import * as Yup from "yup";
import { Formik } from "formik";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { addLead } from "../../redux/features/clientSlice";

const CreateClientModal = (props) => {
  const dispatch = useDispatch();
  const clientId = useParams().id;
  const { t } = useTranslation();
  const { show, onHide } = props;
  const { user } = useSelector((state) => state.auth);

  // Get user-specific sources
  const availableSources = getAvailableSources(user?.user?.id);
  const userOrderedSourceKeys = getOrderedSourceKeys(user?.user?.id);

  // Service options for admin users
  const serviceOptions = [
    "Laser",
    "Hollywood Smile",
    "Derma",
    "Skin Care",
    "Dental Implant",
    "Dental",
    "Orthodontics",
    "Slimming",
    "Gyne",
  ];

  const validationSchema = Yup.object().shape({
    name: Yup.string().required(t("createClient.validation.nameRequired")),
    phone: Yup.string()
      .test({
        name: "is-phone-valid",
        message: t("createClient.validation.invalidPhone"),
        test: function (value) {
          // Allow empty phone number if there is an email
          if (!value && this.parent.email && this.parent.email.length > 0) {
            return true;
          }
          if (typeof value === "string") {
            return /^\+?[0-9\s-]*$/.test(value);
          }
        },
      })
      .test({
        name: "at-least-one-contact",
        exclusive: true,
        message: t("createClient.validation.contactRequired"),
        test: function (value) {
          const email = this.parent.email;
          return !value || value.length > 0 || (email && email.length > 0);
        },
      }),
    email: Yup.string()
      .email(t("createClient.validation.invalidEmail"))
      .test({
        name: "at-least-one-contact",
        exclusive: true,
        message: t("createClient.validation.contactRequired"),
        test: function (value) {
          const phone = this.parent.phone;
          return (value && value.length > 0) || (phone && phone.length > 0);
        },
      }),
    service: Yup.string(),
    source: Yup.number().required(t("createClient.validation.sourceRequired")),
  });

  return (
    <>
      <Modal
        show={show}
        onHide={onHide}
        className={"create-client-modal"}
        centered
      >
        <Container className={`p-4 create-client-form ${props.className}`}>
          <Formik
            initialValues={{
              name: "",
              phone: "",
              email: "",
              service: "",
              source: null,
              notes: "",
              client_id: clientId,
            }}
            validationSchema={validationSchema}
            onSubmit={(values, { resetForm }) => {
              dispatch(addLead({ values, onHide, resetForm }));
            }}
          >
            {({ errors, touched, handleChange, handleSubmit, values }) => (
              <Form noValidate onSubmit={handleSubmit} autoComplete="off">
                <div className={"text-center fw-bold mb-3"}>
                  {t("createClient.title")}
                </div>
                <Form.Group className={"mb-3"}>
                  <Form.Label>{t("createClient.labels.name")}</Form.Label>
                  <Form.Control
                    type={"text"}
                    value={values.name}
                    isInvalid={touched.name && errors.name}
                    onChange={handleChange}
                    name={"name"}
                    placeholder={t("createClient.placeholders.name")}
                    autoComplete="off"
                    data-form-type="other"
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.name}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                  <Form.Label>{t("createClient.labels.phone")}</Form.Label>
                  <Form.Control
                    type={"tel"}
                    value={values.phone}
                    isInvalid={touched.phone && errors.phone}
                    onChange={handleChange}
                    name={"phone"}
                    placeholder={t("createClient.placeholders.phone")}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.phone}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                  <Form.Label>{t("createClient.labels.email")}</Form.Label>
                  <Form.Control
                    type={"email"}
                    value={values.email}
                    isInvalid={touched.email && errors.email}
                    onChange={handleChange}
                    name={"email"}
                    placeholder={t("createClient.placeholders.email")}
                    autoComplete="off"
                    data-form-type="other"
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.email}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                  <Form.Label>{t("createClient.labels.service")}</Form.Label>
                  {isUserExcluded(user?.user?.id) ? (
                    <Form.Select
                      value={values.service}
                      isInvalid={touched.service && errors.service}
                      onChange={handleChange}
                      name={"service"}
                    >
                      <option value="">
                        {t("createClient.placeholders.service")}
                      </option>
                      {serviceOptions.map((option, index) => (
                        <option key={index} value={option}>
                          {option}
                        </option>
                      ))}
                    </Form.Select>
                  ) : (
                    <Form.Control
                      type={"text"}
                      value={values.service}
                      isInvalid={touched.service && errors.service}
                      onChange={handleChange}
                      name={"service"}
                      placeholder={t("createClient.placeholders.service")}
                    />
                  )}
                  <Form.Control.Feedback type="invalid">
                    {errors.service}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                  <Form.Label>{t("createClient.labels.notes")}</Form.Label>
                  <Form.Control
                    as={"textarea"}
                    rows={3}
                    value={values.notes}
                    isInvalid={touched.notes && errors.notes}
                    onChange={handleChange}
                    name={"notes"}
                    placeholder={t("createClient.placeholders.notes")}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.notes}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                  <Form.Label>{t("createClient.labels.source")}</Form.Label>
                  <div className="social-icons-new-client w-full mx-auto">
                    {userOrderedSourceKeys?.map((icon) => (
                      <div
                        key={icon}
                        className={`social-icon ${
                          parseInt(values.source) === parseInt(icon)
                            ? "selected"
                            : ""
                        }`}
                        onClick={() => {
                          handleChange({
                            target: {
                              name: "source",
                              value: parseInt(icon),
                            },
                          });
                        }}
                      >
                        <ReactSVG src={availableSources[icon]} />
                      </div>
                    ))}
                  </div>
                  <Form.Control
                    type="hidden"
                    value={values.source || ""} // Ensure a default value (empty string if undefined)
                    isInvalid={touched.source && errors.source}
                    onChange={handleChange}
                  />
                  {values.source ? null : (
                    <Form.Control.Feedback type="invalid">
                      {errors.source}
                    </Form.Control.Feedback>
                  )}
                </Form.Group>
                <center>
                  <Button
                    type={"submit"}
                    className={"submit-btn mx-auto"}
                    style={{ width: "fit-content" }}
                  >
                    {t("createClient.buttons.addClient")}
                  </Button>
                </center>
              </Form>
            )}
          </Formik>
        </Container>
      </Modal>
    </>
  );
};

export default CreateClientModal;
