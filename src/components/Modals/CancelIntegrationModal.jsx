import { Button } from "react-bootstrap";

const CancelIntegrationModal = ({ onHide, cancelIntegrationFun }) => {
  return (
    <div
      className={
        "d-flex flex-column justify-content-around align-items-center px-3"
      }
      style={{ minHeight: "200px" }}
    >
      <h3 className={"fw-bold text-center"}>
        Are you sure you want to delete this integration?
      </h3>
      <div className={"d-flex justify-content-center"}>
        <Button variant={"secondary"} className={"me-2"} onClick={onHide}>
          Cancel
        </Button>
        <Button variant={"danger"} onClick={cancelIntegrationFun}>
          Confirm
        </Button>
      </div>
    </div>
  );
};

export default CancelIntegrationModal;
