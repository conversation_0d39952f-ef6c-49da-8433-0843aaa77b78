.admin-theme.create-client-form {
    background-color: #000;
}
.create-client-form {
    border-radius: 20px;
    background: #FFF;
}

.admin-theme.create-client-form .form-label {
    color: #FFFFFF !important;
}

.admin-theme.create-client-form {
    color: #FFFFFF;
}

.create-client-modal .modal-dialog {
    margin-right: 20px;
    min-height: calc(110% - var(--bs-modal-margin) * 2);
    justify-content: flex-end;
}

.create-client-modal .modal-content {
    width: 390px;
}

.create-client-modal .modal-content .form-control {
    border-radius: 10px;
    border: 1px solid #000;
    background: #F5F5F5;
    box-shadow: 0 4px 48px 0 rgba(0, 0, 0, 0.10);
}

.create-client-modal .modal-content .form-label {
    color: rgba(0, 0, 0, 0.50);
}

.social-icons-new-client {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.social-icons-new-client .social-icon svg {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    opacity: 0.75;
    margin-bottom: 1rem;
}

.social-icons-new-client .social-icon.selected svg {
    width: 35px;
    height: 35px;
    border: 1px solid #92C020;
    padding: 1px;
    opacity: 1;
    transition: ease-in-out all 0.25s;
}

.members-status-switch .form-check-input {
    min-height: 25px;
    min-width: 50px;
    background-color: #E35757;
    color: white;
}

.members-status-switch.form-switch .form-check-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%27-4 -4 8 8%27%3e%3ccircle r=%273%27 fill=%27%23fff%27/%3e%3c/svg%3e");
}

.members-status-switch .form-check-input:checked {
    background-color: #92C020;
    border-color: #92C020;
}

.members-status-switch .form-switch .form-check-input {
    margin-top: 0;
}

[dir=rtl] .create-client-modal [type=email], [dir=rtl] .create-client-modal [type=number], [dir=rtl] .create-client-modal [type=tel], [dir=rtl] .create-client-modal [type=url] {
    direction: rtl;
}
