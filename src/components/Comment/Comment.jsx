import React, { useState } from "react";
import { useSelector } from "react-redux";
import { format } from "date-fns";
import { FaEye } from "react-icons/fa";
import "./Comment.css";

const Comment = ({ comment, showReadReceipts = true, currentUser }) => {
  // Fallback to selector if currentUser not passed as prop
  const fallbackCurrentUser = useSelector((state) => state.auth.user);
  const user = currentUser || fallbackCurrentUser;

  // State for read receipts modal and hover
  const [showReadReceiptsModal, setShowReadReceiptsModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  if (!comment) {
    return null;
  }

  // Get current user ID for read receipt comparison
  const currentUserId = String(user?.user?.id);
  const isCurrentUser = String(comment.author?.id) === currentUserId;

  // Format timestamp
  const formatTime = (timestamp) => {
    if (!timestamp) return "Just now";
    const date = new Date(timestamp);
    return format(date, "MMM d, h:mm a");
  };

  // Get initials for avatar fallback
  const getInitials = (name) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Get user photo URL
  const getUserPhotoUrl = (photo) => {
    if (!photo) return null;
    if (typeof photo === "string") {
      return process.env.REACT_APP_PROFILE_PIC_ENDPOINT + photo;
    }
    return URL.createObjectURL(photo);
  };

  const userPhotoUrl = getUserPhotoUrl(comment.author?.photo);

  // Check if comment has image content
  const hasImage =
    comment.image ||
    (comment.type === "image" && comment.text?.startsWith("http"));
  const imageUrl = comment.image || (hasImage ? comment.text : null);

  // Handle image preview
  const handleImagePreview = (imageUrl) => {
    window.open(imageUrl, "_blank");
  };

  // Deduplicate read receipts based on userId (keep the latest one)
  const deduplicatedReadBy = (() => {
    if (!comment.readBy || !Array.isArray(comment.readBy)) return [];

    const uniqueReceipts = new Map();
    comment.readBy.forEach((receipt) => {
      if (receipt.userId) {
        // Keep the latest read receipt for each user
        const existing = uniqueReceipts.get(receipt.userId);
        if (!existing || new Date(receipt.readAt) > new Date(existing.readAt)) {
          uniqueReceipts.set(receipt.userId, receipt);
        }
      }
    });

    return Array.from(uniqueReceipts.values());
  })();

  // Check if current user has read this comment
  const hasCurrentUserRead = deduplicatedReadBy.some(
    (receipt) => receipt.userId === currentUserId
  );

  // Filter out current user from read receipts for display
  const otherReadReceipts = deduplicatedReadBy.filter(
    (receipt) => receipt.userId !== currentUserId
  );

  return (
    <div
      className={`comment-item ${isCurrentUser ? "current-user" : ""}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="comment-header">
        <div className="comment-author">
          <div className="comment-avatar">
            {userPhotoUrl ? (
              <img
                src={userPhotoUrl}
                alt={comment.author?.name || "User"}
                className="comment-avatar-image"
                onError={(e) => {
                  e.target.style.display = "none";
                  e.target.nextSibling.style.display = "flex";
                }}
              />
            ) : null}
            <span
              className="comment-avatar-initials"
              style={{ display: userPhotoUrl ? "none" : "flex" }}
            >
              {getInitials(comment.author?.name)}
            </span>
          </div>
          <div className="comment-author-info">
            <span className="comment-author-name">
              {isCurrentUser ? "You" : comment.author?.name || "Unknown User"}
            </span>
            <span className="comment-timestamp">
              {formatTime(comment.createdAt)}
              {/* Read receipts icon - show on hover if there are read receipts */}
              {isHovered &&
                showReadReceipts &&
                deduplicatedReadBy.length > 0 && (
                  <button
                    className="read-receipts-icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowReadReceiptsModal(true);
                    }}
                    title={`Seen by ${deduplicatedReadBy.length} ${
                      deduplicatedReadBy.length === 1 ? "person" : "people"
                    }`}
                  >
                    <FaEye />
                  </button>
                )}
            </span>
          </div>
        </div>
      </div>

      <div className="comment-content">
        {/* Image content */}
        {hasImage && (
          <div className="comment-image-container">
            <img
              src={imageUrl}
              alt="Comment attachment"
              className="comment-image"
              onClick={() => handleImagePreview(imageUrl)}
              onError={(e) => {
                e.target.style.display = "none";
                const fallback = document.createElement("div");
                fallback.className = "image-error";
                fallback.textContent = "Image failed to load";
                e.target.parentNode.appendChild(fallback);
              }}
            />
          </div>
        )}

        {/* Text content */}
        {comment.text && <p className="comment-text">{comment.text}</p>}
      </div>

      {showReadReceipts && deduplicatedReadBy.length > 0 && (
        <div className="comment-read-receipts">
          <div className="read-receipts-content">
            {hasCurrentUserRead && !isCurrentUser && (
              <span className="read-receipt-item current-user">
                <span className="read-receipt-icon"></span>
                You read this
              </span>
            )}
            {otherReadReceipts.length > 0 && (
              <>
                {otherReadReceipts.slice(0, 3).map((receipt, index) => (
                  <span key={index} className="read-receipt-item">
                    <span className="read-receipt-icon"></span>
                    {receipt.userName}
                  </span>
                ))}
                {otherReadReceipts.length > 3 && (
                  <span className="read-receipt-summary">
                    +{otherReadReceipts.length - 3} more
                  </span>
                )}
              </>
            )}
          </div>
          {deduplicatedReadBy.length > 0 && (
            <div
              className={`read-receipt-summary ${
                hasCurrentUserRead ? "current-user-read" : ""
              }`}
              onClick={(e) => {
                e.stopPropagation();
                setShowReadReceiptsModal(true);
              }}
              style={{ cursor: "pointer" }}
              title="Click to see who has read this comment"
            >
              <FaEye style={{ marginRight: "4px", fontSize: "9px" }} />
              {deduplicatedReadBy.length === 1
                ? "1 person has read this"
                : `${deduplicatedReadBy.length} people have read this`}
            </div>
          )}
        </div>
      )}

      {/* Read Receipts Modal */}
      {showReadReceiptsModal && (
        <div
          className="read-receipts-modal-overlay"
          onClick={() => setShowReadReceiptsModal(false)}
        >
          <div
            className="read-receipts-modal"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="read-receipts-modal-header">
              <h6>Seen by</h6>
              <button
                className="close-modal-btn"
                onClick={() => setShowReadReceiptsModal(false)}
              >
                ×
              </button>
            </div>
            <div className="read-receipts-modal-body">
              {deduplicatedReadBy.length > 0 ? (
                <div className="read-receipts-list">
                  {deduplicatedReadBy.map((receipt, index) => {
                    const userPhotoUrl = receipt.userPhoto
                      ? process.env.REACT_APP_PROFILE_PIC_ENDPOINT +
                        receipt.userPhoto
                      : null;

                    return (
                      <div key={index} className="read-receipt-user">
                        <div className="read-receipt-avatar">
                          {userPhotoUrl ? (
                            <img
                              src={userPhotoUrl}
                              alt={receipt.userName || "User"}
                              className="read-receipt-avatar-image"
                              onError={(e) => {
                                e.target.style.display = "none";
                                e.target.nextSibling.style.display = "flex";
                              }}
                            />
                          ) : null}
                          <span
                            className="read-receipt-avatar-initials"
                            style={{ display: userPhotoUrl ? "none" : "flex" }}
                          >
                            {receipt.userName?.charAt(0)?.toUpperCase() || "?"}
                          </span>
                        </div>
                        <div className="read-receipt-info">
                          <div className="read-receipt-name">
                            {receipt.userName || "Unknown User"}
                          </div>
                          <div className="read-receipt-time">
                            {receipt.readAt
                              ? format(
                                  new Date(receipt.readAt),
                                  "MMM d, h:mm a"
                                )
                              : "Just now"}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="no-read-receipts">
                  No one has seen this comment yet
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Comment;
