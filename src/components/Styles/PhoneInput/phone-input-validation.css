.PhoneInput.is-valid .PhoneInputInput {
    border-color: #80AA17 !important;
    padding-right: calc(1.5em + .75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 8 8%27%3e%3cpath fill=%27%2380AA17%27 d=%27M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z%27/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(.375em + .1875rem) center;
    background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

.PhoneInput.is-invalid .PhoneInputInput {
    border-color: #dc3545 !important;
    padding-right: calc(1.5em + .75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 12 12%27 width=%2712%27 height=%2712%27 fill=%27none%27 stroke=%27%23dc3545%27%3e%3ccircle cx=%276%27 cy=%276%27 r=%274.5%27/%3e%3cpath stroke-linejoin=%27round%27 d=%27M5.8 3.6h.4L6 6.5z%27/%3e%3ccircle cx=%276%27 cy=%278.2%27 r=%27.6%27 fill=%27%23dc3545%27 stroke=%27none%27/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(.375em + .1875rem) center;
    background-size: calc(.75em + .375rem) calc(.75em + .375rem);
}

/* RTL support for validation icons */
.validated-phone-input-container.rtl .PhoneInput.is-valid .PhoneInputInput,
.validated-phone-input-container.rtl .PhoneInput.is-invalid .PhoneInputInput,
.validated-phone-input-container.rtl .PhoneInput.PhoneInput--invalid .PhoneInputInput {
    padding-right: 0;
    padding-left: calc(1.5em + .75rem);
    background-position: left calc(.375em + .1875rem) center;
}

/* Add a small text below for validation messages */
.phone-validation-message {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
}

.phone-validation-message.valid-feedback {
    color: #80AA17;
}

.phone-validation-message.invalid-feedback {
    color: #dc3545;
}
