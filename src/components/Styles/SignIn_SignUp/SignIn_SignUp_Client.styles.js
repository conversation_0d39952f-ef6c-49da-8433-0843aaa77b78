import styled, { css, keyframes } from "styled-components";
import signIn_UpBG from "../../../assets/media/SignIn_UpBG.png";

export const Container = styled.div`
  border-radius: 10px;
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  position: relative;
  overflow: auto;
  width: 100vw;
  height: 100vh;
  background-color: #ffffff;
  direction: ${(props) => props.dir};
`;

export const SignUpContainer = styled.div`
  position: absolute;
  top: 0;
  height: 100%;
  transition: all 0.6s ease-in-out;
  ${(props) => (props.dir === "rtl" ? "right: 0" : "left: 0")};
  width: 50%;
  opacity: 0;
  z-index: 1;
  padding: 0 1rem;

  ${(props) =>
    !props.$signIn &&
    css`
      transform: ${props.dir === "rtl"
        ? "translateX(-100%)"
        : "translateX(100%)"};
      opacity: 1;
      z-index: 5;
    `}

  @media (max-width: 992px) {
    width: 100%;
    ${(props) => (props.dir === "rtl" ? "right: 0" : "left: 0")};
    transform: translateX(${(props) => (props.$signIn ? "-100%" : "0")});
    ${(props) =>
      props.dir === "rtl" &&
      css`
        transform: translateX(${(props) => (props.$signIn ? "100%" : "0")});
      `}
    padding: 0;
  }
`;

export const SignInContainer = styled.div`
  position: absolute;
  top: 0;
  height: 100%;
  padding: 0 1rem;
  transition: all 0.6s ease-in-out;
  ${(props) => (props.dir === "rtl" ? "right: 0" : "left: 0")};
  width: 50%;
  z-index: 2;

  ${(props) =>
    !props.$signIn &&
    css`
      transform: ${props.dir === "rtl"
        ? "translateX(-100%)"
        : "translateX(100%)"};
    `}

  @media (max-width: 992px) {
    width: 100%;
    ${(props) => (props.dir === "rtl" ? "right: 0" : "left: 0")};
    transform: translateX(${(props) => (props.$signIn ? "0" : "100%")});
    ${(props) =>
      props.dir === "rtl" &&
      css`
        transform: translateX(${(props) => (props.$signIn ? "0" : "-100%")});
      `}
    padding: 0;
  }
`;

export const Form = styled.div`
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 50px;
  height: 100%;
  text-align: ${(props) => (props.dir === "rtl" ? "right" : "left")};

  @media (max-width: 992px) {
    padding: 0 1.5rem;
  }
`;

export const Title = styled.h1`
  font-weight: bold;
  margin: 0;
  font-size: 2rem;
  text-align: center;

  @media (max-width: 992px) {
    font-size: 1.5rem;
    text-align: ${(props) => (props.dir === "rtl" ? "right" : "left")};
  }
`;

export const Input = styled.input`
  background-color: transparent;
  padding: 12px 15px;
  margin: 8px 0;
  width: 100%;
  border-radius: 7px;
  border: 2px solid rgba(0, 0, 0, 0.2);
`;

export const Button = styled.button`
  border: 1px solid #92c020;
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  padding: 12px 45px;
  letter-spacing: 1px;
  text-transform: uppercase;
  border-radius: 30px;
  background: linear-gradient(263deg, #92c020 -9.91%, #cad511 128.31%);
  transition: transform 80ms ease-in;
  cursor: pointer;

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: none;
  }
`;

export const GhostButton = styled(Button)`
  border-radius: 30px;
  border: 2px solid #fff;
  background: rgba(133, 172, 28, 0.14);
  backdrop-filter: blur(2.65px);
  padding: 15px 70px;
  font-size: 0.8rem;
`;

export const Anchor = styled.div`
  color: #333;
  font-size: 14px;
  text-decoration: none;
  margin: 15px 0;
  cursor: pointer;
`;

export const OverlayContainer = styled.div`
  position: absolute;
  top: 0;
  ${(props) => (props.dir === "rtl" ? "right: 50%" : "left: 50%")};
  width: 50%;
  height: 100%;
  overflow: hidden;
  transition: transform 0.6s ease-in-out;
  z-index: 100;

  ${(props) =>
    !props.$signIn &&
    css`
      transform: ${props.dir === "rtl"
        ? "translateX(100%)"
        : "translateX(-100%)"};
    `}

  @media (max-width: 992px) {
    display: none;
  }
`;

export const Overlay = styled.div`
  background-image: url(${signIn_UpBG});
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 0 0;
  color: #ffffff;
  position: relative;
  ${(props) => (props.dir === "rtl" ? "right: -100%" : "left: -100%")};
  height: 100%;
  width: 200%;
  transform: translateX(0);
  transition: transform 0.6s ease-in-out;

  ${(props) =>
    !props.$signIn &&
    css`
      transform: ${props.dir === "rtl" ? "translateX(-50%)" : "translateX(50%)"};
    `}
`;

export const OverlayPanel = styled.div`
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 40px;
  text-align: center;
  top: 0;
  height: 100%;
  width: 50%;
  transform: translateX(0);
  transition: transform 0.6s ease-in-out;
`;

export const LeftOverlayPanel = styled(OverlayPanel)`
  transform: ${(props) =>
    props.dir === "rtl" ? "translateX(20%)" : "translateX(-20%)"};

  ${(props) =>
    !props.$signIn &&
    css`
      transform: translateX(0);
    `}
`;

export const RightOverlayPanel = styled(OverlayPanel)`
  ${(props) => (props.dir === "rtl" ? "left: 0" : "right: 0")};
  transform: translateX(0);

  ${(props) =>
    !props.$signIn &&
    css`
      transform: ${(props) =>
        props.dir === "rtl" ? "translateX(-20%)" : "translateX(20%)"};
    `}
`;

export const Paragraph = styled.p`
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.5px;
  margin: 50px 0 80px;
  text-align: ${(props) => (props.dir === "rtl" ? "right" : "left")};
`;

export const MobileBtn = styled.div`
  font-size: 12px;
  font-weight: bold;
  padding: 12px 45px;
  letter-spacing: 1px;
  text-transform: uppercase;
  border-radius: 30px;
  display: none;
  cursor: pointer;
  position: relative;
  z-index: 1000;
  border: 1px solid #92c020;
  background: linear-gradient(263deg, #92c020 -9.91%, #cad511 128.31%);
  color: white;

  @media (max-width: 992px) {
    display: block;
    margin: 0 auto;
    width: fit-content;
  }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

export const ForgotPasswordForm = styled(Form)`
  width: 50%;
  height: 100%;
  margin: 0 auto;
  ${css`
    animation: ${fadeIn} 0.6s ease-in-out;
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
  `}
`;

export const ForgotPasswordContainer = styled.div`
  width: 100vw;
  height: 100vh;
  background-color: #ffffff;
`;
