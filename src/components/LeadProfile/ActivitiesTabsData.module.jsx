import * as Yup from "yup";
// Import lead status configuration to get all available statuses
import { getAllLeadStatuses } from "../../config/leadStatusConfig";

// Define status code constants to avoid hardcoding
const LEAD_STATUS = {
    BOOKED_AND_RESERVED: 8,
    QUOTATION: 10
};

// Define quotation status code constants
const QUOTATION_STATUS = {
    PENDING: 0,
    ACCEPTED: 1,
    REJECTED: 2
};

export const validationSchema = Yup.object({
    result: Yup.string().required("Result is required"),
    // lead_status: Yup.string().required("Status is required"),
    next_date: Yup.string().required("Next date is required"),
    amount: Yup.string().test(
        "requiredForBookedAndReserved",
        "Reservation amount is required",
        function (value) {
            if (Number(this.parent.lead_status) === LEAD_STATUS.BOOKED_AND_RESERVED) {
                return !!value;
            }
            return true;
        },
    ),
    // quotation_amount: Yup.string().test(
    //     "requiredForQuotation",
    //     "Quotation amount is required",
    //     function (value) {
    //         if (Number(this.parent.lead_status) === LEAD_STATUS.QUOTATION) {
    //             return !!value;
    //         }
    //         return true;
    //     },
    // ),
    // quotation_status: Yup.string().test(
    //     "requiredForQuotation",
    //     "Quotation status is required",
    //     function (value) {
    //         if (Number(this.parent.lead_status) === LEAD_STATUS.QUOTATION) {
    //             return !!value;
    //         }
    //         return true;
    //     },
    // ).nullable(),
    // refuse_reason: Yup.string().test(
    //     "requiredForRejectedQuotation",
    //     "Refuse reason is required",
    //     function (value) {
    //         if (Number(this.parent.quotation_status) === QUOTATION_STATUS.REJECTED) {
    //             return !!value;
    //         }
    //         return true;
    //     },
    // ),
    // action_proven: Yup.mixed().test(
    //     "requiredForActionStatus",
    //     "Action proof is required",
    //     function (value) {
    //       if (Number(this.parent.action) === 3) {
    //           return value && value instanceof File;
    //       }
    //       return true;
    //     },
    // ),
    // quotation_offer: Yup.mixed().test(
    //     "requiredForQuotation",
    //     "Quotation offer is required",
    //     function (value) {
    //         if (Number(this.parent.lead_status) === LEAD_STATUS.QUOTATION) {
    //             return value && value instanceof File;
    //         }
    //         return true;
    //     }
    // ).nullable(),
});

// Generate action status options from lead status configuration
export const actionStatusOptions = Object.entries(getAllLeadStatuses())
    .filter(([code, _]) => parseInt(code) <= 11) // Only include basic statuses (0-11)
    .map(([code, label]) => ({
        value: parseInt(code),
        label
    }));

// Generate quotation status options from constants
export const quotationStatus = [
    {value: QUOTATION_STATUS.PENDING, label: "Pending"},
    {value: QUOTATION_STATUS.ACCEPTED, label: "Accepted"},
    {value: QUOTATION_STATUS.REJECTED, label: "Rejected"}
]
