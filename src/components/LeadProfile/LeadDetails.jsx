import Form from "react-bootstrap/Form";
import { getAllLeadStatuses } from "../../config/leadStatusConfig";
import { useSelector } from "react-redux";

const LeadDetails = ({leadDetails}) => {
    // Get current user from Redux store
    const { user } = useSelector((state) => state.auth);
    
    // Get all possible lead statuses
    const allLeadStatuses = getAllLeadStatuses();
    
    // Function to get status name based on status code
    const statusName = (status) => {
        return allLeadStatuses[status] || "Unknown";
    }
    return (
        <>
            <Form.Group className="mb-3">
                <Form.Label>Name</Form.Label>
                <Form.Control
                    name="name"
                    type="text"
                    placeholder="Name"
                    value={leadDetails?.name}
                    disabled
                />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Email Address</Form.Label>
                <Form.Control
                    name="email"
                    type="email"
                    placeholder="Email Address"
                    value={leadDetails?.email}
                    disabled
                />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Phone Number</Form.Label>
                <Form.Control
                    name="phone"
                    type="text"
                    placeholder="Phone Number"
                    value={leadDetails?.phone}
                    disabled
                />
            </Form.Group>
            <Form.Group className="mb-4">
                <Form.Label>Status</Form.Label>
                <Form.Select
                    name="status"
                    aria-label="Lead Status"
                    value={leadDetails?.status || ""}
                    disabled
                >
                    <option value="">{statusName(leadDetails?.status)}</option>
                </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Service</Form.Label>
                <Form.Control
                    name="service"
                    type="text"
                    placeholder="Service"
                    value={leadDetails?.service}
                    disabled
                />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Amount</Form.Label>
                <Form.Control
                    name="amount"
                    type="number"
                    placeholder="Amount"
                    value={leadDetails?.amount}
                    disabled
                />
            </Form.Group>
        </>
    );
};

export default LeadDetails;