import {parseDateCustom} from "../../utils/parseDateCustom";
import {useEffect, useState} from "react";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import classNames from 'classnames';
import {FaEye} from "react-icons/fa6";
import QuotationDetailsModal from "./QuotationDetailsModal"; // Add this if using classnames library

const QuotationsData = ({leadDetails, setQuotationsData, quotationsData}) => {
    const [selectedQuotation, setSelectedQuotation] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const handleQuotationStatus = (currentStatus) => {
        switch (currentStatus) {
            case 0:
                return "Pending";
            case 1:
                return "Accepted";
            case 2:
                return "Rejected";
            default:
                return "Pending";
        }
    };
    useEffect(() => {
        if (leadDetails?.quotations){
            setQuotationsData(leadDetails?.quotations);
        }
    }, [leadDetails]);

    return (<div className="about-contact mb-3">
            <div className="fw-bold mainColor fs-6 mb-3">
                Quotations Data
            </div>
            <ul style={{maxHeight: "240px", overflowY: "auto", fontSize: "0.8rem"}}>
                {quotationsData?.map((quotation) => (<div
                        className="my-2 single-quotation lh-md position-relative"
                        role="button"
                        onClick={() => {
                            setSelectedQuotation(quotation);
                            setShowModal(true);
                        }}
                        key={quotation?.id}
                        style={{borderBottom: "1px solid rgb(204 205 206)"}}
                    >
                    <div className="d-flex justify-content-start">
                        <li className="fw-bold">
                            ID:{" "} <span className={"fw-normal"}>{quotation?.id}</span>
                        </li>
                        <li className="fw-bold ms-auto">
                            Created At:{" "}
                            <span
                                className={classNames({"on-hold-status": Number(quotation?.status) === 0}, {"completed-status": Number(quotation?.status) === 1}, {"not-started-status": Number(quotation?.status) === 2})}>{parseDateCustom(quotation?.created_at)}</span>
                        </li>
                    </div>
                    <div className="d-flex justify-content-start">
                        <li>
                            <span className={"text-start py-2 fw-bold"}>Status: </span>
                            <span
                                className={classNames({"on-hold-status": Number(quotation?.status) === 0}, {"completed-status": Number(quotation?.status) === 1}, {"not-started-status": Number(quotation?.status) === 2})}>{handleQuotationStatus(quotation?.status)}</span>
                        </li>
                        <li className={"fw-bold ms-auto"}>
                            Price:{" "}
                            <span className={"fw-normal"}>{quotation?.price}</span>
                        </li>
                    </div>
                    <div className="overlay-quotation">
                        <FaEye className="eye-icon"/>
                    </div>
                </div>))}
            </ul>
        <CenteredModal
            show={showModal}
            onHide={() => setShowModal(false)}
        >
            <QuotationDetailsModal setShowModal={setShowModal} selectedQuotation={selectedQuotation} showModal={showModal} setQuotationsData={setQuotationsData} />
        </CenteredModal>
        </div>);
};

export default QuotationsData;