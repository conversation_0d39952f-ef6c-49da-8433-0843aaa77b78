import { useState } from "react";
import { auth } from "../../utils/firebase.config";
import { RecaptchaVerifier, signInWithPhoneNumber } from "firebase/auth";
import {
  showSuccessToast,
  showErrorToast,
} from "../../utils/toast-success-error";
import OtpInput from "otp-input-react";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import Form from "react-bootstrap/Form";
import LoadingSpinner from "../LoadingAnimation/Icons/LoadingSpinner";
import "./otp.css";
import signin from "../../assets/media/animations/signin.json";
import <PERSON><PERSON> from "lottie-react";
import { ReactSVG } from "react-svg";
import verifyIcon from "../../../src/assets/media/Icons/verify-phon-icon.svg";
import { Button } from "react-bootstrap";
import clientService from "../../services/auth/client";
import useAuth from "../../redux/hooks/useAuth";
import EmailAnimation from "../../assets/media/animations/email-animation.json";
const OTPComponent = ({
  userData,
  setShowOTPComponent,
  handleSignUpSubmit,
}) => {
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const [showOTP, setShowOTP] = useState(false);
  const [user, setUser] = useState(null);
  const [value, setValue] = useState(userData.phone || "+20");
  const { signUpFlow } = useAuth();
  function onCaptchVerify() {
    try {
      if (!window.recaptchaVerifier) {
        window.recaptchaVerifier = new RecaptchaVerifier(
          auth,
          "recaptcha-container",
          {
            size: "invisible",
            callback: () => {
              onSignup();
            },
            "expired-callback": () => {
              showErrorToast("reCAPTCHA has expired. Please refresh the page");
            },
          }
        );
      }
    } catch (error) {
      showErrorToast("Failed to initialize reCAPTCHA. Please refresh the page");
      console.error("reCAPTCHA initialization error:", error);
    }
  }

  async function onSignup(email) {
    setLoading(true);
    const res = await clientService.CheckPhoneApi(email);

    // onCaptchVerify();
    // const appVerifier = window.recaptchaVerifier;

    // signInWithPhoneNumber(auth, value, appVerifier)
    //     .then((confirmationResult) => {
    //         window.confirmationResult = confirmationResult;
    //         setLoading(false);
    //         setShowOTP(true);
    //         showSuccessToast("OTP sent successfully!");
    //     })
    //     .catch((error) => {
    //         setLoading(false);
    //         let errorMessage = "Failed to send OTP";

    //         // Handle specific Firebase auth errors
    //         if (error.code === 'auth/invalid-phone-number') {
    //             errorMessage = "Invalid phone number format";
    //         } else if (error.code === 'auth/too-many-requests') {
    //             errorMessage = "Too many requests. Please try again later";
    //         } else if (error.code === 'auth/quota-exceeded') {
    //             errorMessage = "SMS quota exceeded. Please try again later";
    //         } else if (error.code === 'auth/captcha-check-failed') {
    //             errorMessage = "reCAPTCHA verification failed. Please try again";
    //         } else if (error.code) {
    //             errorMessage = `Error: ${error.code}`;
    //         }

    //         showErrorToast(errorMessage);
    //     });
  }

  function onOTPVerify() {
    setLoading(true);
    window.confirmationResult
      .confirm(otp)
      .then(async (res) => {
        setUser(res.user);
        await handleSignUpSubmit(userData);
        setLoading(false);
      })
      .catch((err) => {
        showErrorToast("Invalid OTP");
        setLoading(false);
      });
  }

  return (
    <section className="d-flex bg-white align-items-center justify-content-center vh-100">
      <div>
        <div id="recaptcha-container"></div>
        {user ? (
          <center>
            <Lottie
              animationData={signin}
              style={{ width: 138, height: 138 }}
            />
            <h2 className="text-center mainColor fw-bold fs-4 mt-4">
              Verified!!
            </h2>
          </center>
        ) : (
          <div className="d-flex flex-column align-items-center gap-4 rounded-lg">
            {/* <ReactSVG src={verifyIcon} /> */}
            <h1 className="text-center lh-base text-dark font-medium fs-2 fw-bold">
              {/* Verify Phone Number */}
              Verify Email
            </h1>
            <Lottie
              animationData={EmailAnimation}
              style={{ width: "350px", height: "auto" }}
              loop={true}
            />

            {showOTP ? (
              <>
                <label
                  htmlFor="otp"
                  className="fw-bold fs-5 text-dark text-center"
                >
                  Enter The Verification code that we sent to {value}
                  <br />
                  Not your phone number?
                  <span
                    className={"mainColor"}
                    onClick={() => setShowOTP(false)}
                  >
                    {" "}
                    Change
                  </span>
                </label>
                <OtpInput
                  value={otp}
                  onChange={setOtp}
                  OTPLength={6}
                  otpType="number"
                  disabled={false}
                  autoFocus
                  autoSubmit
                  className="otp-container"
                />
                <button
                  onClick={onOTPVerify}
                  className="submit-btn mx-auto py-2 text-white"
                >
                  {loading && <LoadingSpinner />}
                  <span className={"fs-5"}>Verify</span>
                </button>
              </>
            ) : (
              <>
                {/* <Form.Group className="mb-4 mobile-otp rounded-2" controlId="mobile-otp">
                                <PhoneInput
                                    placeholder="Enter phone number"
                                    value={String(value)}
                                    onChange={setValue}
                                    defaultCountry="EG"
                                    international={false}
                                />
                            </Form.Group> */}
                <label
                  htmlFor="otp"
                  className="fw-bold fs-5 text-dark text-center"
                >
                  Enter The Verification code that we sent to {userData?.email}
                  <br />
                  Not your email?
                  <span
                    className={"mainColor"}
                    onClick={() => setShowOTPComponent(false)}
                  >
                    {" "}
                    Change
                  </span>
                </label>
                <OtpInput
                  value={otp}
                  onChange={setOtp}
                  OTPLength={6}
                  otpType="number"
                  disabled={false}
                  autoFocus
                  autoSubmit
                  className="otp-container"
                />
                <button
                  onClick={() => signUpFlow({ ...userData, otp: otp })}
                  className="submit-btn mx-auto py-2 text-white"
                  type="button"
                >
                  {loading && <LoadingSpinner />}
                  <span className={"fs-5"}>Verify</span>
                </button>
                {/* <div className={"d-flex justify-content-between"}>
                                <Button variant={"outline-secondary rounded-pill"} onClick={() => setShowOTPComponent(false)} className={"mainColor fs-6"}>
                                    Back
                                </Button>
                                <button
                                    onClick={()=>onSignup(userData?.email)}
                                    className="submit-btn d-flex align-items-center justify-content-center py-2 fs-6"
                                >
                                    {loading && (
                                        <LoadingSpinner/>
                                    )}
                                    <span>Submit</span>
                                </button>
                            </div> */}
              </>
            )}
          </div>
        )}
      </div>
    </section>
  );
};

export default OTPComponent;
