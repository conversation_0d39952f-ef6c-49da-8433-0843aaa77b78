import React, { useEffect, useRef, useState } from 'react';
import './AudioVisualizer.css';

const AudioVisualizer = ({ isRecording, audioLevel }) => {
  const [recordingTime, setRecordingTime] = useState(0);
  const [bars, setBars] = useState([]);
  const timerRef = useRef(null);

  // Generate random bars for visualization
  useEffect(() => {
    if (isRecording) {
      // Generate random heights for bars when recording
      const interval = setInterval(() => {
        const newBars = [];
        const barCount = 20; // Number of bars in the visualizer

        for (let i = 0; i < barCount; i++) {
          // Create more dynamic movement based on position
          const height = Math.max(15, Math.floor(Math.random() * 60));
          newBars.push(height);
        }

        setBars(newBars);
      }, 100);

      return () => clearInterval(interval);
    } else {
      // Reset bars when not recording
      setBars([]);
    }
  }, [isRecording, audioLevel]);

  // Timer for recording duration
  useEffect(() => {
    if (isRecording) {
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      clearInterval(timerRef.current);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRecording]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="audio-visualizer">
      <div className="recording-indicator">
        <div className="recording-dot"></div>
        <span className="recording-time">{formatTime(recordingTime)}</span>
      </div>

      <div className="visualizer-container">
        {bars.map((height, index) => (
          <div
            key={index}
            className="visualizer-bar"
            style={{ height: `${height}px` }}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default AudioVisualizer;
