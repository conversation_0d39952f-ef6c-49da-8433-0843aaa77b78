import React, { useRef, useEffect } from "react";
import * as d3 from "d3";

const IcicleChart = ({ data }) => {
    const chartRef = useRef();

    useEffect(() => {
        d3.select(chartRef.current).selectAll("*").remove();

        const width = 1200;
        const height = 800;

        // Create level titles at the top
        const levelTitles = ["Marketing Performance", "Social Platform", "Pages", "Campaigns", "Ad Sets", "Ads"];
        const titleColors = {
            "Marketing Performance": "#2d3748",
            "Social Platform": "#001433",
            "Pages": "#002966",
            "Campaigns": "#003D99",
            "Ad Sets": "#0052CC",
            "Ads": "#0866FF"
        };

        const margin = { top: 30, right: 0, bottom: 0, left: 0 };

        const svg = d3.select(chartRef.current)
            .attr("viewBox", [0, 0, width, height + margin.top])
            .attr("width", width)
            .attr("height", height + margin.top)
            .attr("style", "max-width: 100%; height: auto; font: 16px sans-serif;");

        // Add background rectangles and titles
        const levelTitlesGroup = svg.append("g")
            .attr("class", "level-titles")
            .attr("transform", `translate(0, 20)`);

        // Add background rectangles
        levelTitlesGroup.selectAll("rect")
            .data(levelTitles)
            .enter()
            .append("rect")
            .attr("x", (d, i) => i * (width / levelTitles.length))
            .attr("y", -20)
            .attr("width", width / levelTitles.length - 1)
            .attr("height", 25)
            .attr("fill", d => titleColors[d])
            .attr("rx", 4)
            .attr("ry", 4);

        // Add title text
        levelTitlesGroup.selectAll("text")
            .data(levelTitles)
            .enter()
            .append("text")
            .attr("x", (d, i) => (i * (width / levelTitles.length)) + (width / levelTitles.length / 2))
            .attr("y", -3)
            .attr("text-anchor", "middle")
            .attr("fill", "white")
            .attr("font-size", "14px")
            .attr("font-weight", "bold")
            .text(d => d);

        const getNodeColor = (d) => {
            // Root node color
            if (d.depth === 0) return "#2d3748";
            
            // Define gradient colors from darkest to Meta blue
            const colors = ['#001433', '#002966', '#003D99', '#0052CC', '#0866FF'];
            
            // Adjust depth index to account for root
            const colorIndex = (d.depth - 1) % colors.length;
            return colors[colorIndex];
        };

        const getTextColor = (d) => {
            // Always return white for better contrast
            return "#ffffff";
        };

        const calculateTotalMetrics = (node) => {
            if (!node.children) {
                return node.data.metrics || null;
            }

            const totals = {
                spend: 0,
                clicks: 0,
                reach: 0,
                cpm: 0,
                hasData: false
            };

            node.children.forEach(child => {
                const childMetrics = calculateTotalMetrics(child);
                if (childMetrics) {
                    totals.hasData = true;
                    // Sum up numeric values, ignore "N/A"
                    if (childMetrics.spend !== "N/A") totals.spend += Number(childMetrics.spend) || 0;
                    if (childMetrics.clicks !== "N/A") totals.clicks += Number(childMetrics.clicks) || 0;
                    if (childMetrics.reach !== "N/A") totals.reach += Number(childMetrics.reach) || 0;
                    // CPM will be recalculated based on total spend and reach
                }
            });

            // Calculate CPM based on total spend and reach
            if (totals.hasData && totals.reach > 0) {
                totals.cpm = (totals.spend / totals.reach) * 1000;
            }

            return totals.hasData ? {
                spend: totals.spend.toString(),
                clicks: totals.clicks.toString(),
                reach: totals.reach.toString(),
                cpm: totals.cpm.toString(),
                isAggregated: true
            } : null;
        };

        const processNode = (node, isRoot = true) => {
            if (!node) return node;
            
            // Only set the title for the root node
            if (isRoot) {
                node.name = "Social Platforms Performance";
            }
            
            if (node.children) {
                // Pass false for isRoot to children
                node.children = node.children.map(child => processNode(child, false));
            }
            
            if (!node.value) {
                if (node.metrics) {
                    const spend = node.metrics.spend !== "N/A" ? Number(node.metrics.spend) : 0;
                    node.value = Math.max(spend, 1000);
                }
                else if (node.name && (!node.children || node.children.length === 0)) {
                    node.value = 10000;
                }
                else {
                    node.value = 100;
                }
            }
            
            return node;
        };

        const processedData = processNode({...data}, true);
        
        const hierarchy = d3.hierarchy(processedData)
            .sum(d => d.value)
            .sort((a, b) => b.height - a.height || b.value - a.value);

        // Calculate and attach total metrics to each node
        hierarchy.each(node => {
            if (!node.data.metrics) {
                const totalMetrics = calculateTotalMetrics(node);
                if (totalMetrics) {
                    node.data.metrics = totalMetrics;
                }
            }
        });

        const filteredRoot = hierarchy.children ? hierarchy : hierarchy;

        const root = d3.partition()
            .size([height, (filteredRoot.height + 1) * width / 3])
            .padding(1)
            (filteredRoot);

        let focus = root;

        const isNodeVisible = (d) => {
            // Always show root and first level
            if (d.depth <= 1) return true;
            // For deeper nodes, check if any ancestor is the current focus
            let current = d;
            while (current.parent) {
                if (current.parent === focus) return true;
                current = current.parent;
            }
            return false;
        };

        const isMetricsVisible = (d) => {
            return d === focus || d.parent === focus;
        };

        const labelVisible = d => {
            const nodeHeight = rectHeight(d);
            const nodeWidth = d.y1 - d.y0;
            return nodeHeight > 30 && nodeWidth > 50;
        };

        const formatNumber = d3.format(",.2f");
        const formatLargeNumber = d3.format(",.0f");

        const g = svg.append("g")
            .attr("transform", `translate(0, ${margin.top})`);

        const cell = g.selectAll("g")
            .data(root.descendants())
            .join("g")
            .attr("transform", d => `translate(${d.y0},${d.x0})`);

        const rectHeight = d => {
            const rawHeight = d.x1 - d.x0 - Math.min(1, (d.x1 - d.x0) / 2);
            return Math.max(rawHeight, 80);
        };

        const rect = cell.append("rect")
            .attr("width", d => d.y1 - d.y0 - 1)
            .attr("height", d => rectHeight(d))
            .attr("fill-opacity", 0.8)
            .attr("fill", getNodeColor);

        const textGroup = cell.append("g")
            .style("user-select", "none")
            .attr("pointer-events", "none");

        const createTextElement = (parent, y, text, { fontSize = "16px", fontWeight = "normal", fill = "white", x = null } = {}) => {
            if (!text) return null;
            const textElement = parent.append("text")
                .attr("x", x !== null ? x : 4)
                .attr("y", y)
                .style("font-size", fontSize)
                .style("font-weight", fontWeight)
                .style("fill", fill)
                .style("opacity", 1)
                .text(text);

            return textElement;
        };

        textGroup.each(function(d) {
            const group = d3.select(this);
            let yOffset = 20;

            // Node name
            createTextElement(group, yOffset, d.data.name, {
                fontSize: "18px",
                fontWeight: "bold",
                fill: getTextColor(d)
            });
            yOffset += 25;

            // Create metrics if they exist
            if (d.data.metrics) {
                const formatValue = (value, formatter) => {
                    if (value === "N/A" || value == null) return "N/A";
                    const numValue = Number(value);
                    if (isNaN(numValue)) return value;
                    return formatter(numValue);
                };

                const metrics = [
                    { 
                        label: "Spend", 
                        value: d.data.metrics.spend, 
                        format: v => `$${formatNumber(v)}`,
                        show: d.data.metrics.spend !== "N/A" && d.data.metrics.spend != null
                    },
                    { 
                        label: "Clicks", 
                        value: d.data.metrics.clicks, 
                        format: formatLargeNumber,
                        show: d.data.metrics.clicks !== "N/A" && d.data.metrics.clicks != null
                    },
                    { 
                        label: "Reach", 
                        value: d.data.metrics.reach, 
                        format: formatLargeNumber,
                        show: d.data.metrics.reach !== "N/A" && d.data.metrics.reach != null
                    },
                    { 
                        label: "CPM", 
                        value: d.data.metrics.cpm, 
                        format: v => `$${formatNumber(v)}`,
                        show: d.data.metrics.cpm !== "N/A" && d.data.metrics.cpm != null
                    }
                ];

                const metricsGroup = group.append("g")
                    .attr("class", "metrics-group");

                metrics.forEach(metric => {
                    if (metric.show) {
                        const formattedValue = formatValue(metric.value, metric.format);
                        if (formattedValue !== "N/A") {
                            createTextElement(metricsGroup, yOffset, `${metric.label}:`, {
                                fontSize: "16px",
                                fontWeight: "normal",
                                fill: getTextColor(d),
                                x: 4
                            });
                            
                            createTextElement(metricsGroup, yOffset, formattedValue, {
                                fontSize: "18px",
                                fontWeight: "bold",
                                fill: getTextColor(d),
                                x: 70
                            });
                            yOffset += 24;
                        }
                    }
                });

                const dateStart = d.data.metrics.date_start;
                const dateStop = d.data.metrics.date_stop;
                if (dateStart && dateStop && dateStart !== "N/A" && dateStop !== "N/A") {
                    createTextElement(metricsGroup, yOffset, `${dateStart} - ${dateStop}`, {
                        fontSize: "12px",
                        fill: getTextColor(d)
                    });
                }
            }
        });

        // Remove the opacity style from textGroup
        textGroup.style("opacity", 1);

        // Add reset button container
        const buttonContainer = d3.select(chartRef.current.parentNode.parentNode)
            .insert("div", "div")
            .style("margin-bottom", "10px");

        // Add reset button
        const resetButton = buttonContainer.append("button")
            .style("background-color", "#4a5568")
            .style("color", "white")
            .style("border", "none")
            .style("padding", "8px 16px")
            .style("border-radius", "4px")
            .style("cursor", "pointer")
            .style("display", "flex")
            .style("align-items", "center")
            .style("gap", "6px")
            .style("font-size", "14px")
            .style("transition", "background-color 0.2s")
            .on("mouseover", function() {
                d3.select(this).style("background-color", "#2d3748");
            })
            .on("mouseout", function() {
                d3.select(this).style("background-color", "#4a5568");
            })
            .on("click", () => clicked(null, root));

        // Add home icon using IoMdHome component
        const iconContainer = resetButton.append("div")
            .style("display", "flex")
            .style("align-items", "center");

        const iconSvg = iconContainer.append("svg")
            .attr("viewBox", "0 0 24 24")
            .attr("width", "16")
            .attr("height", "16")
            .attr("fill", "currentColor");

        iconSvg.html(`<path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>`);

        // Add button text
        resetButton.append("span")
            .text("Reset View");

        const updateTitles = (focusNode) => {
            // Get the path from root to focus node
            let path = [];
            let current = focusNode;
            while (current) {
                path.unshift(current);
                current = current.parent;
            }

            // Calculate visible levels - always show 3 levels starting from focus node's depth
            let visibleLevels = [];
            let startDepth = Math.max(0, focusNode.depth);
            
            // Get the next three level titles starting from the focus depth
            for (let i = 0; i < 3; i++) {
                const levelIndex = startDepth + i;
                if (levelIndex < levelTitles.length) {
                    visibleLevels.push(levelTitles[levelIndex]);
                }
            }

            // If we don't have 3 levels, pad from the start
            while (visibleLevels.length < 3 && startDepth > 0) {
                startDepth--;
                visibleLevels.unshift(levelTitles[startDepth]);
            }

            // Update title rectangles and text
            const titleWidth = width / 3; // Always divide by 3 for consistent width

            // Update or create rectangles
            const rects = levelTitlesGroup.selectAll("rect")
                .data(visibleLevels);

            // Remove old rectangles with transition
            rects.exit()
                .transition()
                .duration(750)
                .style("opacity", 0)
                .remove();

            // Update existing rectangles
            rects.transition()
                .duration(750)
                .attr("x", (d, i) => i * titleWidth)
                .attr("width", titleWidth - 1)
                .attr("fill", d => titleColors[d]);

            // Add new rectangles
            rects.enter()
                .append("rect")
                .attr("x", (d, i) => i * titleWidth)
                .attr("y", -20)
                .attr("width", titleWidth - 1)
                .attr("height", 25)
                .attr("fill", d => titleColors[d])
                .attr("rx", 4)
                .attr("ry", 4)
                .style("opacity", 0)
                .transition()
                .duration(750)
                .style("opacity", 1);

            // Update or create text
            const texts = levelTitlesGroup.selectAll("text")
                .data(visibleLevels);

            // Remove old text with transition
            texts.exit()
                .transition()
                .duration(750)
                .style("opacity", 0)
                .remove();

            // Update existing text
            texts.transition()
                .duration(750)
                .attr("x", (d, i) => (i * titleWidth) + (titleWidth / 2))
                .text(d => d);

            // Add new text
            texts.enter()
                .append("text")
                .attr("x", (d, i) => (i * titleWidth) + (titleWidth / 2))
                .attr("y", -3)
                .attr("text-anchor", "middle")
                .attr("fill", "white")
                .attr("font-size", "14px")
                .attr("font-weight", "bold")
                .text(d => d)
                .style("opacity", 0)
                .transition()
                .duration(750)
                .style("opacity", 1);
        };

        const clicked = (event, p) => {
            if (event) event.stopPropagation();
            
            focus = p === focus ? (p.parent || root) : (p || root);
            
            // Update titles based on new focus
            updateTitles(focus);

            root.each(d => {
                d.target = {
                    x0: (d.x0 - focus.x0) / (focus.x1 - focus.x0) * height,
                    x1: (d.x1 - focus.x0) / (focus.x1 - focus.x0) * height,
                    y0: d.y0 - focus.y0,
                    y1: d.y1 - focus.y0
                };
            });

            const t = cell.transition()
                .duration(750)
                .attr("transform", d => `translate(${d.target.y0},${d.target.x0})`);

            rect.transition(t)
                .attr("height", d => Math.max(0.5, d.target.x1 - d.target.x0))
                .attr("width", d => Math.max(0.5, d.target.y1 - d.target.y0 - 1))
                .attr("fill", getNodeColor);

            textGroup.transition(t)
                .style("opacity", d => {
                    const nodeHeight = d.target.x1 - d.target.x0;
                    const nodeWidth = d.target.y1 - d.target.y0;
                    return nodeHeight > 30 && nodeWidth > 50 ? 1 : 0;
                });

            textGroup.selectAll("text")
                .transition(t)
                .attr("fill", d => getTextColor(d))
                .style("font-size", d => {
                    const nodeHeight = d.target.x1 - d.target.x0;
                    return nodeHeight > 50 ? "16px" : "14px";
                });
        };

        // Initial title setup
        updateTitles(root);

        // Click handlers
        rect.on("click", clicked);
        svg.on("click", (event) => {
            if (event.target.tagName === "svg") {
                clicked(null, root);
            }
        });

        return () => {
            svg.selectAll("*").remove();
            // Clean up button container
            if (buttonContainer) buttonContainer.remove();
        };
    }, [data]);

    return (
        <div>
            <div style={{ marginBottom: '10px' }}></div>
            <div style={{ position: 'relative' }}>
                <svg ref={chartRef}></svg>
            </div>
        </div>
    );
};

export default IcicleChart;