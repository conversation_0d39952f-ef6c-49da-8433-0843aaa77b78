import { Col, Dropdown, Row } from "react-bootstrap";
import { HiDotsVertical } from "react-icons/hi";
import { HiMiniArrowUturnRight, HiOutlineXMark } from "react-icons/hi2";
import { FiEdit } from "react-icons/fi";
import { useEffect, useState } from "react";
import leadService from "../../../services/leads";
import { Line, Doughnut, Bar } from "react-chartjs-2";
import { useTranslation } from "react-i18next";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
} from "chart.js";

// Register Chart.js components globally
ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement
);

const getColor = (label) => {
  switch (label) {
    case "facebook":
      return "#1b47f8";
    case "linked":
      return "#E3651D";
    case "tiktok":
      return "#191919";
    case "snapchat":
      return "#BED754";
    case "insta":
      return "red";
    case "global":
      return "#750E21";
    case "google":
      return "green";
    default:
      return "black";
  }
};

const ChartsComponent = () => {
  const { t } = useTranslation();
  const [pieChartRoundedData, setPieChartRoundedData] = useState([]);
  const [barChartData, setBarChartData] = useState([]);
  const [lineChartData, setLineChartData] = useState([]);
  const [pieChartSource, setPieChartSource] = useState([]);
  const [lineChartDataDomain, setLineChartDataDomain] = useState(100);
  useEffect(() => {
    (async () => {
      try {
        const results = await Promise.allSettled([
          leadService.getAllLeadsChartApi(),
          leadService.getAllLeadsStatusApi(),
          leadService.getAllLeadsSourcesApi(),
          leadService.getAllCompletedLeadsApi(),
        ]);

        const [
          chartDataResult,
          statusDataResult,
          sourcesDataResult,
          completedLeadsDataResult,
        ] = results;

        // Check and handle each result
        if (chartDataResult.status === "fulfilled") {
          const transformedBarChartData = Object.entries(
            chartDataResult.value?.data || {}
          ).map(([month, value]) => ({
            x: month,
            y: Math.min(value, 100),
          }));
          setBarChartData(transformedBarChartData);
        } else {
          console.error("Error fetching chart data:", chartDataResult.reason);
        }

        if (statusDataResult.status === "fulfilled") {
          const transformedLineChartData = Object.keys(
            statusDataResult.value?.data || {}
          ).map((month) => ({
            x: month,
            y1: {
              value: statusDataResult.value?.data[month]?.pendding || 0,
              label: `${t("status.pending")}: ${
                statusDataResult.value?.data[month]?.pendding || 0
              }`,
            },
            y2: {
              value: statusDataResult.value?.data[month]?.inprogress || 0,
              label: `${t("status.inProgress")}: ${
                statusDataResult.value?.data[month]?.inprogress || 0
              }`,
            },
            y3: {
              value: statusDataResult.value?.data[month]?.completed || 0,
              label: `${t("status.completed")}: ${
                statusDataResult.value?.data[month]?.completed || 0
              }`,
            },
            y4: {
              value: statusDataResult.value?.data[month]?.rejected || 0,
              label: `${t("status.rejected")}: ${
                statusDataResult.value?.data[month]?.rejected || 0
              }`,
            },
          }));
          setLineChartData(transformedLineChartData);
        } else {
          console.error("Error fetching status data:", statusDataResult.reason);
        }

        if (completedLeadsDataResult.status === "fulfilled") {
          const transformedPieChartSource = Object.entries(
            completedLeadsDataResult.value?.data || {}
          ).map(([name, value]) => ({
            x: name,
            y: value,
            color: getRandomColor(),
          }));
          setPieChartSource(transformedPieChartSource);
        } else {
          console.error(
            "Error fetching completed leads data:",
            completedLeadsDataResult.reason
          );
        }

        if (sourcesDataResult.status === "fulfilled") {
          const transformedPieChartRoundedData = Object.entries(
            sourcesDataResult.value?.data || {}
          ).map(([platform, percentage]) => ({
            x: platform,
            y: parseFloat(percentage),
          }));
          setPieChartRoundedData(transformedPieChartRoundedData);
        } else {
          console.error(
            "Error fetching sources data:",
            sourcesDataResult.reason
          );
        }
      } catch (error) {
        console.error("Error during data fetch process:", error);
      }
    })();
  }, []);

  useEffect(() => {
    const maxLineChartDataY =
      lineChartData?.length > 0
        ? Math.max(
            ...lineChartData?.map((month) =>
              Math.max(
                month.y1.value,
                month.y2.value,
                month.y3.value,
                month.y4.value
              )
            )
          )
        : 0;
    const maxYAxis = Math.ceil(maxLineChartDataY / 10) * 10;
    setLineChartDataDomain(maxYAxis);
  }, [lineChartData]);

  const getRandomColor = () => {
    // Generate random color code
    return "#" + Math.floor(Math.random() * 16777215).toString(16);
  };

  return (
    <>
      <Row className={"justify-content-between"}>
        <Col
          lg={8}
          md={12}
          className={"content-container"}
          style={{
            maxWidth: "800px",
            height: "fit-content", // Change to fit-content to prevent stretching
          }}
        >
          <h2>{t("dashboard.leadsStatusYear")}</h2>
          <div
            style={{
              position: "relative",
              height: "350px", // Fixed height container for the chart
              width: "100%",
            }}
          >
            <Line
              className="line-opportunities-chart"
              style={{
                position: "relative",
                width: "100%",
                height: "100%", // Will fill parent container
              }}
              data={{
                labels: lineChartData?.map((datum) => datum.x),
                datasets: [
                  {
                    label: t("status.pending"),
                    backgroundColor: "rgba(128,128,128,0.2)",
                    borderColor: "grey",
                    data: lineChartData?.map((datum) => datum.y1.value),
                    fill: false,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                  },
                  {
                    label: t("status.inProgress"),
                    backgroundColor: "rgba(27, 71, 248, 0.2)",
                    borderColor: "rgb(27, 71, 248)",
                    data: lineChartData?.map((datum) => datum.y2.value),
                    fill: false,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                  },
                  {
                    label: t("status.completed"),
                    backgroundColor: "rgba(0, 128, 0, 0.2)",
                    borderColor: "green",
                    data: lineChartData?.map((datum) => datum.y3.value),
                    fill: false,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                  },
                  {
                    label: t("status.rejected"),
                    backgroundColor: "rgba(255, 0, 0, 0.2)",
                    borderColor: "red",
                    data: lineChartData?.map((datum) => datum.y4.value),
                    fill: false,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                  },
                ],
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false, // Important: allows chart to fill container
                interaction: { mode: "index", intersect: false },
                plugins: {
                  tooltip: {
                    mode: "index",
                    intersect: false,
                    boxPadding: 8,
                    callbacks: {
                      label: function (context) {
                        const label = context.dataset.label || "";
                        const value = context.raw;
                        return `${label}: ${value}`;
                      },
                    },
                  },
                  legend: {
                    display: true,
                    position: "top",
                    labels: {
                      usePointStyle: true, // This changes squares to circles
                      pointStyle: "circle", // Explicitly set point style to circle
                      padding: 15,
                      font: {
                        size: 12,
                      },
                    },
                  },
                },
                scales: {
                  x: {
                    beginAtZero: true,
                  },
                  y: {
                    beginAtZero: true,
                    ticks: {
                      callback: function (value) {
                        return value;
                      },
                    },
                  },
                },
              }}
            />
          </div>
        </Col>

        <Col
          lg={4}
          md={12}
          className={
            "content-container d-flex flex-column justify-content-between"
          }
        >
          <div className={"d-flex justify-content-between"}>
            <h2>{t("dashboard.leadSource")}</h2>
            <Dropdown>
              <Dropdown.Toggle
                variant="light"
                id="dropdown-basic"
                className={"team-actions-button"}
              >
                <HiDotsVertical />
              </Dropdown.Toggle>

              <Dropdown.Menu className={"team-actions-menu"}>
                <Dropdown.Item
                  className={
                    "d-flex justify-content-between align-items-center"
                  }
                >
                  Option 1
                  <HiMiniArrowUturnRight />
                </Dropdown.Item>
                <Dropdown.Item
                  className={
                    "d-flex justify-content-between align-items-center text-secondary"
                  }
                >
                  Option 2 <FiEdit />
                </Dropdown.Item>
                <Dropdown.Item
                  className={
                    "d-flex justify-content-between align-items-center text-danger fw-bold"
                  }
                >
                  Option 3 <HiOutlineXMark />
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </div>
          <div className="position-relative mx-auto">
            <Doughnut
              style={{ width: "300px", height: "300px" }}
              className="doughnut-team-chart mx-auto"
              data={{
                labels: pieChartRoundedData.map((datum) => datum.x),
                datasets: [
                  {
                    data: pieChartRoundedData.map((datum) => datum.y),
                    backgroundColor: pieChartRoundedData.map((datum) =>
                      getColor(datum.x)
                    ),
                    borderColor: "#fff",
                    borderWidth: 4,
                  },
                ],
              }}
              options={{
                responsive: true,
                plugins: {
                  tooltip: {
                    enabled: true,
                    boxPadding: 8,
                    callbacks: {
                      label: function (context) {
                        const label = context.label || "";
                        const value = context.raw || 0;
                        return `${label}: ${value}%`;
                      },
                    },
                  },
                  legend: {
                    display: false,
                  },
                },
                elements: {
                  arc: {
                    borderRadius: 20,
                    borderWidth: 4,
                  },
                },
                layout: {
                  padding: {
                    top: 20,
                    bottom: 20,
                    left: 20,
                    right: 20,
                  },
                },
                cutout: "80%",
              }}
            />
            <div
              style={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                textAlign: "center",
                fontSize: "20px",
                fontWeight: "bold",
                color: "#333",
                zIndex: 10,
              }}
            >
              {pieChartRoundedData?.length > 0
                ? `${
                    pieChartRoundedData?.reduce((a, b) => (a.y > b.y ? a : b), {
                      x: "",
                      y: 0,
                    }).x
                  }: ${
                    pieChartRoundedData?.reduce((a, b) => (a.y > b.y ? a : b), {
                      x: "",
                      y: 0,
                    }).y
                  }%`
                : "No data available"}
            </div>
          </div>
          <div
            className="labels-container"
            style={{ maxHeight: "100px", overflowY: "auto" }}
          >
            {pieChartRoundedData
              ?.filter((datum) => datum.y > 0)
              ?.map((datum, index) => (
                <div key={index} className="label-item">
                  <span
                    className="dot"
                    style={{ backgroundColor: getColor(datum.x) }}
                  />
                  {datum.x}
                </div>
              ))}
          </div>
        </Col>
      </Row>
      <Row className={"justify-content-between"}>
        <Col
          lg={8}
          className={"content-container"}
          style={{
            maxWidth: "800px",
            height: "fit-content", // Prevent container stretching
          }}
        >
          <h2>{t("dashboard.salesFunnel")}</h2>
          <div
            style={{
              position: "relative",
              height: "350px", // Fixed height container
              width: "100%",
            }}
          >
            <Bar
              className="sales-bar-chart"
              style={{
                position: "relative",
                width: "100%",
                height: "100%", // Fill parent container
              }}
              data={{
                labels: barChartData?.map((datum) => datum.x),
                datasets: [
                  {
                    label: "Opportunities",
                    data: barChartData?.map((datum) => datum.y),
                    backgroundColor: "rgba(146, 192, 32, 1)",
                    borderColor: "rgba(146, 192, 32, 1)",
                    borderWidth: 1,
                    barThickness: 20,
                    borderRadius: 10,
                  },
                ],
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false, // Allow chart to fill container
                plugins: {
                  tooltip: {
                    boxPadding: 8,
                    callbacks: {
                      label: function (context) {
                        const label = context.label || "";
                        const value = context.raw || 0;
                        return `${label}: ${value}%`;
                      },
                    },
                    backgroundColor: "white",
                    titleColor: "black",
                    bodyColor: "black",
                    borderColor: "#444444",
                    borderWidth: 1,
                    padding: 10,
                    cornerRadius: 5,
                    pointer: {
                      length: 8,
                    },
                  },
                  legend: {
                    display: false,
                  },
                },
                scales: {
                  x: {
                    grid: {
                      display: false,
                    },
                    ticks: {
                      font: {
                        size: 12,
                        family: "Arial",
                        weight: "bold",
                      },
                      color: "#444444",
                    },
                  },
                  y: {
                    grid: {
                      color: "#dddddd",
                    },
                    ticks: {
                      callback: function (value) {
                        return value + "%";
                      },
                      font: {
                        size: 12,
                        family: "Arial",
                        weight: "bold",
                      },
                      color: "#444444",
                    },
                    beginAtZero: true,
                    max: 100,
                  },
                },
              }}
            />
          </div>
        </Col>
        <Col
          lg={4}
          className={
            "content-container d-flex justify-content-between flex-column"
          }
        >
          <div className={"d-flex justify-content-between"}>
            <h2>{t("dashboard.topTeamMembers")}</h2>
          </div>
          <div className="position-relative">
            <Doughnut
              className="doughnut-opportunities-chart mx-auto"
              style={{ width: "300px", height: "300px" }}
              data={{
                labels: pieChartSource
                  ?.filter((datum) => datum.y > 0)
                  ?.map((datum) => datum.x),
                datasets: [
                  {
                    data: pieChartSource
                      ?.filter((datum) => datum.y > 0)
                      ?.map((datum) => datum.y),
                    backgroundColor: pieChartSource
                      ?.filter((datum) => datum.y > 0)
                      ?.map((datum) => datum.color),
                    borderColor: "#fff",
                    borderWidth: 4,
                  },
                ],
              }}
              options={{
                responsive: true,
                plugins: {
                  tooltip: {
                    boxPadding: 8,
                    enabled: true,
                    callbacks: {
                      label: function (context) {
                        const label = context.label || "";
                        const value = context.raw || 0;
                        return `${label}: ${value}`;
                      },
                    },
                  },
                  legend: {
                    display: false,
                  },
                },
                cutout: "80%",
              }}
            />
            <div
              style={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                textAlign: "center",
                fontSize: "20px",
                fontWeight: "bold",
                color: "#333",
                zIndex: 10,
              }}
            >
              {pieChartSource?.length > 0
                ? `${
                    pieChartSource
                      ?.filter((datum) => datum.y > 0)
                      ?.reduce((a, b) => (a.y > b.y ? a : b), {
                        x: "",
                        y: 0,
                      }).x
                  }: ${
                    pieChartSource
                      .filter((datum) => datum.y > 0)
                      ?.reduce((a, b) => (a.y > b.y ? a : b), {
                        x: "",
                        y: 0,
                      }).y
                  }`
                : "No data available"}
            </div>
          </div>
          <div className="labels-container text-capitalize mt-3">
            {pieChartSource
              ?.filter((datum) => datum.y > 0)
              ?.map((datum, index) => (
                <div
                  key={index}
                  className="label-item d-flex align-items-center"
                >
                  <span
                    className="dot"
                    style={{
                      backgroundColor: datum.color,
                      width: "10px",
                      height: "10px",
                      borderRadius: "50%",
                      display: "inline-block",
                      marginRight: "8px",
                    }}
                  />
                  {datum.x}
                </div>
              ))}
          </div>
        </Col>
      </Row>
    </>
  );
};

export default ChartsComponent;
