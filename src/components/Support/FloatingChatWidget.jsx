import { useEffect, useRef } from "react";
import { FaComments } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  setChatModalVisibility,
  setupGlobalChatListener,
  selectHasNewMessages,
  selectUnreadCount,
} from "../../redux/features/supportChatSlice";
import notificationSound from "../../assets/media/notification_sound.wav";
import "../../pages/Support/components/FloatingChatWidget.css";

const FloatingChatWidget = ({ userId }) => {
  const dispatch = useDispatch();
  const hasNewMessages = useSelector(selectHasNewMessages);
  const unreadCount = useSelector(selectUnreadCount);
  const prevUnreadCountRef = useRef(0);
  // PAUSED: Don't create audio element to prevent loading sound file
  // const notificationSoundRef = useRef(new Audio(notificationSound));
  const notificationSoundRef = useRef(null);
  const showChatModal = useSelector((state) => state.supportChat.showChatModal);

  // Get the RTL setting from i18n
  const { i18n } = useTranslation();
  const isRTL = i18n.language === "ar";

  // Ensure the global listener is set up
  useEffect(() => {
    if (userId) {
      const result = dispatch(setupGlobalChatListener(userId));
      return () => {
        if (typeof result.payload === "function") {
          result.payload();
        }
      };
    }
  }, [dispatch, userId]);

  // Play notification sound when new messages arrive (PAUSED)
  useEffect(() => {
    // If the unread count has increased, play sound
    if (unreadCount > prevUnreadCountRef.current) {
      console.log(
        "🔇 [FLOATING CHAT WIDGET] Notification sound PAUSED - would have played for new messages"
      );
      // PAUSED: Comment out the sound playing
      // notificationSoundRef.current.play().catch((error) => {
      //   console.error("Error playing notification sound:", error);
      // });
    }

    // Update the previous unread count reference
    prevUnreadCountRef.current = unreadCount;
  }, [unreadCount]);

  const handleClick = () => {
    // Toggle the chat modal
    dispatch(setChatModalVisibility(true));
  };

  return (
    <div
      className={`floating-chat-widget ${isRTL ? "rtl" : "ltr"} ${
        hasNewMessages ? "has-new-messages" : ""
      } ${showChatModal ? "active" : ""}`}
      onClick={handleClick}
      title="Support Chat"
    >
      <div className="widget-icon">
        <FaComments size={24} />
        {unreadCount > 0 && (
          <span className="unread-badge">
            {unreadCount > 9 ? "9+" : unreadCount}
          </span>
        )}
      </div>
      <div className="widget-label">Support</div>
    </div>
  );
};

export default FloatingChatWidget;
