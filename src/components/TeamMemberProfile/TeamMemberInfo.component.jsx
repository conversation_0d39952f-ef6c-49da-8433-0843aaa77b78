import React, { useEffect, useState } from "react";
import { PiTrashFill } from "react-icons/pi";
import ProfilePictureComponent from "../ProfilePicture/ProfilePicture.component";
import { MdEdit, MdLock, MdOutlineMailOutline } from "react-icons/md";
import Form from "react-bootstrap/Form";
import { Button, Col, Row } from "react-bootstrap";
import { Formik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { FaPhone } from "react-icons/fa6";
import updateTeamMemberApi from "../../services/teams/update-team-member.api";
import { setRoles } from "../../redux/features/roleSlice";
import { RiUserSettingsFill } from "react-icons/ri";
import { useDispatch, useSelector } from "react-redux";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import DeleteTeamMemberModal from "../Modals/DeleteTeamMemberModal";
import { handleDeleteTeamMemberThunk } from "../../redux/features/clientSlice";
import { useNavigate } from "react-router-dom";
import getAllRolesApi from "../../services/roles/get-all-roles.api";
import "../../pages/Team/Team.css";
import { useTranslation } from "react-i18next";
import {
  showSuccessToast,
  showErrorToast,
} from "../../utils/toast-success-error";

const TeamMemberInfoComponent = ({ teamMember, setTeamMember }) => {
  const [editMode, setEditMode] = useState(false);
  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    phone: Yup.number().required("Phone is required"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    role: Yup.string().required("Role is required"),
  });
  const { user, currentUserPermissions } = useSelector((state) => state.auth);
  const { roles } = useSelector((state) => state.role);
  const canEdit = currentUserPermissions.includes("member-edit");
  const canDelete = currentUserPermissions.includes("member-delete");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const dispatch = useDispatch();
  const handleDeleteTeamMember = ({
    teamMemberId,
    handleClose,
    afterDelete,
  }) => {
    dispatch(
      handleDeleteTeamMemberThunk({ teamMemberId, handleClose, afterDelete })
    );
  };
  const navigate = useNavigate();
  const handleEditClick = () => {
    setEditMode(true);
  };
  useEffect(() => {
    const fetchRoles = async () => {
      const roleType = user.user.flag === "admin" ? "admin" : "user";
      const response = currentUserPermissions.includes("role-list")
        ? await getAllRolesApi(roleType)
        : null;
      dispatch(setRoles(response?.data));
    };
    fetchRoles();
  }, [dispatch, user, currentUserPermissions]);
  const handleSubmit = async (values, { setSubmitting }) => {
    // Check for changes before proceeding
    const hasChanges =
      values.name !== teamMember?.name ||
      values.email !== teamMember?.email ||
      values.phone !== teamMember?.phone ||
      Number(values.role) !== teamMember?.role ||
      values.status !== teamMember?.status ||
      (values.password && values.password.trim() !== "");

    if (!hasChanges) {
      toast.warn("No changes detected", {
        position: "bottom-right",
        theme: "dark",
      });
      setEditMode(false);
      setSubmitting(false);
      return;
    }

    // Prepare update data
    const updateData = {
      name: values.name,
      phone: values.phone,
      email: values.email,
      status: values.status === "active" ? "active" : "deactive",
      role: Number(values.role),
    };

    // Only include password if it was changed
    if (values.password && values.password.trim() !== "") {
      updateData.password = values.password.trim();
    }

    // Update local state and make API call
    setTeamMember((prevTeamMember) => ({
      ...prevTeamMember,
      ...updateData,
    }));

    await updateTeamMemberApi(updateData, teamMember?.id);
    showSuccessToast("Team member updated successfully", {
      position: "bottom-right",
      theme: "dark",
    });
    setSubmitting(false);
    setEditMode(false);
  };
  const parsedRoleNames = roles
    .map((role) => {
      if (role.id === 1 || role.id === 0) {
        return {
          id: role.id,
          name: "Admin",
        };
      }
      if (role.id === 2) {
        return {
          id: role.id,
          name: "Moderator",
        };
      } else if (role.id === 3) {
        return {
          id: role.id,
          name: "Sales",
        };
      } else if (role.id === 4) {
        return {
          id: role.id,
          name: "Accountant",
        };
      }
    })
    .filter((member) => member);
  let roleName = parsedRoleNames.find((role) => role.id === teamMember?.role);
  const handleAfterDelete = () => {
    navigate("/team");
  };
  const { t } = useTranslation();
  return (
    <>
      <Formik
        enableReinitialize
        initialValues={{
          name: teamMember?.name || "",
          phone: teamMember?.phone || "",
          email: teamMember?.email || "",
          password: teamMember?.password || "",
          status: teamMember?.status || "deactive",
          role: teamMember?.role || null,
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({
          values,
          handleChange,
          handleBlur,
          handleSubmit,
          isSubmitting,
          errors,
          touched,
        }) => (
          <Form noValidate onSubmit={handleSubmit}>
            <div className={"content-container w-75 mx-auto"}>
              <div
                className={"d-flex justify-content-between align-items-start"}
              >
                {canDelete ? (
                  editMode ? null : (
                    <div
                      className={"shadow-sm rounded-2 p-1"}
                      role={"button"}
                      onClick={() => setShowDeleteModal(true)}
                    >
                      <PiTrashFill size={20} className={"text-danger"} />
                    </div>
                  )
                ) : null}
                {canEdit ? (
                  editMode ? null : (
                    <div
                      className={"shadow-sm rounded-2 p-1"}
                      role={"button"}
                      onClick={handleEditClick}
                    >
                      <MdEdit size={20} className={"text-dark"} />
                    </div>
                  )
                ) : null}
              </div>
              <div
                className={
                  "d-flex justify-content-center gap-2 align-items-center"
                }
              >
                {editMode ? (
                  <Form.Group>
                    <Form.Control
                      type="text"
                      name="name"
                      value={values.name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      isValid={touched.name && !errors.name}
                      isInvalid={touched.name && errors.name}
                      className="text-center fw-bold fs-5"
                    />
                    <Form.Control.Feedback
                      type="invalid"
                      className="text-center"
                    >
                      {errors.name}
                    </Form.Control.Feedback>
                  </Form.Group>
                ) : (
                  <center className={"fw-bold fs-5"}>{teamMember?.name}</center>
                )}
                {editMode ? (
                  <div
                    className={
                      "d-flex justify-content-center align-items-center"
                    }
                  >
                    <Form.Check
                      type="switch"
                      id={`custom-switch-${teamMember?.id}`}
                      checked={values.status === "active"}
                      className={"members-status-switch"}
                      role={"button"}
                      onChange={() =>
                        handleChange({
                          target: {
                            name: "status",
                            value:
                              values.status === "active"
                                ? "deactive"
                                : "active",
                          },
                        })
                      }
                    />
                  </div>
                ) : null}
              </div>

              <Row
                className={
                  "align-content-center justify-content-center mt-5 mb-2 text-center"
                }
              >
                <Col
                  lg={3}
                  className={
                    "d-flex justify-content-center align-items-center mt-3"
                  }
                >
                  <FaPhone className={"mainColor me-2"} size={25} />{" "}
                  {editMode ? (
                    <Form.Group className={"flex-grow-1"}>
                      <Form.Control
                        type="tel"
                        name="phone"
                        value={values.phone}
                        onChange={handleChange}
                        isValid={touched.phone && !errors.phone}
                        isInvalid={touched.phone && errors.phone}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.phone}
                      </Form.Control.Feedback>
                    </Form.Group>
                  ) : (
                    <div>{teamMember?.phone}</div>
                  )}
                </Col>
                <Col
                  lg={3}
                  className={
                    "d-flex justify-content-center align-items-center mt-3"
                  }
                >
                  <MdOutlineMailOutline
                    className={"mainColor me-2"}
                    size={25}
                  />{" "}
                  {editMode ? (
                    <Form.Group className={"flex-grow-1"}>
                      <Form.Control
                        type="email"
                        name="email"
                        value={values.email}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        isValid={touched.email && !errors.email}
                        isInvalid={touched.email && errors.email}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.email}
                      </Form.Control.Feedback>
                    </Form.Group>
                  ) : (
                    teamMember?.email
                  )}
                </Col>
                <Col
                  lg={3}
                  className={
                    "d-flex justify-content-center align-items-center mt-3"
                  }
                >
                  <MdLock className={"mainColor me-2"} size={25} />{" "}
                  {editMode ? (
                    <Form.Group className={"flex-grow-1"}>
                      <Form.Control
                        type="password"
                        name="password"
                        placeholder={"**********"}
                        value={values.password}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        isValid={touched.password && !errors.password}
                        isInvalid={touched.password && errors.password}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.password}
                      </Form.Control.Feedback>
                    </Form.Group>
                  ) : (
                    "***********"
                  )}
                </Col>
                <Col
                  lg={3}
                  className={
                    "d-flex justify-content-center align-items-center mt-3"
                  }
                >
                  <RiUserSettingsFill className={"mainColor me-2"} size={25} />
                  {editMode ? (
                    <Form.Group className={"flex-grow-1"}>
                      <Form.Select
                        name={"role"}
                        aria-label="Team member role"
                        onChange={handleChange}
                        isInvalid={touched.role && errors.role}
                        value={values.role}
                      >
                        <option disabled value="">
                          Select Role
                        </option>
                        {roles?.map((role) => (
                          <option key={role?.id} value={Number(role?.id)}>
                            {role?.show_name}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Control.Feedback type="invalid">
                        {errors.role}
                      </Form.Control.Feedback>
                    </Form.Group>
                  ) : (
                    <div>{roleName?.name}</div>
                  )}
                </Col>
              </Row>
              {editMode ? (
                <center className={"mt-4"}>
                  <Button
                    type="submit"
                    className="submit-btn"
                    disabled={isSubmitting}
                  >
                    {t("common.save")}
                  </Button>
                  <Button
                    type="button"
                    variant={"outline-danger"}
                    className="rounded-3 py-2 fs-6 ms-3"
                    onClick={() => setEditMode(false)}
                  >
                    {t("common.cancel")}
                  </Button>
                </center>
              ) : null}
            </div>
          </Form>
        )}
      </Formik>
      <CenteredModal
        show={showDeleteModal}
        children={
          <DeleteTeamMemberModal
            title={"Are you sure you want to delete this Team Member?"}
            deleteMemberFunction={handleDeleteTeamMember}
            id={teamMember.id}
            afterDelete={handleAfterDelete}
            handleClose={() => setShowDeleteModal(false)}
          />
        }
        onHide={() => setShowDeleteModal(false)}
      />
    </>
  );
};

export default TeamMemberInfoComponent;
