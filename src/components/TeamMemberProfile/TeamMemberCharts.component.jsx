import React, {useEffect, useState} from 'react';
import {Col, Row} from "react-bootstrap";
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON>Bar, <PERSON><PERSON>hart, VictoryLabel, Victory<PERSON>ie, VictoryTooltip} from "victory";
import {useParams} from "react-router-dom";
import {getTeamMemberStatisticsApi, getTeamMemberStatisticsApi2} from "../../services/teams/get-teammember-statistics.api";
import { useTranslation } from 'react-i18next';
import i18n from '../../utils/i18n';

const TeamMemberChartsComponent = ({classNames}) => {
    const params = useParams();
    const [teamMember, setTeamMember] = useState({});
    const [teamMemberBarChart, setTeamMemberBarChart] = useState([]);
    const {t} = useTranslation();
    const Rtl = i18n.dir() === 'rtl';

    const teamMemberArray = Object.entries(teamMember)
        .filter(([key]) => key !== 'total')
        .map(([key, value]) => ({
            x: t(`teamMember.charts.status.${key}`),
            y: value,
            originalKey: key // Keep original key for color mapping
        }));

    const teamMemberData = [
        { label: t('teamMember.charts.totalTasks'), value: teamMember.total },
        { label: t('teamMember.charts.totalFinished'), value: teamMember.completed },
        { label: t('teamMember.charts.totalChallenges'), value: teamMember.inProgress },
    ];
    useEffect(() => {
        const fetchTeamMember = async () => {
            await getTeamMemberStatisticsApi(params.id)
                .then((res) => {
                    setTeamMember(res.data);
                })
                .catch((err) => {
                    console.log(err);
                });
            await getTeamMemberStatisticsApi2(params.id)
                .then((res) => {
                    const transformedData = Object.entries(res.data).map(([month, value]) => ({
                        x: month,
                        y: Math.min(value, 100),
                    }));
                    setTeamMemberBarChart(transformedData);
                })
                .catch((err) => {
                    console.log(err);
                });
        }
        fetchTeamMember();
    }, []);

    const getColor = (label) => {
        switch (label) {
            case "rejected":
                return "#d50000";
            case "inProgress":
                return "#00cfff";
            case "completed":
                return "#90BD20";
            case "pending":
                return "#ff9800";
            default:
                return "black";
        }
    };
    return (
        <>
            <Row className={`justify-content-evenly text-center ${classNames}`}>
                {teamMemberData?.map((data, index) => (
                    <Col key={index} lg={3} className={"TM-statistics"}>
                        <p>{data.label}</p>
                        <p className={"mainColor fs-4 fw-bold"}>{data.value}</p>
                    </Col>
                ))}
            </Row>
            <Row className={`justify-content-evenly text-center my-5 ${classNames}`}>
                <Col lg={5}>
                    <div className={"content-container"}>
                        <VictoryChart
                            width={500}
                            height={500}
                            domainPadding={{x: 40}}
                            padding={{ top: 50, bottom: 50, left: 50, right: 50 }} // Adjust padding
                        >
                            <VictoryBar
                                alignment="middle"
                                barWidth={20}
                                data={teamMemberBarChart?.map(item => ({...item, y: 100}))}
                                x="x"
                                y="y"
                                style={{data: {fill: 'rgba(146, 192, 32, 0.30)'}}}
                                labelComponent={<VictoryTooltip cornerRadius={5} flyoutStyle={{ fill: "white", stroke: "black" }} />}
                            />
                            <VictoryBar
                                alignment="middle"
                                barWidth={20}
                                data={teamMemberBarChart}
                                x="x"
                                y="y"
                                style={{
                                    data: {
                                        fill: "rgba(146, 192, 32, 1)",
                                        borderRadius: "6px",
                                    },
                                }}
                                labelComponent={<VictoryTooltip cornerRadius={5} flyoutStyle={{ fill: "white", stroke: "black" }} />}
                            />
                            <VictoryAxis />
                            <VictoryAxis
                                dependentAxis
                                tickFormat={(tick) => tick}
                                style={{
                                    tickLabels: {
                                        textAnchor: Rtl ? "start" : "end",
                                        fill: "#333",
                                        fontSize: 16,
                                        padding: 10
                                    },
                                    grid: { stroke: "none" },
                                    axis: { stroke: "#333" }
                                }}
                                offsetX={50} // Adjust this value for RTL
                            />
                        </VictoryChart>
                    </div>
                    <div className={"text-center my-3 fw-bold fs-5"}>
                        {t('teamMember.charts.performance')}
                    </div>
                </Col>
                <Col lg={5}>
                    <div className={"content-container"}>
                        <svg viewBox="0 0 400 400" className={"pieChart-container"}>
                            <VictoryPie
                                standalone={false}
                                width={400}
                                height={400}
                                data={teamMemberArray}
                                innerRadius={80}
                                labelRadius={100}
                                labels={({datum}) => `${datum.x}: ${datum.y}`}
                                labelComponent={<VictoryTooltip centerOffset={{x: 0, y: 0}}/>}
                                style={{
                                    labels: {fontSize: 20, fill: "black"},
                                    data: {
                                        fill: ({datum}) => getColor(datum.originalKey),
                                    },
                                }}
                            />
                            <VictoryLabel
                                textAnchor="middle"
                                style={{fontSize: 20}}
                                x={200} y={200}
                                text={`${t('teamMember.charts.totalLeads')}:\n${teamMember?.total}`}
                            />
                        </svg>
                        <div className="d-flex justify-content-center flex-wrap">
                            {teamMemberArray?.map((datum, index) => (
                                <div key={index} className={"mx-2 label-item"}>
                                    <span
                                        className="dot"
                                        style={{backgroundColor: getColor(datum.originalKey)}}
                                    />
                                    {datum.x}
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className={"text-center my-3 fw-bold fs-5"}>
                        {t('teamMember.charts.skills')}
                    </div>
                </Col>
            </Row>
        </>
    );
};

export default TeamMemberChartsComponent;
