/* Comment Input Form */
.comment-input-form {
  width: 100%;
}

/* Comment Input Container */
.comment-input-container {
  position: relative;
  width: 100%;
}

/* Comment Input Wrapper */
.comment-input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  min-height: 44px;
}

.comment-input-wrapper:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Comment Textarea */
.comment-textarea {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  overflow-y: auto;
  min-height: 20px;
  max-height: 120px;
  padding: 6px 0;
  font-family: inherit;
  color: #212529;
}

.comment-textarea::placeholder {
  color: #6c757d;
}

.comment-textarea:disabled {
  color: #6c757d;
  cursor: not-allowed;
}

.comment-textarea.over-limit {
  color: #dc3545;
}

/* Comment Input Actions */
.comment-input-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

/* Image Preview Container */
.image-preview-container {
  margin-bottom: 12px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.image-preview {
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  max-width: 200px;
}

.preview-image {
  width: 100%;
  height: auto;
  max-height: 150px;
  object-fit: cover;
  display: block;
}

.remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.remove-image-btn:hover {
  background: rgba(220, 53, 69, 0.8);
  transform: scale(1.1);
}

/* Image Button */
.image-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 16px;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: all 0.2s ease;
}

.image-btn:hover:not(:disabled) {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

.image-btn:disabled {
  color: #adb5bd;
  cursor: not-allowed;
}

/* Emoji Button */
.emoji-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 18px;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: all 0.2s ease;
}

.emoji-btn:hover:not(:disabled) {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

.emoji-btn:disabled {
  color: #adb5bd;
  cursor: not-allowed;
}

/* Send Comment Button */
.send-comment-btn {
  background: none;
  border: none;
  color: #007bff;
  font-size: 16px;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: all 0.2s ease;
  position: relative;
}

.send-comment-btn:hover:not(:disabled) {
  background-color: rgba(0, 123, 255, 0.1);
  transform: scale(1.05);
}

.send-comment-btn:active:not(:disabled) {
  transform: scale(0.95);
}

.send-comment-btn:disabled {
  color: #adb5bd;
  cursor: not-allowed;
  transform: none;
}

/* Character Count */
.character-count {
  font-size: 12px;
  text-align: right;
  margin-top: 4px;
  padding-right: 8px;
}

.character-count.near-limit {
  color: #ffc107;
}

.character-count.over-limit {
  color: #dc3545;
  font-weight: 500;
}

/* Comment Emoji Picker */
.comment-emoji-picker-container {
  position: absolute;
  bottom: 100%;
  right: 0;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .comment-input-wrapper {
    padding: 6px 10px;
    min-height: 40px;
  }

  .comment-textarea {
    font-size: 14px;
    padding: 4px 0;
  }

  .image-btn,
  .emoji-btn,
  .send-comment-btn {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }

  .image-preview-container {
    margin-bottom: 8px;
    padding: 6px;
  }

  .image-preview {
    max-width: 150px;
  }

  .preview-image {
    max-height: 120px;
  }

  .comment-emoji-picker-container {
    right: -10px;
    width: 280px;
  }
}

@media (max-width: 480px) {
  .comment-input-wrapper {
    border-radius: 16px;
    padding: 6px 8px;
  }

  .comment-textarea {
    font-size: 13px;
  }

  .image-btn,
  .emoji-btn,
  .send-comment-btn {
    width: 26px;
    height: 26px;
    font-size: 14px;
  }

  .image-preview {
    max-width: 120px;
  }

  .preview-image {
    max-height: 100px;
  }

  .comment-emoji-picker-container {
    right: -20px;
    width: 260px;
  }

  .character-count {
    font-size: 11px;
    padding-right: 4px;
  }
}

/* Loading State */
.send-comment-btn .spinner-border {
  width: 16px;
  height: 16px;
}

/* Focus States for Accessibility */
.emoji-btn:focus,
.send-comment-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Smooth Transitions */
.comment-input-wrapper,
.comment-textarea,
.emoji-btn,
.send-comment-btn {
  transition: all 0.2s ease;
}

/* Scrollbar Styling for Textarea */
.comment-textarea::-webkit-scrollbar {
  width: 4px;
}

.comment-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.comment-textarea::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.comment-textarea::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for emoji picker */
.comment-emoji-picker-container {
  animation: slideUpFadeIn 0.2s ease-out;
}

@keyframes slideUpFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Error state styling */
.comment-input-wrapper.error {
  border-color: #dc3545;
  animation: errorShake 0.5s ease-in-out;
}

.comment-input-wrapper.error:focus-within {
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* Success state for sent comments */
.comment-input-wrapper.success {
  border-color: #28a745;
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    border-color: #28a745;
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.2);
  }
  100% {
    border-color: #e9ecef;
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

/* Enhanced loading state for send button */
.send-comment-btn.loading {
  background-color: rgba(0, 123, 255, 0.1);
  cursor: not-allowed;
}

.send-comment-btn .loading-spinner {
  animation: buttonSpinnerRotate 1s linear infinite;
}

@keyframes buttonSpinnerRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Typing indicator animation */
.comment-input-wrapper.typing {
  border-color: #007bff;
  box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.1);
}

.comment-input-wrapper.typing::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 12px;
  right: 12px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #007bff, transparent);
  animation: typingIndicator 2s ease-in-out infinite;
}

@keyframes typingIndicator {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .comment-input-wrapper {
    background-color: #4a5568;
    border-color: #4a5568;
  }

  .comment-input-wrapper:focus-within {
    border-color: #4dabf7;
    box-shadow: 0 0 0 2px rgba(77, 171, 247, 0.25);
  }

  .comment-textarea {
    color: #e2e8f0;
  }

  .comment-textarea::placeholder {
    color: #a0aec0;
  }

  .comment-textarea:disabled {
    color: #a0aec0;
  }

  .image-preview-container {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .image-btn,
  .emoji-btn {
    color: #a0aec0;
  }

  .image-btn:hover:not(:disabled),
  .emoji-btn:hover:not(:disabled) {
    color: #4dabf7;
    background-color: rgba(77, 171, 247, 0.1);
  }

  .send-comment-btn {
    color: #4dabf7;
  }

  .send-comment-btn:hover:not(:disabled) {
    background-color: rgba(77, 171, 247, 0.1);
  }

  .character-count {
    color: #a0aec0;
  }

  .character-count.near-limit {
    color: #d69e2e;
  }

  .character-count.over-limit {
    color: #f56565;
  }

  .comment-input-wrapper.error {
    border-color: #f56565;
  }

  .comment-input-wrapper.error:focus-within {
    border-color: #f56565;
    box-shadow: 0 0 0 2px rgba(245, 101, 101, 0.25);
  }

  .comment-input-wrapper.success {
    border-color: #68d391;
  }

  .comment-input-wrapper.typing {
    border-color: #4dabf7;
    box-shadow: 0 0 0 1px rgba(77, 171, 247, 0.1);
  }

  .comment-input-wrapper.typing::after {
    background: linear-gradient(90deg, transparent, #4dabf7, transparent);
  }
}

/* Enhanced accessibility */
.comment-input-wrapper:focus-within {
  outline: none;
}

.comment-textarea:focus {
  outline: none;
}

.emoji-btn:focus-visible,
.send-comment-btn:focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Improved mobile touch targets */
@media (max-width: 768px) {
  .emoji-btn,
  .send-comment-btn {
    min-width: 44px;
    min-height: 44px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .comment-input-wrapper,
  .comment-textarea,
  .emoji-btn,
  .send-comment-btn {
    transition: none;
  }

  .comment-input-wrapper.error {
    animation: none;
  }

  .comment-input-wrapper.success {
    animation: none;
  }

  .comment-input-wrapper.typing::after {
    animation: none;
  }

  .comment-emoji-picker-container {
    animation: none;
  }

  .send-comment-btn .loading-spinner {
    animation: none;
  }
}
/* Validation error styles */
.comment-validation-error {
  font-size: 0.875rem;
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: errorSlideIn 0.3s ease-out;
}

.comment-validation-error::before {
  content: "⚠️";
  font-size: 1rem;
  flex-shrink: 0;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Network status styles */
.comment-network-status {
  font-size: 0.875rem;
  padding: 8px 12px;
  margin-top: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: statusSlideIn 0.3s ease-out;
}

.comment-network-status.offline {
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
}

.comment-network-status.offline::before {
  content: "📡";
  font-size: 1rem;
  flex-shrink: 0;
}

@keyframes statusSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced textarea styles for validation states */
.comment-textarea.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.comment-textarea.offline {
  background-color: #f8f9fa;
  border-color: #6c757d;
}

/* Disabled state enhancements */
.comment-input-form.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Dark mode support for new elements */
@media (prefers-color-scheme: dark) {
  .comment-validation-error {
    color: #f56565;
    background-color: #2d1b1e;
    border-color: #4a2c2a;
  }

  .comment-network-status.offline {
    color: #d69e2e;
    background-color: #2d2a1b;
    border-color: #4a452c;
  }

  .comment-textarea.error {
    border-color: #f56565;
    box-shadow: 0 0 0 0.2rem rgba(245, 101, 101, 0.25);
  }

  .comment-textarea.offline {
    background-color: #2d3748;
    border-color: #4a5568;
  }
}

/* Mobile responsive adjustments for new elements */
@media (max-width: 768px) {
  .comment-validation-error,
  .comment-network-status {
    font-size: 0.8125rem;
    padding: 6px 10px;
    margin-top: 6px;
  }

  .comment-validation-error::before,
  .comment-network-status.offline::before {
    font-size: 0.875rem;
  }
}

/* Reduced motion support for new animations */
@media (prefers-reduced-motion: reduce) {
  .comment-validation-error,
  .comment-network-status {
    animation: none;
  }
}
