import { Modal } from 'react-bootstrap';
import "./BottomModal.css";
import {BsArrowsAngleExpand} from "react-icons/bs";
import {AiOutlineClose} from "react-icons/ai";
const BottomModalComponent = (props) => {
    return (
        <Modal
            {...props}
            size="md"
            aria-labelledby="contained-modal-title-vcenter"
            className={"bottom-modal"}
        >
            <Modal.Header className={"bottom-modal-header"}>
                <Modal.Title id="contained-modal-title-vcenter">
                    <div className={"d-flex justify-content-between"}>
                        <div>
                            <div>
                                {props.title}
                            </div>
                        </div>
                        <div className={"d-flex justify-content-between"}>
                            <div className={"me-2"} role={"button"}>
                                <BsArrowsAngleExpand size={20} />
                            </div>
                            <div onClick={props.onHide} role={"button"}>
                                <AiOutlineClose size={20} />
                            </div>
                        </div>
                    </div>
                </Modal.Title>
            </Modal.Header>
            {props.children}
        </Modal>
    );
};

export default BottomModalComponent;
