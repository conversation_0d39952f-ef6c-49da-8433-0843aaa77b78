import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import leadService from '../../services/leads';
import clientService from '../../services/clients';
import adminService from '../../services/admin';
import { cleanData } from '../../utils/clean-data';
import { showSuccessToast, showErrorToast } from '../../utils/toast-success-error';
import CreateTeamMemberApi from "../../services/teams/create-team-member.api";
import deleteTeamMemberApi from "../../services/teams/delete-member.api";

// Async Thunks
export const handleSocialIconClickThunk = createAsyncThunk(
  'client/handleSocialIconClick',
  async (source) => source
);

export const handleAddLeadThunk = createAsyncThunk(
  'client/handleAddLead',
  async ({ values, onHide, resetForm }, { getState }) => {
    try {
      const state = getState();
      const user = state.auth;
      const autoAssign = values.auto_assign ? 1 : 0;
      const leadData = {
        ...values,
        client_id: values.client_id ? parseInt(values.client_id) : user?.user?.id,
        auto_assign: autoAssign,
      };

      const adminFlag = !!values.client_id;
      const filteredData = cleanData(leadData);
      const result = await leadService.createLeadApi(filteredData, adminFlag);

      showSuccessToast("Lead Created Successfully!");
      resetForm();
      onHide();

      return result.data;
    } catch (error) {
      showErrorToast(error.response?.data?.message || 'Failed to add lead');
      throw error;
    }
  }
);

export const handleAssignTeamMemberThunk = createAsyncThunk(
  'client/handleAssignTeamMember',
  async ({ leadId, memberId }) => {
    try {
      const result = await leadService.updateSingleLeadApi(Number(leadId), {
        assigned_id: Number(memberId),
      });
      showSuccessToast("Team member assigned successfully!");
      return result.data;
    } catch (error) {
      showErrorToast(error.response?.data?.message || 'Failed to assign team member');
      throw error;
    }
  }
);

export const handleUnAssignTeamMemberThunk = createAsyncThunk(
  'client/handleUnAssignTeamMember',
  async (leadId) => {
    try {
      const result = await leadService.unassignedLeadsApi(leadId);
      showSuccessToast("Lead Unassigned Successfully");
      return result.data;
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to unassign team member');
      throw error;
    }
  }
);

export const handleAutoAssignThunk = createAsyncThunk(
  'client/handleAutoAssign',
  async (status) => {
    try {
      const result = await leadService.autoAssignLeadsApi(status);
      return result.data;
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to auto-assign leads');
      throw error;
    }
  }
);

export const handleAddTeamMemberThunk = createAsyncThunk(
  'client/handleAddTeamMember',
  async ({ values, handleClose, resetForm }, { getState }) => {
    try {
      const state = getState();
      const user = state.auth.user;
      const trimmedValues = {
        parent_id: user.id,
        name: values.name,
        email: values.email.trim(),
        phone: values.phone.trim(),
        status: "active",
        password: values.password.trim(),
        role: Number(values.role),
      };
      const cleanedData = cleanData(trimmedValues);
      const res = await CreateTeamMemberApi(cleanedData);
      if (res && res.status === 200) {
        showSuccessToast("Team member added successfully");
        handleClose();
        resetForm();
        return res.data.user;
      }
    } catch (err) {
      if (err?.response?.data?.status === 400 && err?.response?.data?.data?.quota === 0) {
        // Handle quota exceeded
        showErrorToast(err?.response?.data?.message || 'Failed to add team member');
        return { quotaExceeded: true };
      }
      showErrorToast(err?.response?.data?.message || 'Failed to add team member');
      throw err;
    }
  }
);

export const handleAddAdminThunk = createAsyncThunk(
  'client/handleAddAdmin',
  async ({ values, handleClose, resetForm }, { getState }) => {
    try {
      const trimmedValues = {
        ...values,
        name: values.name,
        email: values.email.trim(),
        phone: values.phone.trim(),
        password: values.password.trim(),
      };
      const res = await adminService.createAdminMember(trimmedValues);
      if (res && res.status === 200) {
        showSuccessToast("Team member added successfully");
        handleClose();
        resetForm();
        return res.data.Admin;
      }
    } catch (err) {
      showErrorToast(err?.response?.data?.message || 'Failed to add admin');
      throw err;
    }
  }
);

export const handleExportLeadsThunk = createAsyncThunk(
  'client/handleExportLeads',
  async (clientId) => {
    try {
      await leadService.exportLeadsApi(clientId);
      showSuccessToast("Leads exported successfully!");
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to export leads');
      throw error;
    }
  }
);

export const handleExportClientsThunk = createAsyncThunk(
  'client/handleExportClients',
  async () => {
    try {
      await clientService.exportClientsApi();
      showSuccessToast("Clients exported successfully!");
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to export clients');
      throw error;
    }
  }
);

export const handleImportLeadsThunk = createAsyncThunk(
  'client/handleImportLeads',
  async (data) => {
    try {
      const result = await leadService.importLeads(data);
      showSuccessToast("File uploaded successfully");
      window.location.reload();
      return result.data;
    } catch (error) {
      showErrorToast(`Error uploading file: ${error.message}`);
      throw error;
    }
  }
);

export const handleAdminTeamMemberStatusThunk = createAsyncThunk(
  'client/handleAdminTeamMemberStatus',
  async ({ id, status }) => {
    try {
      await leadService.updateAdminMemberStatusApi(id);
      return { id, status };
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to update team member status');
      throw error;
    }
  }
);

export const handleDeleteTeamMemberThunk = createAsyncThunk(
  'client/handleDeleteTeamMember',
  async ({ teamMemberId, handleClose, afterDelete }) => {
    try {
      // Ensure teamMemberId is passed as a value, not an object
      const response = await deleteTeamMemberApi(teamMemberId);
      showSuccessToast(response.message || 'Team member deleted successfully');
      handleClose();
      if (afterDelete) afterDelete();
      return teamMemberId;
    } catch (e) {
      showErrorToast(e?.response?.data?.message || 'Failed to delete team member');
      throw e;
    }
  }
);

export const handleDeleteAdminTeamMemberThunk = createAsyncThunk(
  'client/handleDeleteAdminTeamMember',
  async ({ teamMemberId, handleClose }) => {
    try {
      const response = await adminService.deleteAdminMemberApi(teamMemberId);
      showSuccessToast(response.message || 'Admin team member deleted successfully');
      handleClose();
      return teamMemberId;
    } catch (e) {
      showErrorToast(e?.response?.data?.message || 'Failed to delete admin team member');
      throw e;
    }
  }
);

export const filterLeadsBySource = createAsyncThunk(
  'client/filterLeadsBySource',
  async ({ status, source, recordsPerPage, page }) => {
    try {
      const result = await leadService.getLeadsBySourceApi(status, source, recordsPerPage, page);
      return result.data;
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to filter leads by source');
      throw error;
    }
  }
);

export const handleResetLead = createAsyncThunk(
  'client/handleResetLead',
  async (leadId) => {
    try {
      const result = await leadService.resetLeadApi(leadId);
      showSuccessToast("Lead reset successfully!");
      return result.data;
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to reset lead');
    }
  }
)

const initialState = {
  leadStatusCounts: {
    completed: 0,
    assigned: 0,
    inprogress: 0,
    pendding: 0,
    rejected: 0,
  },
  selectedSource: null,
  leads: [],
  noCommunicationLeads: [],
  teamMembers: [],
  activeTab: 'assignment',
  filterStatus: 'all',
  disableAddMember: false,
  loading: false,
  error: null,
  currentPage: 1,
  recordsPerPage: 10,
};

const clientSlice = createSlice({
  name: 'client',
  initialState,
  reducers: {
    setSelectedSource: (state, action) => {
      state.selectedSource = action.payload;
    },
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    setFilterStatus: (state, action) => {
      state.filterStatus = action.payload;
    },
    setTeamMembers: (state, action) => {
      state.teamMembers = action.payload;
    },
    setLeads: (state, action) => {
      state.leads = action.payload;
    },
    setNoCommunicationLeads: (state, action) => {
      state.noCommunicationLeads = action.payload;
    },
    setLeadStatusCounts: (state, action) => {
      state.leadStatusCounts = action.payload;
    },
    setDisableAddMember: (state, action) => {
      state.disableAddMember = action.payload;
    },
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    setRecordsPerPage: (state, action) => {
      state.recordsPerPage = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(handleSocialIconClickThunk.fulfilled, (state, action) => {
        state.selectedSource = action.payload;
      })
      .addCase(handleAddLeadThunk.fulfilled, (state, action) => {
        state.leads = [action.payload, ...state.leads];
      })
      .addCase(handleAssignTeamMemberThunk.fulfilled, (state, action) => {
        const updateLeadInArray = (arr) => {
          const idx = arr.findIndex(lead => lead.id === action.payload.id);
          if (idx !== -1) {
            arr[idx] = action.payload;
          }
        };

        // Update both leads arrays to keep UI in sync
        updateLeadInArray(state.leads);
        updateLeadInArray(state.noCommunicationLeads);

        // Update lead status counts (only when mutating main leads)
        state.leadStatusCounts.inprogress += 1;
        state.leadStatusCounts.assigned -= 1;
      })
      .addCase(handleUnAssignTeamMemberThunk.fulfilled, (state, action) => {
        const updateLeadInArray = (arr) => {
          const idx = arr.findIndex(lead => lead.id === action.meta.arg);
          if (idx !== -1) {
            arr[idx] = {
              ...arr[idx],
              assignedTo: null,
              latestActivity: action.payload?.activities?.[action.payload.activities.length - 1]
            };
          }
        };

        updateLeadInArray(state.leads);
        updateLeadInArray(state.noCommunicationLeads);
      })
      .addCase(handleAddTeamMemberThunk.fulfilled, (state, action) => {
        if (action.payload?.quotaExceeded) {
          state.disableAddMember = true;
        } else if (action.payload) {
          state.teamMembers = [action.payload, ...state.teamMembers];
        }
      })
      .addCase(handleAddAdminThunk.fulfilled, (state, action) => {
        if (action.payload) {
          state.teamMembers = [action.payload, ...state.teamMembers];
        }
      })
      .addCase(handleImportLeadsThunk.fulfilled, (state, action) => {
        // Update leads after successful import
        if (action.payload) {
          state.leads = action.payload;
        }
      })
      .addCase(handleAdminTeamMemberStatusThunk.fulfilled, (state, action) => {
        const { id, status } = action.payload;
        state.teamMembers = state.teamMembers.map(member =>
          member.id === id ? { ...member, status } : member
        );
      })
      .addCase(handleDeleteTeamMemberThunk.fulfilled, (state, action) => {
        state.teamMembers = state.teamMembers.filter(member => member.id !== action.payload);
      })
      .addCase(handleDeleteAdminTeamMemberThunk.fulfilled, (state, action) => {
        state.teamMembers = state.teamMembers.filter(member => member.id !== action.payload);
      })
      .addCase(filterLeadsBySource.pending, (state) => {
        state.loading = true;
      })
      .addCase(filterLeadsBySource.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.leads = action.payload;
        }
      })
      .addCase(filterLeadsBySource.rejected, (state) => {
        state.loading = false;
      })
      .addCase(handleResetLead.fulfilled, (state, action) => {
        const index = state.leads.findIndex(lead => lead.id === action.payload.id);
        if (index !== -1) {
          state.leads[index] = action.payload;
        }
      });
  },
});

export const {
  setSelectedSource,
  setActiveTab,
  setFilterStatus,
  setTeamMembers,
  setLeads,
  setNoCommunicationLeads,
  setLeadStatusCounts,
  setDisableAddMember,
  setCurrentPage,
  setRecordsPerPage,
} = clientSlice.actions;

// Add aliases for thunk functions to maintain backward compatibility
export const addLead = handleAddLeadThunk;
export const importLeads = handleImportLeadsThunk;
export const deleteTeamMember = handleDeleteTeamMemberThunk;
export const deleteAdminTeamMember = handleDeleteAdminTeamMemberThunk;
export const handleAddAdminTeamMember = handleAddAdminThunk;

export default clientSlice.reducer;
