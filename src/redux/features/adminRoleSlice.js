import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import getSingleRoleApi from '../../services/roles/get-single-role.api';
import updateRoleApi from '../../services/roles/update-role.api';
import Cookies from 'js-cookie';
import { showErrorToast } from '../../utils/toast-success-error';
import { toast } from 'react-toastify';
import { setCurrentUserPermissions } from './authSlice';

const initialState = {
  roles: [],
  permissions: [],
  selectedPermissions: [],
  selectedRoleName: null,
  loading: false,
  allPermissions: [],
  defaultKey: null,
  roleId: null,
  dynamicAdminRolesData: [],
  createRole: false,
};

export const fetchAdminPermissions = createAsyncThunk(
  'adminRole/fetchAdminPermissions',
  async (_, { dispatch }) => {
    const userPermissions = JSON.parse(Cookies.get('userPermissions'));
    dispatch(setCurrentUserPermissions(userPermissions));
    return userPermissions;
  }
);

export const fetchSingleAdminRoleData = createAsyncThunk(
  'adminRole/fetchSingleAdminRoleData',
  async (id, { rejectWithValue }) => {
    try {
      const response = await getSingleRoleApi(id, "admin");
      return response?.data;
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to fetch admin role data', { position: 'bottom-right', theme: 'dark' });
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch admin role data');
    }
  }
);

export const updateAdminRole = createAsyncThunk(
  'adminRole/updateAdminRole',
  async ({ roleId, selectedPermissions }, { rejectWithValue }) => {
    try {
      await updateRoleApi(roleId, selectedPermissions, "admin");
      toast('Admin role updated successfully', { type: 'success', position: 'bottom-right', theme: 'dark' });
      return true;
    } catch (error) {
      toast.error(error?.response?.data?.message || 'Failed to update admin role', { type: 'error', position: 'bottom-right', theme: 'dark' });
      return rejectWithValue(error?.response?.data?.message || 'Failed to update admin role');
    }
  }
);

export const handleAdminTabSelect = createAsyncThunk(
  'adminRole/handleAdminTabSelect',
  async (selectedKey, { dispatch, getState }) => {
    const { roles } = getState().adminRole;
    const selectedRole = roles.find(role => role.name === selectedKey);

    if (selectedRole) {
      // First update the state with the selected role info
      dispatch(setSelectedRoleName(selectedRole.name));
      dispatch(setDefaultKey(selectedKey));
      dispatch(setRoleId(selectedRole.id));

      // Then fetch the role's permissions
      try {
        const response = await getSingleRoleApi(selectedRole.id, "admin");
        return response?.data;
      } catch (error) {
        showErrorToast(error?.response?.data?.message || 'Failed to fetch admin role data', { position: 'bottom-right', theme: 'dark' });
        throw error;
      }
    }
    return null;
  }
);

const adminRoleSlice = createSlice({
  name: 'adminRole',
  initialState,
  reducers: {
    setRoles: (state, action) => {
      state.roles = action.payload;
    },
    setPermissions: (state, action) => {
      state.permissions = action.payload;
    },
    setSelectedPermissions: (state, action) => {
      state.selectedPermissions = action.payload;
    },
    setSelectedRoleName: (state, action) => {
      state.selectedRoleName = action.payload;
    },
    setCreateRole: (state, action) => {
      state.createRole = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setAllPermissions: (state, action) => {
      state.allPermissions = action.payload;
    },
    setDefaultKey: (state, action) => {
      state.defaultKey = action.payload;
    },
    setRoleId: (state, action) => {
      state.roleId = action.payload;
    },
    setDynamicAdminRolesData: (state, action) => {
      state.dynamicAdminRolesData = action.payload;
    },
    handleAdminSwitchChange: (state, action) => {
      const permissionName = action.payload;
      if (state.selectedPermissions.includes(permissionName)) {
        state.selectedPermissions = state.selectedPermissions.filter(name => name !== permissionName);
      } else {
        state.selectedPermissions = [...state.selectedPermissions, permissionName];
      }
    },
    handleAdminVerticalTabSelect: (state, action) => {
      const selectedKey = action.payload;
      const selectedRole = state.dynamicAdminRolesData.find(role => role.key === selectedKey);
      if (selectedRole) {
        state.defaultKey = selectedKey;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSingleAdminRoleData.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchSingleAdminRoleData.fulfilled, (state, action) => {
        if (action.payload) {
          state.permissions = action.payload?.permissions || [];
          state.selectedPermissions = action.payload?.permissions?.map(permission => permission.name) || [];
        }
        state.loading = false;
      })
      .addCase(fetchSingleAdminRoleData.rejected, (state) => {
        state.loading = false;
      })
      .addCase(updateAdminRole.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateAdminRole.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateAdminRole.rejected, (state) => {
        state.loading = false;
      })
      .addCase(handleAdminTabSelect.pending, (state) => {
        state.loading = true;
      })
      .addCase(handleAdminTabSelect.fulfilled, (state, action) => {
        if (action.payload) {
          state.permissions = action.payload?.permissions || [];
          state.selectedPermissions = action.payload?.permissions?.map(permission => permission.name) || [];
        }
        state.loading = false;
      })
      .addCase(handleAdminTabSelect.rejected, (state) => {
        state.loading = false;
      });
  }
});

export const {
  setRoles,
  setPermissions,
  setSelectedPermissions,
  setSelectedRoleName,
  setCreateRole,
  setLoading,
  setAllPermissions,
  setDefaultKey,
  setRoleId,
  setDynamicAdminRolesData,
  handleAdminSwitchChange,
  handleAdminVerticalTabSelect
} = adminRoleSlice.actions;

export default adminRoleSlice.reducer;
