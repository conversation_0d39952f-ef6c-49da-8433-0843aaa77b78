import { createSlice } from '@reduxjs/toolkit';

// Initial state matches the state variables from the context
const initialState = {
  totalPages: 1,
  currentPage: 1,
  recordsPerPage: 10,
  paginationLinks: [],
  total: 0,
  recordsToDisplay: 10 // Default value
};

const leadsPaginationSlice = createSlice({
  name: 'leadsPagination',
  initialState,
  reducers: {
    setTotalPages: (state, action) => { state.totalPages = action.payload; },
    setCurrentPage: (state, action) => { state.currentPage = action.payload; },
    setRecordsPerPage: (state, action) => { state.recordsPerPage = action.payload; },
    setPaginationLinks: (state, action) => { state.paginationLinks = action.payload; },
    setTotal: (state, action) => { state.total = action.payload; },
    setRecordsToDisplay: (state, action) => { state.recordsToDisplay = action.payload; },
    // Add a reset action to reset the pagination state
    resetPagination: (state) => {
      state.totalPages = 1;
      state.currentPage = 1;
      state.recordsPerPage = 10;
      state.paginationLinks = [];
      state.total = 0;
      state.recordsToDisplay = 10;
    }
  },
});

export const {
  setTotalPages,
  setCurrentPage,
  setRecordsPerPage,
  setPaginationLinks,
  setTotal,
  setRecordsToDisplay,
  resetPagination
} = leadsPaginationSlice.actions;

export default leadsPaginationSlice.reducer;
