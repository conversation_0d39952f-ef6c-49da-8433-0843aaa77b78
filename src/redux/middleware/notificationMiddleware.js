import { playNotificationSound } from '../../utils/notificationSound';
import { resetNotificationSound } from '../features/metaBusinessChatSlice';

/**
 * Middleware to handle notification sounds for comments
 */
export const notificationMiddleware = (store) => (next) => (action) => {
    // Call the next middleware/reducer first
    const result = next(action);

    // Check if we should play notification sound after state update
    const state = store.getState();
    if (state.metaBusinessSuite.shouldPlayNotificationSound) {
        console.log('🔔 [MIDDLEWARE] Notification sound flag is true, but sound is PAUSED');

        // PAUSED: Comment out the sound playing
        // playNotificationSound();

        // Reset the flag to prevent accumulation
        store.dispatch(resetNotificationSound());

        console.log('� [MIDDLEWARE] Notification sound PAUSED - flag reset without playing sound');
    }

    return result;
};
