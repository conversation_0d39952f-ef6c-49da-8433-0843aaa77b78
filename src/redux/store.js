import { configureStore } from '@reduxjs/toolkit';

import clientReducer from './features/clientSlice';
import adminReducer from './features/adminSlice';
import authReducer from './features/authSlice';
import roleReducer from './features/roleSlice';
import adminRoleReducer from './features/adminRoleSlice';
import integrationReducer from './features/integrationSlice';
import leadsPaginationReducer from './features/leadsPaginationSlice';
import facebookTokenModalReducer from './features/facebookTokenModalSlice';
import supportChatReducer from './features/supportChatSlice';
import adminSupportChatReducer from './features/adminSupportChatSlice';
import metaBusinessChatSliceReducer from './features/metaBusinessChatSlice';
import reportsReducer from './features/reportsSlice';
import { notificationMiddleware } from './middleware/notificationMiddleware';

const store = configureStore({
  reducer: {
    client: clientReducer,
    admin: adminReducer,
    auth: authReducer,
    role: roleReducer,
    adminRole: adminRoleReducer,
    integration: integrationReducer,
    leadsPagination: leadsPaginationReducer,
    supportChat: supportChatReducer,
    adminSupportChat: adminSupportChatReducer,
    facebookTokenModal: facebookTokenModalReducer,
    metaBusinessSuite: metaBusinessChatSliceReducer,
    reports: reportsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(notificationMiddleware),
});

export default store;

