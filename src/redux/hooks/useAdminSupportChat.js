import { useDispatch, useSelector } from 'react-redux';
// Import everything needed from the adminSupportChat slice
import {
    // Action creators
    setActiveClient,
    setMessages,
    setMessageText,
    setAttachment,
    addUnreadChat as addUnreadChatActionCreator,
    removeUnread<PERSON><PERSON> as removeUnreadChatActionCreator,
    update<PERSON><PERSON><PERSON><PERSON> as updateClientChatActionCreator,
    setChatModalVisibility,
    resetChatState,
    setChatFocusState,
    // Selectors
    selectClientChats,
    selectActiveClientId,
    selectActiveClientName,
    selectMessages as selectChatMessages,
    selectMessageText as selectChatMessageText,
    selectAttachment as selectChatAttachment,
    selectLoading as selectChatLoading,
    selectError as selectChatError,
    selectUnreadChats,
    selectShowChatModal,
    selectIsChatFocused,
    // Async thunks
    fetchClientChats,
    markClientMessagesAsRead,
    sendMessageAsSupport,
    setupGlobalAdminChatListener,
} from '../features/adminSupportChatSlice';

/**
 * Custom hook exposing admin support chat slice state, synchronous actions, and async thunks.
 */
const useAdminSupportChat = () => {
    const dispatch = useDispatch();

    // ----------- State selectors ----------- //
    const clientChats = useSelector(selectClientChats);
    const activeClientId = useSelector(selectActiveClientId);
    const activeClientName = useSelector(selectActiveClientName);
    const messages = useSelector(selectChatMessages);
    const messageText = useSelector(selectChatMessageText);
    const attachment = useSelector(selectChatAttachment);
    const loading = useSelector(selectChatLoading);
    const error = useSelector(selectChatError);
    const unreadChats = useSelector(selectUnreadChats);
    const showChatModal = useSelector(selectShowChatModal);
    const isChatFocused = useSelector(selectIsChatFocused);

    // ----------- Sync action dispatchers ----------- //
    const actions = {
        setActiveClient: (value) => dispatch(setActiveClient(value)),
        setMessages: (value) => dispatch(setMessages(value)),
        setMessageText: (value) => dispatch(setMessageText(value)),
        setAttachment: (value) => dispatch(setAttachment(value)),
        addUnreadChat: (value) => dispatch(addUnreadChatActionCreator(value)),
        removeUnreadChat: (value) => dispatch(removeUnreadChatActionCreator(value)),
        updateClientChat: (value) => dispatch(updateClientChatActionCreator(value)),
        setChatModalVisibility: (value) => dispatch(setChatModalVisibility(value)),
        resetChatState: () => dispatch(resetChatState()),
        setChatFocusState: (value) => dispatch(setChatFocusState(value)),
    };

    // ----------- Async action dispatchers ----------- //
    const asyncActions = {
        fetchClientChats: () => dispatch(fetchClientChats()),
        markClientMessagesAsRead: (clientId) =>
            dispatch(markClientMessagesAsRead(clientId)),
        sendMessageAsSupport: (params) => dispatch(sendMessageAsSupport(params)),
        setupGlobalAdminChatListener: () =>
            dispatch(setupGlobalAdminChatListener()),
    };

    // Return API
    return {
        // State
        clientChats,
        activeClientId,
        activeClientName,
        messages,
        messageText,
        attachment,
        loading,
        error,
        unreadChats,
        showChatModal,
        isChatFocused,

        // Sync actions
        ...actions,

        // Async actions
        ...asyncActions,
    };
};

export default useAdminSupportChat;

// Re-export thunks for granular usage if needed
export {
    fetchClientChats,
    markClientMessagesAsRead,
    sendMessageAsSupport,
    setupGlobalAdminChatListener,
};
