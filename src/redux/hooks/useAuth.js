import { useDispatch, useSelector } from 'react-redux';
import {
    // Sync action creators
    login,
    logout,
    setCurrentUserPermissions,
    setUserFlag,
    setUser,
    setShowExpiredSessionModal,
    setProcessing,
    setPackageData,
    updateUser,
    // Async thunks
    signUpFlow,
    handlePurchase,
} from '../features/authSlice';

// Inline selectors (could also be exported from slice)
const selectUser = (state) => state.auth.user;
const selectCurrentUserPermissions = (state) => state.auth.currentUserPermissions;
const selectUserFlag = (state) => state.auth.userFlag;
const selectShowExpiredSessionModal = (state) => state.auth.showExpiredSessionModal;
const selectPackageData = (state) => state.auth.packageData;
const selectProcessing = (state) => state.auth.processing;

/**
 * Hook providing auth slice state and dispatch helpers.
 */
const useAuth = () => {
    const dispatch = useDispatch();

    // State selectors
    const user = useSelector(selectUser);
    const currentUserPermissions = useSelector(selectCurrentUserPermissions);
    const userFlag = useSelector(selectUserFlag);
    const showExpiredSessionModal = useSelector(selectShowExpiredSessionModal);
    const packageData = useSelector(selectPackageData);
    const processing = useSelector(selectProcessing);

    // Sync actions
    const actions = {
        login: (payload) => dispatch(login(payload)),
        logout: () => dispatch(logout()),
        setCurrentUserPermissions: (payload) => dispatch(setCurrentUserPermissions(payload)),
        setUserFlag: (payload) => dispatch(setUserFlag(payload)),
        setUser: (payload) => dispatch(setUser(payload)),
        setShowExpiredSessionModal: (payload) => dispatch(setShowExpiredSessionModal(payload)),
        setProcessing: (payload) => dispatch(setProcessing(payload)),
        setPackageData: (payload) => dispatch(setPackageData(payload)),
        updateUser: (payload) => dispatch(updateUser(payload)),
    };

    // Async thunk dispatchers
    const asyncActions = {
        signUpFlow: (values) => dispatch(signUpFlow(values)),
        handlePurchase: () => dispatch(handlePurchase()),
    };

    return {
        // State
        user,
        currentUserPermissions,
        userFlag,
        showExpiredSessionModal,
        packageData,
        processing,

        // Sync actions
        ...actions,

        // Async actions
        ...asyncActions,
    };
};

export default useAuth;

export {
    // re-export thunks for granular usage
    signUpFlow,
    handlePurchase,
};
