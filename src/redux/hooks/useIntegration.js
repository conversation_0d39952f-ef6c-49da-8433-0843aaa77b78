import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
    // Action creators
    setSearchTerm,
    setCurrentPage,
    setFilteredData,
    setShowIntegrationModal,
    setAccounts,
    setAdvertisers,
    setTiktokIntegrated,
    setShowCancelIntegrationModal,
    setShowCancelIntegrationModal2,
    setIntegratedAlready,
    setTiktokPages,
    setLoadingPages,
    setSelectedAdvertiser,
    setShowTikTokAuthModal,
    setUserPages,
    clearAuthError,
    clearDisconnectError,
    clearFetchPagesError,
    clearDownloadLeadsError,
    // Async thunks
    signInWithPlatform,
    disconnectFromFacebook,
    disconnectFromTiktok,
    fetchTikTokPages,
    downloadLeadsTiktok,
    // Helper function
    getIntegrationData,
} from '../features/integrationSlice';

/**
 * Custom hook that provides integration functionality using Redux
 * This replaces the useIntegrationContext hook for easier migration
 */
export const useIntegration = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { t } = useTranslation();

    // Select all integration state from Redux store
    const integrationState = useSelector((state) => state.integration);

    // Destructure state for easier access
    const {
        searchTerm,
        currentPage,
        filteredData,
        showIntegrationModal,
        accounts,
        advertisers,
        tiktokIntegrated,
        showCancelIntegrationModal,
        showCancelIntegrationModal2,
        integratedAlready,
        tiktokPages,
        loadingPages,
        selectedAdvertiser,
        showTikTokAuthModal,
        userPages,
        // Loading states
        isAuthenticating,
        isDisconnecting,
        isFetchingPages,
        isDownloadingLeads,
        // Error states
        authError,
        disconnectError,
        fetchPagesError,
        downloadLeadsError,
    } = integrationState;

    // Action dispatchers
    const actions = {
        setSearchTerm: (value) => dispatch(setSearchTerm(value)),
        setCurrentPage: (value) => dispatch(setCurrentPage(value)),
        setFilteredData: (value) => dispatch(setFilteredData(value)),
        setShowIntegrationModal: (value) => dispatch(setShowIntegrationModal(value)),
        setAccounts: (value) => dispatch(setAccounts(value)),
        setAdvertisers: (value) => dispatch(setAdvertisers(value)),
        setTiktokIntegrated: (value) => dispatch(setTiktokIntegrated(value)),
        setShowCancelIntegrationModal: (value) => dispatch(setShowCancelIntegrationModal(value)),
        setShowCancelIntegrationModal2: (value) => dispatch(setShowCancelIntegrationModal2(value)),
        setIntegratedAlready: (value) => dispatch(setIntegratedAlready(value)),
        setTiktokPages: (value) => dispatch(setTiktokPages(value)),
        setLoadingPages: (value) => dispatch(setLoadingPages(value)),
        setSelectedAdvertiser: (value) => dispatch(setSelectedAdvertiser(value)),
        setShowTikTokAuthModal: (value) => dispatch(setShowTikTokAuthModal(value)),
        setUserPages: (value) => dispatch(setUserPages(value)),

        // Error clearing actions
        clearAuthError: () => dispatch(clearAuthError()),
        clearDisconnectError: () => dispatch(clearDisconnectError()),
        clearFetchPagesError: () => dispatch(clearFetchPagesError()),
        clearDownloadLeadsError: () => dispatch(clearDownloadLeadsError()),
    };

    // Async action dispatchers
    const asyncActions = {
        handleSignInWithPlatform: (platform) =>
            dispatch(signInWithPlatform({ platform, navigate, t })),

        handleDisconnectFromFB: (FBAccID) =>
            dispatch(disconnectFromFacebook({ FBAccID, navigate })),

        handleDisconnectFromTiktok: () =>
            dispatch(disconnectFromTiktok()),

        handleFetchTikTokPages: (advertiserID) =>
            dispatch(fetchTikTokPages(advertiserID)),

        handleDownloadLeadsTiktok: (advertiserID, pageID) =>
            dispatch(downloadLeadsTiktok({ advertiserID, pageID })),
    };

    // Get integration data with translations
    const integrationData = getIntegrationData(t);

    // Return all the values that were previously available in the context
    return {
        // State values
        searchTerm,
        currentPage,
        filteredData,
        showIntegrationModal,
        accounts,
        advertisers,
        tiktokIntegrated,
        showCancelIntegrationModal,
        showCancelIntegrationModal2,
        integratedAlready,
        tiktokPages,
        loadingPages,
        selectedAdvertiser,
        showTikTokAuthModal,
        userPages,
        integrationData,

        // Loading states
        isAuthenticating,
        isDisconnecting,
        isFetchingPages,
        isDownloadingLeads,

        // Error states
        authError,
        disconnectError,
        fetchPagesError,
        downloadLeadsError,

        // Action dispatchers (maintaining the same API as context)
        ...actions,

        // Async actions (maintaining the same API as context)
        ...asyncActions,

        // Legacy aliases for backward compatibility
        handleSignInWithPlatform: asyncActions.handleSignInWithPlatform,
        handleDisconnectFromFB: asyncActions.handleDisconnectFromFB,
        handleDisconnectFromTiktok: asyncActions.handleDisconnectFromTiktok,
        handleFetchTikTokPages: asyncActions.handleFetchTikTokPages,
        handleDownloadLeadsTiktok: asyncActions.handleDownloadLeadsTiktok,
    };
};

// Export as default for easier importing
export default useIntegration;

// Also export the individual pieces for more granular usage if needed
export {
    // Re-export from slice for direct usage
    signInWithPlatform,
    disconnectFromFacebook,
    disconnectFromTiktok,
    fetchTikTokPages,
    downloadLeadsTiktok,
    getIntegrationData,
};
