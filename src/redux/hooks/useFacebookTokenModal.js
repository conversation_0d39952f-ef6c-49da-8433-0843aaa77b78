import { useDispatch, useSelector } from 'react-redux';
import {
    setShowTokenModal,
    setHasShownModal,
    handleModalClose,
    showModal,
} from '../features/facebookTokenModalSlice';

// Selectors
const selectShowTokenModal = (state) => state.facebookTokenModal.showTokenModal;
const selectHasShownModal = (state) => state.facebookTokenModal.hasShownModal;

const useFacebookTokenModal = () => {
    const dispatch = useDispatch();

    const showTokenModal = useSelector(selectShowTokenModal);
    const hasShownModal = useSelector(selectHasShownModal);

    const actions = {
        setShowTokenModal: (value) => dispatch(setShowTokenModal(value)),
        setHasShownModal: (value) => dispatch(setHasShownModal(value)),
        handleModalClose: () => dispatch(handleModalClose()),
        showModal: () => dispatch(showModal()),
    };

    return {
        showTokenModal,
        hasShownModal,
        ...actions,
    };
};

export default useFacebookTokenModal;
