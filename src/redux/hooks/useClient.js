import { useDispatch, useSelector } from 'react-redux';
import {
    // Sync actions
    setSelectedSource,
    setActiveTab,
    setFilterStatus,
    setTeamMembers,
    setLeads,
    setLeadStatusCounts,
    setDisableAddMember,
    setCurrentPage as setCurrentPageAction,
    setRecordsPerPage as setRecordsPerPageAction,
    setNoCommunicationLeads,
    // Async thunks (import the original names & aliases)
    handleSocialIconClickThunk,
    handleAddLeadThunk,
    handleAssignTeamMemberThunk,
    handleUnAssignTeamMemberThunk,
    handleAutoAssignThunk,
    handleAddTeamMemberThunk,
    handleAddAdminThunk,
    handleExportLeadsThunk,
    handleExportClientsThunk,
    handleImportLeadsThunk,
    handleAdminTeamMemberStatusThunk,
    handleDeleteTeamMemberThunk,
    handleDeleteAdminTeamMemberThunk,
    filterLeadsBySource,
    handleResetLead,
    // Aliases
    addLead,
    importLeads,
    deleteTeamMember,
    deleteAdminTeamMember,
    handleAddAdminTeamMember,
} from '../features/clientSlice';

// Selectors
const selectLeadStatusCounts = (state) => state.client.leadStatusCounts;
const selectSelectedSource = (state) => state.client.selectedSource;
const selectLeads = (state) => state.client.leads;
const selectTeamMembers = (state) => state.client.teamMembers;
const selectActiveTab = (state) => state.client.activeTab;
const selectFilterStatus = (state) => state.client.filterStatus;
const selectDisableAddMember = (state) => state.client.disableAddMember;
const selectLoading = (state) => state.client.loading;
const selectError = (state) => state.client.error;
const selectCurrentPage = (state) => state.client.currentPage;
const selectRecordsPerPage = (state) => state.client.recordsPerPage;
const selectNoCommLeads = (state) => state.client.noCommunicationLeads;

/**
 * Hook exposing client slice state and actions.
 */
const useClient = () => {
    const dispatch = useDispatch();

    // State
    const leadStatusCounts = useSelector(selectLeadStatusCounts);
    const selectedSource = useSelector(selectSelectedSource);
    const leads = useSelector(selectLeads);
    const noCommunicationLeads = useSelector(selectNoCommLeads);
    const teamMembers = useSelector(selectTeamMembers);
    const activeTabState = useSelector(selectActiveTab);
    const filterStatus = useSelector(selectFilterStatus);
    const disableAddMember = useSelector(selectDisableAddMember);
    const loading = useSelector(selectLoading);
    const error = useSelector(selectError);
    const currentPage = useSelector(selectCurrentPage);
    const recordsPerPage = useSelector(selectRecordsPerPage);

    // Sync actions
    const syncActions = {
        setSelectedSource: (payload) => dispatch(setSelectedSource(payload)),
        setActiveTab: (payload) => dispatch(setActiveTab(payload)),
        setFilterStatus: (payload) => dispatch(setFilterStatus(payload)),
        setTeamMembers: (payload) => dispatch(setTeamMembers(payload)),
        setLeads: (payload) => dispatch(setLeads(payload)),
        setNoCommunicationLeads: (payload) => dispatch(setNoCommunicationLeads(payload)),
        setLeadStatusCounts: (payload) => dispatch(setLeadStatusCounts(payload)),
        setDisableAddMember: (payload) => dispatch(setDisableAddMember(payload)),
        setCurrentPage: (payload) => dispatch(setCurrentPageAction(payload)),
        setRecordsPerPage: (payload) => dispatch(setRecordsPerPageAction(payload)),
    };

    // Async actions
    const asyncActions = {
        handleSocialIconClick: (source) => dispatch(handleSocialIconClickThunk(source)),
        handleAddLead: (values, onHide, resetForm) =>
            dispatch(handleAddLeadThunk({ values, onHide, resetForm })),
        handleAssignTeamMember: (leadId, memberId) =>
            dispatch(handleAssignTeamMemberThunk({ leadId, memberId })),
        handleUnAssignTeamMember: (leadId) => dispatch(handleUnAssignTeamMemberThunk(leadId)),
        handleAutoAssign: (status) => dispatch(handleAutoAssignThunk(status)),
        handleAddTeamMember: (values, handleClose, resetForm) =>
            dispatch(handleAddTeamMemberThunk({ values, handleClose, resetForm })),
        handleAddAdmin: (values, handleClose, resetForm) =>
            dispatch(handleAddAdminThunk({ values, handleClose, resetForm })),
        handleExportLeads: (clientId) => dispatch(handleExportLeadsThunk(clientId)),
        handleExportClients: () => dispatch(handleExportClientsThunk()),
        handleImportLeads: (data) => dispatch(handleImportLeadsThunk(data)),
        handleAdminTeamMemberStatus: (id, status) =>
            dispatch(handleAdminTeamMemberStatusThunk({ id, status })),
        handleDeleteTeamMember: (teamMemberId, handleClose, afterDelete) =>
            dispatch(handleDeleteTeamMemberThunk({ teamMemberId, handleClose, afterDelete })),
        handleDeleteAdminTeamMember: (teamMemberId, handleClose) =>
            dispatch(handleDeleteAdminTeamMemberThunk({ teamMemberId, handleClose })),
        filterLeadsBySource: (params) => dispatch(filterLeadsBySource(params)),
        handleResetLead: (leadId) => dispatch(handleResetLead(leadId)),
        // Aliases for backward compatibility
        addLead: (args) => dispatch(addLead(args)),
        importLeads: (data) => dispatch(importLeads(data)),
        deleteTeamMember: (teamMemberId, handleClose, afterDelete) =>
            dispatch(deleteTeamMember({ teamMemberId, handleClose, afterDelete })),
        deleteAdminTeamMember: (teamMemberId, handleClose) =>
            dispatch(deleteAdminTeamMember({ teamMemberId, handleClose })),
        handleAddAdminTeamMember: (args) => dispatch(handleAddAdminTeamMember(args)),
    };

    return {
        // State
        leadStatusCounts,
        selectedSource,
        leads,
        noCommunicationLeads,
        teamMembers,
        activeTab: activeTabState,
        filterStatus,
        disableAddMember,
        loading,
        error,
        currentPage,
        recordsPerPage,

        // Sync actions
        ...syncActions,

        // Async actions
        ...asyncActions,
    };
};

export default useClient;

// Export thunks directly
export {
    handleSocialIconClickThunk,
    handleAddLeadThunk,
    handleAssignTeamMemberThunk,
    handleUnAssignTeamMemberThunk,
    handleAutoAssignThunk,
    handleAddTeamMemberThunk,
    handleAddAdminThunk,
    handleExportLeadsThunk,
    handleExportClientsThunk,
    handleImportLeadsThunk,
    handleAdminTeamMemberStatusThunk,
    handleDeleteTeamMemberThunk,
    handleDeleteAdminTeamMemberThunk,
    filterLeadsBySource,
    handleResetLead,
};
