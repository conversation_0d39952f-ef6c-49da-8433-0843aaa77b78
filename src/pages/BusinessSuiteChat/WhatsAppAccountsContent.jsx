import { useEffect, useState } from "react";
import {
  Container,
  // Dropdown, Nav
} from "react-bootstrap";
import { FaWhatsapp } from "react-icons/fa6";
// import { HiDotsHorizontal } from "react-icons/hi";
import {
  // HiMiniArrowUturnRight,
  HiOutlineXMark,
} from "react-icons/hi2";
// import { FiEdit } from "react-icons/fi";
import Tab from "react-bootstrap/Tab";
import { useSelector } from "react-redux";
import metaService from "../../services/integrations/meta";
import { useMetaBusinessChatContext } from "../../context/MetaBusinessChatContext";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import FilterDataSection from "./FilterDataSection";
import { IoIosSend } from "react-icons/io";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import SendMessageModal from "../../components/Modals/SendMessageModal";
import Cookies from "js-cookie";

const WhatsAppAccountsContent = () => {
  const { user } = useSelector((state) => state.auth);
  const accessToken =
    user?.user?.["access-token"] || Cookies.get("access_token");
  const [loading, setLoading] = useState(true);
  const {
    selectedWhatsAccount,
    setWhatsAppAccountDetails,
    whatsAppAccountDetails,
    setConversations,
    setSelectedWhatsAccount,
  } = useMetaBusinessChatContext();
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  useEffect(() => {
    if (selectedWhatsAccount) {
      setLoading(true);
      const fetchData = async () => {
        const response = await metaService.getWhatsAppAccountDetails({
          accessToken,
          id: selectedWhatsAccount?.id,
        });
        setWhatsAppAccountDetails(response);
        setConversations(
          response?.conversation_analytics?.data[0]?.data_points,
        );
        setLoading(false);
      };
      fetchData();
    }
  }, [selectedWhatsAccount]);
  return (
    <Container className={"px-4"}>
      {loading ? (
        <FetchingDataLoading />
      ) : (
        <Tab.Container id="left-tabs-example" defaultActiveKey="summary">
          <div
            style={{ borderBottom: "1px solid rgb(211, 211, 211)" }}
            className={"py-4"}
          >
            <div
              className={"d-flex justify-content-between align-items-center"}
            >
              <div className={"d-flex text-start align-items-center mb-3"}>
                {selectedWhatsAccount?.image === "dummy" ||
                !selectedWhatsAccount?.image ? (
                  <div
                    className={
                      "text-center p-2 rounded-circle bg-grey-suit me-3"
                    }
                    style={{ width: 45, height: 45 }}
                  >
                    <FaWhatsapp
                      className={"text-center"}
                      color={"gray"}
                      size={30}
                    />
                  </div>
                ) : (
                  <img
                    width={60}
                    height={60}
                    src={selectedWhatsAccount?.image}
                    alt={"User"}
                    className={"integration-account rounded-circle"}
                  />
                )}
                <div className={"d-flex flex-column"}>
                  <div className={"fw-bold"}>
                    {whatsAppAccountDetails?.name}
                  </div>
                  <div>ID: {whatsAppAccountDetails?.id}</div>
                  <div>Owned by: {selectedWhatsAccount?.ownedBy}</div>
                </div>
              </div>
              <div
                className={
                  "text-start d-flex justify-content-start align-items-center"
                }
              >
                <IoIosSend
                  size={25}
                  role={"button"}
                  onClick={() => setShowCenteredModal(true)}
                />
                {/*<Dropdown className={"ms-4"}>*/}
                {/*  <Dropdown.Toggle*/}
                {/*    variant="light"*/}
                {/*    id="dropdown-basic"*/}
                {/*    className={"team-actions-button"}*/}
                {/*  >*/}
                {/*    <HiDotsHorizontal />*/}
                {/*  </Dropdown.Toggle>*/}

                {/*  <Dropdown.Menu className={"team-actions-menu"}>*/}
                {/*    <Dropdown.Item*/}
                {/*      className={*/}
                {/*        "d-flex justify-content-between align-items-center"*/}
                {/*      }*/}
                {/*    >*/}
                {/*      Option 1*/}
                {/*      <HiMiniArrowUturnRight />*/}
                {/*    </Dropdown.Item>*/}
                {/*    <Dropdown.Item*/}
                {/*      className={*/}
                {/*        "d-flex justify-content-between align-items-center text-secondary"*/}
                {/*      }*/}
                {/*    >*/}
                {/*      Option 2 <FiEdit />*/}
                {/*    </Dropdown.Item>*/}
                {/*    <Dropdown.Item*/}
                {/*      className={*/}
                {/*        "d-flex justify-content-between align-items-center text-danger fw-bold"*/}
                {/*      }*/}
                {/*    >*/}
                {/*      Option 3 <HiOutlineXMark />*/}
                {/*    </Dropdown.Item>*/}
                {/*  </Dropdown.Menu>*/}
                {/*</Dropdown>*/}
                <HiOutlineXMark
                  size={25}
                  className={"ms-4"}
                  role={"button"}
                  onClick={() => setSelectedWhatsAccount(false)}
                />
              </div>
            </div>
            {/*<Nav variant="pills" className="justify-content-start">*/}
            {/*  <Nav.Item>*/}
            {/*    <Nav.Link eventKey="summary">Summary</Nav.Link>*/}
            {/*  </Nav.Item>*/}
            {/*  <Nav.Item className={"ms-3"}>*/}
            {/*    <Nav.Link eventKey="people">People</Nav.Link>*/}
            {/*  </Nav.Item>*/}
            {/*  <Nav.Item className={"ms-3"}>*/}
            {/*    <Nav.Link eventKey="partners">Partners</Nav.Link>*/}
            {/*  </Nav.Item>*/}
            {/*</Nav>*/}
          </div>
          <div
            style={{ borderBottom: "1px solid rgb(211, 211, 211)" }}
            className={"py-4"}
          >
            <h4 className={"fw-bold"}>Summary</h4>
            <div className={"my-3"}>
              Owned By: {whatsAppAccountDetails?.name}
            </div>
            <div>WhatsApp account ID: {whatsAppAccountDetails?.id}</div>
          </div>

          <div className={"py-4"}>
            <FilterDataSection />
          </div>
        </Tab.Container>
      )}
      <CenteredModal
        show={showCenteredModal}
        children={
          <SendMessageModal handleClose={() => setShowCenteredModal(false)} />
        }
        onHide={() => setShowCenteredModal(false)}
      />
    </Container>
  );
};

export default WhatsAppAccountsContent;
