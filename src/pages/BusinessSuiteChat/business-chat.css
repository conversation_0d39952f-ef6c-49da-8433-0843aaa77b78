.business-chat {
    min-height: 600px;
}
.business-chat .search-icon {
    position: absolute;
    right: 2%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.chat-btn-filter {
    background: #FAF9F6;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    border-radius: 5px;
    align-content: center;
    padding: 5px;
    cursor: pointer;
}

.chat-user-logo {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 50px;
}

.chat-user-logo svg {
    background-color: #FFFF;
    border-radius: 50%;
}

.chat-content {
    display: flex;
    justify-content: space-between;
}

.messages-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 1rem;
}

.message-action-btn {
    background: #FAF9F6;
    box-shadow: rgba(0, 0, 0, 0.05) 0 6px 24px 0, rgba(0, 0, 0, 0.08) 0 0 0 1px;
    cursor: pointer;
}

.message-action-btn:not(:first-child) {
    margin-left: 1rem;
}

/* chat component */
.chat-box {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 500px;
    position: relative;
}

.messages-wrapper {
    padding: 30px;
    flex: 1;
    overflow-y: auto;
    padding-bottom: 50px;
    max-height: 480px;
}

/* send message */
.send-message {
    position: sticky;
    bottom: 0;
    width: 100%;
    padding: 15px 10px;
    display: flex;
    border-bottom-right-radius: 10px;
    justify-content: space-between;
    background-color: #fff;
    z-index: 10;
    border-top: 1px solid #e0e0e0;
}

/* Enhanced attachment preview container */
.attachment-preview-container {
    background: linear-gradient(to right, #f8f9fa, #ffffff);
    border: 1px solid rgba(0, 123, 255, 0.2);
    border-radius: 12px;
    padding: 15px;
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 100%;
    margin-bottom: 8px;
    z-index: 20;
    box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.08);
    animation: slide-up 0.3s ease-out;
    transition: all 0.3s ease;
    max-height: 250px;
    overflow-y: auto;
}

/* Animation for attachment preview */
@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments for attachment preview */
@media (max-width: 768px) {
    .attachment-preview-container {
        padding: 12px;
        border-radius: 10px;
        max-height: 200px;
    }
}

/* Hover effect for attachment preview */
.attachment-preview-container:hover {
    box-shadow: 0 -6px 20px rgba(0, 123, 255, 0.15);
    border-color: rgba(0, 123, 255, 0.3);
}

.chat-container {
    max-height: 650px;
    overflow-y: auto;
}

/*.chat-container::-webkit-scrollbar {*/
/*    display: none;*/
/*}*/

.messages-wrapper::-webkit-scrollbar {
    display: none;
}

.chat-bubble {
    border-radius: 20px 20px 20px 0;
    padding: 0 10px;
    background-color: rgb(211, 211, 211);
    width: max-content;
    max-width: calc(100% - 50px);
    box-shadow: -2px 2px 1px 1px rgb(176, 176, 176);
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    color: #333;
}
.chat-bubble.right {
    margin-left: auto;
    border-radius: 20px 20px 0 20px;
    background-color: #0D6EFD;
    box-shadow: -2px 2px 1px 1px #0351c2;
    color: #FFFFFF;
}
.chat-bubble__left {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}
.user-name {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 0.9rem;
    color: #1c2c4c;
}
.user-message {
    word-break: break-all;
    white-space: pre-wrap; /* Preserve spaces and line breaks */
}
.message-time {
    display: block;
    text-align: right;
}

.send-message > input {
    height: 40px;
    padding: 10px 10px;
    border-radius: 5px 0 0 5px;
    border: 1px solid rgb(211, 211, 211);
    background-color: white;
    color: #1c2c4c;
    font-size: 1rem;
    flex-grow: 1;
}
.send-message > input::placeholder {
    color: #ddd;
}
.send-message > :is(input, button):focus {
    outline: none;
    border-bottom: 1px solid #7cc5d9;
}
.send-message > button {
    border-radius: 0 5px 5px 0;
    min-height: 2.5rem;
    color: #242443;
    border: 1px solid rgb(211, 211, 211);
    background-color: #FFFFFF;
    font-weight: 600;
}

.single-chat-container {
    cursor: pointer;
}

.single-chat-container:hover {
    background-color: rgb(229, 229, 229);
}

.selected-chat {
    background-color: rgb(229, 229, 229);
    border-right: 2px solid blue;
}

.selectedWhats-chat {
    background-color: rgb(229, 229, 229);
    border-right: 2px solid #25D366;
}

@keyframes dropIn {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.avatar-drop-in {
    animation: dropIn 0.3s ease-out;
}

.tab-custom.nav-pills .nav-link.active.all-tab, .tab-custom .nav-pills .show>.nav-link.all-tab {
    background-color: #007bff; /* Blue for All messages */
    color: white;
}

.tab-custom.nav-pills .nav-link.active.messenger-tab {
    background-color:#0084ff;
    color: white;
}

.tab-custom.nav-pills .nav-link.active.instagram-tab {
    background:linear-gradient(45deg, #405de6, #5851db, #833ab4, #c13584, #e1306c, #fd1d1d);
    color: white;
}

.tab-custom.nav-pills .nav-link.active.whatsapp-tab {
    background:#25D366;
    color: white;
}

.custom-phone-whatsapp .PhoneInputCountry {
    position: unset;
    transform: none;
}

.custom-phone-whatsapp input {
    border: unset;
}

.custom-phone-whatsapp .PhoneInputCountrySelect {
    width: 40px;
}

.meta-tab-custom.nav-pills .nav-link.all-tab, .meta-tab-custom.nav-pills .nav-link.active {
    box-sizing: border-box;
    border: 1px solid rgb(0, 0, 0);
    border-radius: 50px;
    background-color: white;
    color: black;
}

.meta-tab-custom.nav-pills .nav-link {
    padding: 0.2rem 0.5rem;
    color: #000000;
}

.send-message {
    width: 100%;
}

.input-container {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    width: 100%;
}

.form-input__input {
    font-size: 16px;
    color: #333;
    padding: 8px;
    border: none;
    outline: none;
    flex-grow: 1;
}

.attachment-label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #f5f5f5;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.attachment-icon {
    transition: color 0.3s ease;
}

.attachment-preview-container {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 12px;
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 100%;
    margin-bottom: 10px;
    z-index: 20;
}

.attachment-preview {
    position: relative;
    max-width: 100%;
    height: auto;
}

.preview-image {
    width: 100%;
    max-width: 250px;
    max-height: 250px;
    border-radius: 8px;
    object-fit: cover;
}

.file-info {
    text-align: center;
    color: #666;
}

.remove-btn {
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    padding: 0.3rem 0.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: absolute;
    top: 0;
    right: 0;
    color: red;
}

.remove-btn:hover {
    background: rgba(255, 255, 255, 1);
}

.send-btn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.send-btn:hover {
    background: #0056b3;
    color: white;
}

.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5;
  color: #999;
}

.img-video-modal-preview {
    position: relative;
}

.modal-close-icon {
    position: absolute;
    top: 10%;
    right: 0;
    cursor: pointer;
}
