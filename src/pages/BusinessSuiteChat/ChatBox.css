.chat-box {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drag-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 90px; /* Leave space for the send message component */
  background-color: rgba(0, 123, 255, 0.15);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 3px dashed rgba(0, 123, 255, 0.5);
  border-radius: 8px;
  pointer-events: none;
  margin: 0 auto;
  max-width: calc(100% - 20px);
}

.drag-overlay-content {
  background-color: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  color: #0d6efd;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.drag-overlay-content p {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.6);
  z-index: 50;
}
