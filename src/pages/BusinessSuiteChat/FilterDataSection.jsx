// import React, { useState } from "react";
import Form from "react-bootstrap/Form";
import { Button, Col, Row } from "react-bootstrap";
// import moment from "moment";
import metaService from "../../services/integrations/meta";
import { useSelector } from "react-redux";
import { useMetaBusinessChatContext } from "../../context/MetaBusinessChatContext";
import Cookies from "js-cookie";
import {
  setStartDate,
  setEndDate,
  filterWhatsAppConversations,
} from "../../redux/features/metaBusinessChatSlice"; // Adjust path as necessary

const FilterDataSection = () => {
  const {
    selectedWhatsAccount,
    startDate,
    endDate,
    setStartDate,
    setEndDate,
    setConversations,
    conversations,
  } = useMetaBusinessChatContext();
  const { user } = useSelector((state) => state.auth);
  const accessToken =
    user?.user?.["access-token"] || Cookies.get("access_token");

  const params = {
    accessToken,
    id: selectedWhatsAccount?.id,
    startDate,
    endDate,
  };
  const filterConversations = async () => {
    const response = await metaService.getWhatsAppFilterConversations(params);
    setConversations(response?.conversation_analytics?.data[0]?.data_points);
  };

  const RenderFilterSection = () => (
    <div className="my-4">
      <h5 className="fw-bold">Filter Conversations</h5>
      <Form>
        <Row className="align-items-end">
          <Col md={4}>
            <Form.Group controlId="startDate">
              <Form.Label>Start Date</Form.Label>
              <Form.Control
                type="datetime-local"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group controlId="endDate">
              <Form.Label>End Date</Form.Label>
              <Form.Control
                type="datetime-local"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Button variant="primary" onClick={filterConversations}>
              Filter
            </Button>
          </Col>
        </Row>
      </Form>
      <div className="mt-4">
        <h5 className="fw-bold">Filtered Results</h5>
        {conversations?.length > 0 ? (
          <ul className="list-group">
            {conversations?.map((item, index) => (
              <li key={index} className="list-group-item">
                <div>Start: {item?.start}</div>
                <div>End: {item?.end}</div>
                <div>Conversations: {item?.conversation}</div>
                <div>Cost: {item?.cost}</div>
              </li>
            ))}
          </ul>
        ) : (
          <p>No results found for the selected date range.</p>
        )}
      </div>
    </div>
  );

  return <RenderFilterSection />;
};

export default FilterDataSection;
