import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { sendWhatsMessage } from "../metaBusinessChatSlice"; // Adjust path as necessary

const SendWhatsMessage = () => {
    const { selectedWhatsAccount, sendWhatsMessage } = useMetaBusinessChatContext();
    const [newMessage, setNewMessage] = useState("");
    const handleSendMessage = (e) => {
        e.preventDefault();
        if (selectedWhatsAccount && newMessage.trim() !== "") {
            sendWhatsMessage({
                // sender: user.user.id,
                text: {body: newMessage.trim()},
                messaging_product: "whatsapp",
                to: selectedWhatsAccount?.sender,
                type: "text"
            });
            setNewMessage("");
        }
    };
    return (
        <form className="send-message" onSubmit={handleSendMessage}>
            <label htmlFor="messageInput" hidden>
                Enter Message
            </label>
            <input
                id="messageInput"
                name="messageInput"
                type="text"
                className="form-input__input"
                placeholder="type message..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
            />
            <button type="submit">Send</button>
        </form>
    );
};

export default SendWhatsMessage;