// import adidasPage from "../../assets/media/Icons/adidas_page.svg";
// import {TbExclamationMark} from "react-icons/tb";
import {
  selectMessages,
  selectSelectedChat,
  selectActiveFilter,
  setSelectedChat,
  setMessages,
  setNestedMessages,
  selectWhatsappChatMessages,
  selectSelectedWhatsappChat,
  formatDate,
} from "../../redux/features/metaBusinessChatSlice";
import { BsArrowLeft } from "react-icons/bs";
import { FaUserCircle } from "react-icons/fa";
import ChatBox from "./ChatBox";
import CommentsButton from "../../components/CommentsButton";
import CommentsModal from "../../components/CommentsModal/CommentsModal";
import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";

const ChatContent = () => {
  const dispatch = useDispatch();

  const whatsappChatMessages = useSelector(selectWhatsappChatMessages);
  const selectedWhatsappChat = useSelector(selectSelectedWhatsappChat);
  const messages = useSelector(selectMessages);
  const selectedChat = useSelector(selectSelectedChat);
  const activeFilter = useSelector(selectActiveFilter);

  // Add state to track the current time display
  const [currentTime, setCurrentTime] = useState("");

  // Loading flags
  const fetchingMessages = useSelector(
    (state) => state.metaBusinessSuite.loading.fetchingMessages
  );
  const fetchingWhatsAppMessages = useSelector(
    (state) => state.metaBusinessSuite.loading.fetchingWhatsAppMessages
  );
  const loadingMessages = fetchingMessages || fetchingWhatsAppMessages;

  // Update the time whenever messages or selectedWhatsappChat changes
  useEffect(() => {
    if (activeFilter === "whatsapp" && selectedWhatsappChat?.updated_at) {
      setCurrentTime(
        new Date(selectedWhatsappChat.updated_at).toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    } else if (selectedChat?.updated_time) {
      setCurrentTime(formatDate(selectedChat.updated_time));
    }
  }, [
    whatsappChatMessages,
    messages,
    selectedWhatsappChat,
    selectedChat,
    activeFilter,
  ]);

  const resetSelectedChat = () => {
    dispatch(setSelectedChat(null));
    dispatch(setMessages([]));
    dispatch(setNestedMessages([]));
  };

  // Show loader while messages are being fetched
  if (loadingMessages) {
    return (
      <div className="d-flex justify-content-center align-items-center w-100 h-100">
        <FetchingDataLoading />
      </div>
    );
  }

  return (
    <>
      {selectedChat || (activeFilter === "whatsapp" && selectedWhatsappChat) ? (
        <>
          <div
            className="chat-content p-2"
            style={{ borderBottom: "1px solid rgb(211, 211, 211)" }}
          >
            <div
              className={"d-flex justify-content-between align-items-center"}
            >
              <div className="d-flex align-items-center">
                {/*{selectedChat && selectedChat.avatar ?*/}
                {/*    <img src={selectedChat.avatar} alt={'User'} className={'integration-account'}/> :*/}
                {/*    <img src={adidasPage} alt={'User'} className={'integration-account'}/>}*/}
                {window.innerWidth < 1200 && (
                  <div
                    className="message-action-btn py-1 px-3"
                    onClick={resetSelectedChat}
                  >
                    <BsArrowLeft />
                  </div>
                )}
                {activeFilter === "whatsapp" ? (
                  <>
                    {selectedWhatsappChat?.image ? (
                      <img
                        width={60}
                        height={60}
                        src={selectedWhatsappChat?.image}
                        alt={"User"}
                        className={"integration-account rounded-circle"}
                      />
                    ) : (
                      <FaUserCircle
                        className={"rounded-circle bg-white"}
                        color={"gray"}
                        size={60}
                      />
                    )}
                    <div className={"ms-3"}>
                      {selectedWhatsappChat?.sender_name}
                    </div>
                  </>
                ) : activeFilter === "instagram" ? (
                  <>
                    {selectedChat?.participants?.data[1]?.image === "dummy" ? (
                      <FaUserCircle
                        className={"rounded-circle bg-white"}
                        color={"gray"}
                        size={60}
                      />
                    ) : (
                      <img
                        width={60}
                        height={60}
                        src={selectedChat?.participants?.data[1]?.image}
                        alt={"User"}
                        className={"integration-account rounded-circle"}
                      />
                    )}
                    <div className={"ms-3"}>
                      {selectedChat?.participants?.data[1]?.username}
                    </div>
                  </>
                ) : (
                  <>
                    {selectedChat?.participants?.data[0]?.image === "dummy" ? (
                      <FaUserCircle
                        className={"rounded-circle bg-white"}
                        color={"gray"}
                        size={60}
                      />
                    ) : (
                      <img
                        width={60}
                        height={60}
                        src={selectedChat?.participants?.data[0]?.image}
                        alt={"User"}
                        className={"integration-account rounded-circle"}
                      />
                    )}
                    <div className={"ms-3"}>
                      {selectedChat?.participants?.data[0]?.name}
                    </div>
                  </>
                )}
              </div>

              {/* Comments Button */}
              <div className="d-flex align-items-center">
                <CommentsButton
                  disabled={
                    !(activeFilter === "whatsapp"
                      ? selectedWhatsappChat
                      : selectedChat)
                  }
                />
              </div>
            </div>
            {/*<div className={"messages-actions"}>*/}
            {/*    <div className={"message-action-btn py-1 px-3"}>*/}
            {/*        <BsFillExclamationOctagonFill/>*/}
            {/*    </div>*/}

            {/*    <div className={"message-action-btn py-1 px-3"}>*/}
            {/*        <BsTrash3Fill/>*/}
            {/*    </div>*/}
            {/*    <div className={"message-action-btn py-1 px-3"}>*/}
            {/*        <FaStar/>*/}
            {/*    </div>*/}
            {/*    <div className={"message-action-btn py-1 px-3"}>*/}
            {/*        <BsEnvelopeFill/>*/}
            {/*    </div>*/}
            {/*    <div className={"message-action-btn py-1 px-3"}>*/}
            {/*        <TbExclamationMark color={"red"}/>*/}
            {/*    </div>*/}
            {/*    <div className={"message-action-btn py-1 px-3"}>*/}
            {/*        <FaCheck/>*/}
            {/*    </div>*/}
            {/*</div>*/}
          </div>
          {activeFilter === "whatsapp" && whatsappChatMessages?.length > 0 ? (
            <div className={"text-center"} style={{ color: "#d5d5d5" }}>
              {currentTime}
            </div>
          ) : messages?.length > 0 ? (
            <div className={"text-center"} style={{ color: "#d5d5d5" }}>
              {currentTime}
            </div>
          ) : null}
          {/*<div>{message?.text}</div>*/}
          <ChatBox />
        </>
      ) : null}

      {/* Comments Modal */}
      <CommentsModal />
    </>
  );
};

export default ChatContent;
