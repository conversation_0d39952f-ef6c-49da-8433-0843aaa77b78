.admin-sign-in-page {
    background: url(../../assets/media/admin-signBG.png) no-repeat center center;
    background-size: cover;
    height: 100vh;
    filter: blur(3px);
    -webkit-filter: blur(3px);
    -moz-filter: blur(3px);
    -o-filter: blur(3px);
    -ms-filter: blur(3px);
    position: relative;
}

.admin-sign-in-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 350px;
}

.admin-sign-in-form {
    filter: none;
    -webkit-filter: none;
    -moz-filter: none;
    -o-filter: none;
    -ms-filter: none;
    box-sizing: border-box;
    border: 2px solid rgb(255, 255, 255);
    border-radius: 24px;
    backdrop-filter: blur(25px);
    background: rgba(82, 82, 82, 0.5);
    padding: 40px 35px;
}

.admin-sign-in-form input {
    border-radius: 12px;
    background: rgb(37, 37, 37);
}

.admin-sign-in-form input:focus {
    background: rgb(37, 37, 37);
}

.admin-sign-in-form label {
    color: rgb(255, 255, 255);
    font-size: 1rem;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0;
    text-align: left;
}

.admin-sign-in-form button {
    border-radius: 12px;
    background: rgb(255, 255, 255);
}