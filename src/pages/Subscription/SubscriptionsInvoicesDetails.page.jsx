import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button, Table } from "react-bootstrap";
import getAllInvoicesSubscriptionsApi from "../../services/package/get-subscription-invoices.api";
import "./subscription.css";
import { cancelSubscriptionApi } from "../../services/package/cancel-subscription.api";
import useAuth from "../../redux/hooks/useAuth";
import Cookies from "js-cookie";
import { format, parseISO } from "date-fns";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";

function SubscriptionsInvoicesDetailsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const [userSubDetails, setUserSubDetails] = useState([]);
  const [userInvoices, setUserInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const { handlePurchase } = useAuth();

  // Check if user should have access to package features
  useEffect(() => {
    if (user && !shouldShowPackageFeatures(user?.user?.id)) {
      navigate("/unauthorized");
      return;
    }
  }, [user, navigate]);

  useEffect(() => {
    const fetchSubDetails = async () => {
      try {
        const userId = user?.user?.id;
        const response = await getAllInvoicesSubscriptionsApi(userId);
        setUserSubDetails(response?.data?.curunt);
        setUserInvoices(response?.data?.old);
      } catch (error) {
        console.error("Error fetching user subscription details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSubDetails();
  }, [user]);
  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "--";
    try {
      const date = parseISO(dateString);
      const formattedDate = format(date, "yyyy-MM-dd");
      const formattedTime = format(date, "HH:mm");
      return (
        <div className="date-time-container">
          <div className="date">{formattedDate}</div>
          <div className="time text-muted">{formattedTime}</div>
        </div>
      );
    } catch (error) {
      return "--";
    }
  };

  const handleCancel = async () => {
    try {
      const userId = user?.user?.id;
      const response = await cancelSubscriptionApi(userId);
      if (response?.status === 200) {
        alert("Subscription canceled successfully");
        const userData = Cookies.get("userData");
        if (userData) {
          const parsedUserData = JSON.parse(userData);
          parsedUserData.user.package_id = null;
          parsedUserData.user.package_name = null;
          parsedUserData.user.package_price = null;
          Cookies.set("userData", JSON.stringify(parsedUserData));
        }
        window.location.reload();
      }
      console.log("Cancel initiated");
    } catch (error) {
      console.error("Error canceling subscription:", error);
    }
  };

  return (
    <section className={"container my-4"}>
      {loading ? (
        <div className="text-center p-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">{t("common.loading")}</span>
          </div>
        </div>
      ) : (
        <>
          <div className={"text-center mb-4"}>
            <h2 className={"text-capitalize fw-bold"}>
              {t("subscription.title")}
            </h2>
            <p className="fw-semibold">{t("subscription.invoices.details")}</p>
          </div>

          <p className="fw-semibold mt-4">
            {t("subscription.invoices.currentPlan")}
          </p>
          <div className="radial-gradient-border-static">
            <p>
              {t("subscription.invoices.package")}:{" "}
              <span className="ms-2">
                {userSubDetails?.package?.title || "--"}
              </span>
            </p>
            <p>
              {t("subscription.invoices.amount")}:{" "}
              <span className="ms-2">
                {userSubDetails?.package?.price !== undefined
                  ? `$${userSubDetails.package.price}`
                  : "--"}
              </span>
            </p>
            <p>
              {t("subscription.invoices.duration")}:{" "}
              <span className="ms-2">
                {userSubDetails?.package?.period
                  ? `${userSubDetails.package.period} ${t(
                      "subscription.invoices.months"
                    )}`
                  : "--"}
              </span>
            </p>
            <div className="d-flex justify-content-end gap-4">
              <Button
                className="submit-btn rounded-2 fs-6"
                onClick={() => handlePurchase()}
              >
                {t("subscription.invoices.renew")}
              </Button>
              <Button
                variant="none"
                className="cancel-btn rounded-2 fs-6"
                onClick={handleCancel}
              >
                {t("subscription.invoices.cancel")}
              </Button>
            </div>
          </div>

          <p className="fw-semibold mt-4">
            {t("subscription.invoices.paymentTerms")}
          </p>
          <div className="radial-gradient-border-static">
            <p className={"d-flex justify-content-between gap-2 w-25"}>
              {t("subscription.invoices.renewalDate")}:{" "}
              <span className="ms-2">
                {formatDate(userSubDetails?.subscription_at)}
              </span>
            </p>
            <p className={"d-flex justify-content-between gap-2 w-25"}>
              {t("subscription.invoices.endDate")}:{" "}
              <span className="ms-2">
                {formatDate(userSubDetails?.end_date)}
              </span>
            </p>
          </div>

          <p className="fw-semibold mt-4">
            {t("subscription.invoices.taxInvoices")}
          </p>
          <div className="table-responsive">
            <Table className="radial-gradient-border-static align-middle packages-table">
              <thead>
                <tr>
                  <th>{t("common.date")}</th>
                  <th>{t("common.price")}</th>
                  <th>{t("subscription.invoices.duration")}</th>
                  <th>{t("subscription.invoices.taxInvoiceNumber")}</th>
                </tr>
              </thead>
              <tbody>
                {userInvoices && userInvoices.length > 0 ? (
                  userInvoices.map((sub, index) => (
                    <tr key={index}>
                      <td>{formatDate(sub?.subscription_at)}</td>
                      <td>${sub?.package_price || 0}</td>
                      <td>
                        {sub?.package?.period
                          ? `${sub.package.period} ${t(
                              "subscription.invoices.months"
                            )}`
                          : "--"}
                      </td>
                      <td>{sub?.invoice_id || "--"}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="4" className="text-center">
                      {t("subscription.invoices.noInvoices")}
                    </td>
                  </tr>
                )}
              </tbody>
            </Table>
          </div>
        </>
      )}
    </section>
  );
}

export default SubscriptionsInvoicesDetailsPage;
