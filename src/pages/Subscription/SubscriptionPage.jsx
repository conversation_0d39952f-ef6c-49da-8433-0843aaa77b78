import "./subscription.css";
import { Nav, Row, Tab } from "react-bootstrap";
import MonthlyTabComponent from "../../components/Subscribtion/MonthlyTab.component";
import YearlyTabComponent from "../../components/Subscribtion/YearlyTab.component";
import { useEffect, useState } from "react";
import getAllPackagesApi from "../../services/package/get-all-packages.api";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";

const SubscriptionPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const packagesType = [
    { label: "Monthly", eventKey: "monthly" },
    { label: "Yearly", eventKey: "yearly" },
  ];
  const [packages, setPackages] = useState([]);
  const [loading, setLoading] = useState(false);

  // Check if user should have access to package features
  useEffect(() => {
    if (user && !shouldShowPackageFeatures(user?.user?.id)) {
      navigate("/unauthorized");
      return;
    }
  }, [user, navigate]);
  useEffect(() => {
    const fetchPackages = async () => {
      setLoading(true);
      try {
        const userId = user?.user?.id;
        const result = await getAllPackagesApi(userId);
        setPackages(result.data);
      } catch (error) {
        console.error("Error fetching packages:", error);
        toast.error("Failed to load packages");
      } finally {
        setLoading(false);
      }
    };
    fetchPackages();
  }, [user]);
  return (
    <>
      <h2>{t("subscription.title")}</h2>
      <section className={"container"}>
        <div className={"pricing-header"}>
          <h4 className={"text-uppercase fw-medium"}>OUR PLANS & PRICING</h4>
          <p>{t("subscription.subheading")}</p>
        </div>
        <Tab.Container id="packages-tabs" defaultActiveKey="monthly">
          <div
            className={
              "d-flex justify-content-between align-items-center mt-4 mx-5"
            }
          >
            <h2 className={"fw-bold text-dark"}>Simple, transparent pricing</h2>
            <Nav
              variant="pills"
              className={"justify-content-center roles-tabs-navs"}
            >
              {packagesType.map((tab, index) => (
                <Nav.Item key={index}>
                  <Nav.Link eventKey={tab.eventKey} className={"py-2 px-3"}>
                    {tab.label}
                  </Nav.Link>
                </Nav.Item>
              ))}
            </Nav>
          </div>
          <p className={"text-muted mt-2 mx-5"}>
            No contracts. No surprise fees.
          </p>
          <Row className={"mt-4"}>
            <Tab.Content>
              <Tab.Pane eventKey="monthly">
                <MonthlyTabComponent
                  packages={packages}
                  loading={loading}
                  setLoading={setLoading}
                />
              </Tab.Pane>
              <Tab.Pane eventKey="yearly">
                <YearlyTabComponent
                  packages={packages}
                  loading={loading}
                  setLoading={setLoading}
                />
              </Tab.Pane>
            </Tab.Content>
          </Row>
        </Tab.Container>
      </section>
    </>
  );
};

export default SubscriptionPage;
