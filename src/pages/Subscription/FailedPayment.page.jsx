import React, { useEffect } from "react";
import <PERSON><PERSON> from "lottie-react";
import failedAnimation from "../../assets/media/animations/animation_failed.json"; // Replace with the path to your failed animation JSON file
import { Link, useNavigate } from "react-router-dom";
import "./subscription.css";
import { Button } from "react-bootstrap";
import { useSelector } from "react-redux";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";

const PaymentFailedPage = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);

  // Check if user should have access to package features
  useEffect(() => {
    if (user && !shouldShowPackageFeatures(user?.user?.id)) {
      navigate("/unauthorized");
      return;
    }
  }, [user, navigate]);

  const handleRedirect = () => {
    setTimeout(() => {
      navigate("/packages");
    }, 5000);
  };

  React.useEffect(() => {
    handleRedirect();
  }, []);

  return (
    <div className="payment-failed">
      <Lottie
        animationData={failedAnimation}
        loop={false}
        className={"w-50 h-50"}
      />
      <h1>Payment Failed</h1>
      <p>
        We’re sorry, but something went wrong. You’ll be redirected shortly to
        try again.
      </p>
      <Link to={"/packages"} replace={true}>
        <Button
          className={
            "bg-danger rounded-pill fs-5 fw-bold border-0 align-self-center mb-3"
          }
          id="reset-button"
        >
          Go to Packages
        </Button>
      </Link>
    </div>
  );
};

export default PaymentFailedPage;
