/* Remove the existing modal styles that use react-bootstrap Modal */

/* New floating chat modal styles */
.floating-chat-modal-container {
  position: fixed;
  bottom: 100px;
  right: 50px;
  width: 0;
  height: 0;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  z-index: 1050;
  overflow: hidden;
  opacity: 0;
  transform-origin: bottom right;
  transform: scale(0.3);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  border: 1px solid #b9b8b8;
}

/* RTL support */
.floating-chat-modal-container.rtl {
  right: auto;
  left: 20px;
  transform-origin: bottom left;
}

.floating-chat-modal-container.show {
  opacity: 1;
  width: 50%;
  max-width: 500px;
  height: 50vh;
  transform: scale(0.8);
}

.floating-chat-modal-container.show.animation-complete {
  transform: scale(1);
}

.floating-chat-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
}

.floating-chat-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.floating-chat-modal-title {
  margin: 0;
  font-weight: 600;
  color: #212529;
}

.floating-chat-modal-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
}

/* Chat messages container */
.chat-messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Message input form */
.message-input-form {
  padding: 10px 15px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

/* Attachment thumbnail preview */
.attachment-preview {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  padding: 0.4rem 0.6rem;
  background-color: #e9ecef;
  border-radius: 8px;
  max-width: 100%;
  margin-bottom: 10px;
  overflow: hidden;
}

.attachment-thumbnail {
  max-width: 25vw;
  max-height: 20vh;
  width: auto;
  height: auto;
  object-fit: cover;
  flex-shrink: 0;
}

.attachment-filename {
  max-width: calc(100% - 100px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-attachment-btn-top {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 24px;
  height: 24px;
  padding: 0 !important;
  background-color: rgba(0,0,0,0.6) !important;
  border: none;
  color: #fff !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.remove-attachment-btn-top:hover {
  background-color: rgba(0,0,0,0.8) !important;
}

.input-group {
  position: relative;
}

.message-input {
  border-radius: 20px 0 0 20px !important;
  padding-right: 80px;
}

.attachment-button {
  background-color: transparent;
  border-left: none;
  cursor: pointer;
}

.send-button {
  border-radius: 0 20px 20px 0 !important;
}

/* Date header styles */
.date-header {
  position: sticky;
  top: 10px;
  z-index: 1;
  margin: 15px 0;
}

.date-label {
  background-color: rgba(240, 240, 240, 0.9) !important;
  font-size: 0.8rem;
  color: #666;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Message container styles */
.message-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 12px;
  position: relative;
}

.message-right {
  align-items: flex-end;
}

.message-left {
  align-items: flex-start;
}

/* Message bubble styles */
.message-bubble {
  display: flex;
  flex-direction: column;
  max-width: 75%;
  padding: 8px 12px;
  border-radius: 12px;
  position: relative;
}

/* For text messages, keep time inline */
.message-bubble.with-text {
  flex-direction: row;
  align-items: flex-end;
}

.client-message {
  background-color: #dcf8c6;
  border-top-right-radius: 2px;
}

.support-message {
  background-color: lightgray;
  border-top-left-radius: 2px;
}

/* Message content should expand to fill available space */
.message-content {
  width: 100%;
  word-break: break-word;
}

/* Inline time display */
.message-time-inline {
  font-size: 0.7rem;
  color: #888;
  align-self: flex-end;
  margin-top: 4px;
  text-align: right;
}

/* For text messages, keep time inline */
.message-bubble.with-text .message-time-inline {
  margin-left: 8px;
  margin-top: 0;
  white-space: nowrap;
  margin-bottom: 2px;
}

/* Sender name should be below the bubble */
.message-sender {
  font-size: 0.75rem;
  color: #666;
  margin-top: 2px;
  padding: 0 4px;
}

/* File attachment styles */
.file-attachment {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  text-decoration: none;
  color: #333;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  margin-bottom: 4px;
}

.file-attachment:hover {
  background-color: rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.message-audio {
  max-width: 250px;
  margin: 5px 0;
}

.message-image, .message-video {
  border-radius: 8px;
  max-width: 200px;
  max-height: 200px;
  object-fit: cover;
}

/* Animation for the modal appearing from the chat widget */
@keyframes popOut {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  70% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .floating-chat-modal-container.show {
    width: 95%;
    height: 70vh;
    bottom: 80px;
  }

  .message-container {
    max-width: 85%;
  }
}

/* For very small screens */
@media (max-width: 480px) {
  .floating-chat-modal-container.show {
    width: 100%;
    height: 80vh;
    bottom: 70px;
    right: 0;
    border-radius: 12px 12px 0 0;
  }
}

/* Keep existing message styling */
.message-image {
  border-radius: 8px;
  transition: transform 0.2s;
}

.message-image:hover {
  transform: scale(1.05);
}

.file-attachment {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
}

.file-attachment:hover {
  background-color: rgba(0, 0, 0, 0.1);
}



