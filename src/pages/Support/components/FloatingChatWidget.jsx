import React, { useEffect } from "react";
import { FaComments } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  setChatModalVisibility,
  selectUnreadCount,
  selectHasNewMessages,
  setupGlobalChatListener,
} from "../../../redux/features/supportChatSlice";
import "./FloatingChatWidget.css";

const FloatingChatWidget = () => {
  const dispatch = useDispatch();
  const unreadCount = useSelector(selectUnreadCount);
  const hasNewMessages = useSelector(selectHasNewMessages);
  const user = useSelector((state) => state.auth.user);

  // Get the RTL setting from i18n
  const { i18n } = useTranslation();
  const isRTL = i18n.language === "ar";

  // Ensure the global listener is set up
  useEffect(() => {
    if (user?.user?.id) {
      dispatch(setupGlobalChatListener(user.user.id));
    }
  }, [dispatch, user?.user?.id]);

  const handleClick = () => {
    dispatch(setChatModalVisibility(true));
  };

  return (
    <div
      className={`floating-chat-widget ${isRTL ? "rtl" : "ltr"} ${
        hasNewMessages ? "has-new-messages" : ""
      }`}
      onClick={handleClick}
      title="Live Support Chat"
    >
      <div className="widget-icon">
        <FaComments size={24} />
        {unreadCount > 0 && (
          <span className="unread-badge">
            {unreadCount > 9 ? "9+" : unreadCount}
          </span>
        )}
      </div>
      <div className="widget-label">Chat</div>
    </div>
  );
};

export default FloatingChatWidget;
