.floating-chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #0D6EFD;
  color: white;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1000;
}

/* LTR positioning (default) */
.floating-chat-widget.ltr {
  right: 30px;
}

/* RTL positioning */
.floating-chat-widget.rtl {
  left: 30px;
}

.widget-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.widget-label {
  margin: 0 10px;
  font-weight: 500;
}

.unread-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff3b30;
  color: white;
  font-size: 12px;
  font-weight: bold;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}

/* For RTL layout, position the badge on the left */
.floating-chat-widget.rtl .unread-badge {
  right: auto;
  left: -8px;
}

.floating-chat-widget:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Animation for new messages */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 132, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 132, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 132, 255, 0);
  }
}

.floating-chat-widget.has-new-messages {
  animation: pulse 2s infinite;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .floating-chat-widget {
    bottom: 90px; /* lift above mobile footer nav */
  }

  /* On mobile, place button on left for LTR to avoid overlapping visible-items dropdown */
  .floating-chat-widget.ltr {
    left: 20px;
    right: auto;
  }

  /* And on right for RTL */
  .floating-chat-widget.rtl {
    right: 20px;
    left: auto;
  }

  .widget-label {
    display: none;
  }

  .floating-chat-widget {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    padding: 0;
    justify-content: center;
  }
}
