.ticket-details {
  /* max-height: 70vh; */
  overflow-y: auto;
  padding-right: 10px;
  overflow-x: hidden;
}

.ticket-description {
  white-space: pre-line;
}

.attachment-item {
  background-color: #f8f9fa;
  transition: background-color 0.2s;
}

.attachment-item:hover {
  background-color: #e9ecef;
}

.attachment-icon {
  font-size: 1.2rem;
}

.reply-box {
  margin-top: 10px;
  margin-bottom: 10px;
}

/* Ticket History Styles */
.ticket-history-container {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 5px;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  background-color: #f8f9fa;
}

.history-card {
  border-left: 4px solid #ccc;
  margin-left: 10px;
  transition: transform 0.2s;
}

.history-card:hover {
  transform: translateX(5px);
}

.history-card[data-type="creation"] {
  border-left-color: #0d6efd; /* primary */
}

.history-card[data-type="status_change"] {
  border-left-color: #ffc107; /* warning */
}

.history-card[data-type="comment"] {
  border-left-color: #0dcaf0; /* info */
}

.history-card[data-type="attachment"] {
  border-left-color: #198754; /* success */
}

.history-card[data-type="resolution"] {
  border-left-color: #198754; /* success */
}

.history-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #f8f9fa;
  border-radius: 50%;
}

.history-icon {
  font-size: 1.2rem;
}

.history-timestamp {
  font-size: 0.8rem;
}

.history-text {
  color: #495057;
}

/* Attachment styles */
/* Enhanced attachment grid styles */
.attachments-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 15px;
}

.attachment-card {
  border: 1px solid #dee2e6;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.attachment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #adb5bd;
}

.attachment-preview {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 15px;
  flex-grow: 1;
  position: relative;
  overflow: hidden;
}

.attachment-preview:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.05);
}

.img-preview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.file-icon-container,
.pdf-preview-container,
.document-preview-container,
.video-preview-container,
.audio-preview-container {
  font-size: 3.5rem;
  color: #6c757d;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}

.preview-label {
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 8px;
}

.file-type {
  font-size: 0.8rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.attachment-info {
  padding: 12px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.attachment-filename {
  font-size: 0.9rem;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: #495057;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.preview-btn, .download-btn {
  flex: 1;
  font-size: 0.8rem;
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.2s;
  white-space: nowrap;
}

.preview-btn:hover:not(:disabled) {
  background-color: #6c757d;
  color: white;
}

.download-btn:hover {
  background-color: #0d6efd;
  color: white;
}

.preview-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Preview modal styling */
.file-preview-modal .modal-content {
  border-radius: 12px;
  overflow: hidden;
}

.file-preview-modal .modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.file-preview-modal .modal-body {
  padding: 0;
  background-color: #f8f9fa;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  min-height: 400px;
  max-height: 70vh;
  overflow: auto;
}

.image-preview-container img {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

.pdf-embed-container,
.document-embed-container {
  height: 70vh;
  min-height: 400px;
  width: 100%;
  overflow: hidden;
}

.video-preview-container {
  background-color: #000;
  padding: 20px;
}

.audio-preview-container {
  padding: 40px 20px;
  background-color: #f8f9fa;
}

@media (max-width: 1200px) {
  .attachment-actions {
    flex-direction: column;
    gap: 5px;
  }

  .preview-btn, .download-btn {
    width: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .attachments-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .attachments-grid {
    grid-template-columns: 1fr;
  }

  .attachment-preview {
    height: 140px;
  }
}

/* Dropzone styles */
.attachment-dropzone {
  margin-bottom: 15px;
}

.dropzone {
  border: 2px dashed #dee2e6;
  border-radius: 5px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
  background-color: #f8f9fa;
}

.dropzone:hover {
  border-color: #0d6efd;
}

.dropzone.active {
  border-color: #198754;
  background-color: rgba(25, 135, 84, 0.1);
}

.reply-attachments-preview {
  margin-bottom: 15px;
}

.reply-attachments-preview h6 {
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: #6c757d;
}

/* Add compact dropzone style */
.compact-dropzone {
  padding: 10px;
  margin-bottom: 10px;
}

.compact-dropzone p {
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* Make scrollbars prettier */
.ticket-details::-webkit-scrollbar,
.ticket-history-container::-webkit-scrollbar {
  width: 8px;
}

.ticket-details::-webkit-scrollbar-track,
.ticket-history-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.ticket-details::-webkit-scrollbar-thumb,
.ticket-history-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.ticket-details::-webkit-scrollbar-thumb:hover,
.ticket-history-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* History attachment preview */
.history-attachment-preview {
  margin-top: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #dee2e6;
}


