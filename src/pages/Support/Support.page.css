.support-page {
  background-color: #f8f9fa;
  min-height: calc(100vh - 60px);
}

.support-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #212529;
}

.tickets-table-container {
  background-color: #ffffff;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  overflow-x: auto;
}


.tickets-table th {
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: #e9ecef;
  vertical-align: middle;
}

.tickets-table td {
  font-size: 0.9rem;
  color: #212529;
  vertical-align: middle;
}

.no-tickets {
  color: #6c757d;
  font-style: italic;
}


.sortable-header {
  cursor: pointer;
}

.sortable-header:hover {
  background-color: #dde2e6;
}

.records-buttons-container {
  border: 1px solid #9DC41D;
  border-radius: 5px;
  padding: 4px;
}

.record-button-selected {
  border: 1px solid #9DC41D;
  background: #9DC41D;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.24);
  color: #FFFFFF;
  padding: 6px 10px;
  border-radius: 4px;
}

.record-button {
  border-radius: 4px;
  color: #000;
  padding: 6px 10px;
}

.data-table-pagination .page-link {
  margin: 10px 5px;
  color: rgba(0, 0, 0, 0.50);
  font-size: 1.25rem;
  font-weight: 400;
  border: unset;
  border-radius: 7px;
}

.data-table-pagination .active>.page-link {
  background: linear-gradient(220deg, #92C020 -9.71%, #CAD511 117.08%);
  color: #FFF;
}
