import { useState, useMemo, useEffect, useCallback, useRef } from "react";
import "./Support.page.css";
import CreateTicketModal from "./components/CreateTicketModal";
import TicketDetailsModal from "./components/TicketDetailsModal";
import { Table, Button, Badge, Form, Row, Col } from "react-bootstrap";
import { FaSort, FaSortUp, FaSortDown, FaComments } from "react-icons/fa";
import PaginationRecordsForReports from "../../components/Reports/PaginationRecordsForReports";
import { useDropzone } from "react-dropzone";
import {
  getAllTickets,
  getSingleTicket,
  createTicket,
  replyToTicket,
  searchTickets,
} from "../../services/tickets";
import { showErrorToast } from "../../utils/toast-success-error";
import {
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import useDebounce from "../../utils/use-debounce";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import { BsFillCaretDownFill } from "react-icons/bs";
import { useDispatch, useSelector } from "react-redux";
import {
  setChatModalVisibility,
  selectUnreadCount,
  selectShowChatModal,
} from "../../redux/features/supportChatSlice";

const SupportPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All");
  const [priorityFilter, setPriorityFilter] = useState("All");
  const [loading, setLoading] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [statusCounts, setStatusCounts] = useState({
    open: 0,
    inprogress: 0,
    closed: 0,
  });
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showTicketDetails, setShowTicketDetails] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [paginationLinks, setPaginationLinks] = useState([]);
  const [total, setTotal] = useState(0);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [recordsToDisplay, setRecordsToDisplay] = useState(10);
  const [isLoadingTicketDetails, setIsLoadingTicketDetails] = useState(false);
  const [loadingTicketId, setLoadingTicketId] = useState(null);
  const dispatch = useDispatch();
  const showChatModal = useSelector(selectShowChatModal);
  const unreadMessageCount = useSelector(selectUnreadCount);

  const columns = useTranslatedColumns();

  const handleViewTicket = async (ticketId) => {
    try {
      // Use a separate loading state for ticket details, not the main table loading state
      setIsLoadingTicketDetails(true);
      setLoadingTicketId(ticketId);

      const response = await getSingleTicket(ticketId);
      if (response && response.data) {
        setSelectedTicket(response.data);
        setShowTicketDetails(true);
      } else {
        showErrorToast("Failed to load ticket details");
      }
    } catch (error) {
      console.error("Error fetching ticket details:", error);
      showErrorToast("Failed to load ticket details");
    } finally {
      setIsLoadingTicketDetails(false);
      setLoadingTicketId(null);
    }
  };

  // Callback to receive updated ticket from modal and refresh local state
  const handleTicketUpdate = (updatedTicket) => {
    setTickets((prevTickets) => {
      const updatedTickets = prevTickets.map((t) =>
        t.id === updatedTicket.id ? { ...t, ...updatedTicket } : t
      );

      setStatusCounts(computeStatusCounts(updatedTickets));

      return updatedTickets;
    });

    // Also update selected ticket if it is currently open in modal
    if (selectedTicket && selectedTicket.id === updatedTicket.id) {
      setSelectedTicket(updatedTicket);
    }
  };

  // Memoize the columns to prevent unnecessary re-renders
  const memoizedColumns = useMemo(
    () =>
      columns.ticketsColumns(
        isLoadingTicketDetails,
        loadingTicketId,
        handleViewTicket
      ),
    [columns, isLoadingTicketDetails, loadingTicketId]
  );

  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } =
    useTable(
      {
        columns: memoizedColumns,
        data: tickets,
        initialState: {
          pageIndex: 0,
        },
      },
      useFilters,
      useSortBy
    );

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleCreateTicket = async (newTicket) => {
    setLoading(true);
    const response = await createTicket(newTicket);
    setTickets((prevTickets) => [response, ...prevTickets]);
    closeModal();
    setLoading(false);
  };

  // Create a ref to track in-flight requests
  const currentRequest = useRef(null);

  const fetchData = useCallback(
    async (
      url = `ticket?page=${currentPage}&number_of_records=${recordsPerPage}`
    ) => {
      // Add a console log to track when this runs

      // Cancel any in-flight request
      if (currentRequest.current) {
        currentRequest.current.abort();
      }

      // Create a new AbortController
      currentRequest.current = new AbortController();

      try {
        setLoading(true);
        setIsSearchActive(false);

        // Pass the signal to the API call
        const response = await getAllTickets(
          url,
          currentRequest.current.signal
        );

        // If the request was aborted, don't update state
        if (currentRequest.current.signal.aborted) {
          console.log("Request was aborted, not updating state");
          return;
        }

        if (response && response.data) {
          const { data, meta } = response;
          const { current_page, per_page, links, total, to } = meta;
          setTickets(data || []);
          setCurrentPage(current_page);
          setRecordsPerPage(per_page);
          setPaginationLinks(links);
          setTotal(total);
          setRecordsToDisplay(to);

          setStatusCounts(computeStatusCounts(data));
        } else {
          // Handle empty response
          setTickets([]);
          setPaginationLinks([]);
          setTotal(0);
          setRecordsToDisplay(0);
        }
      } catch (error) {
        // Only log and show error if it's not an abort error
        if (error.name !== "AbortError") {
          console.error("Error fetching tickets:", error);
          showErrorToast("Failed to load tickets");
          setTickets([]);
        } else {
          console.log("Request was aborted:", error);
        }
      } finally {
        // Only update loading state if the request wasn't aborted
        if (currentRequest.current && !currentRequest.current.signal.aborted) {
          setLoading(false);
        }
      }
    },
    [currentPage, recordsPerPage]
  );

  // Use a ref to track if we've already made the initial fetch
  const initialFetchDone = useRef(false);

  // Clean up the AbortController on unmount
  useEffect(() => {
    return () => {
      if (currentRequest.current) {
        currentRequest.current.abort();
      }
    };
  }, []); // Empty dependency array means this runs once on mount

  const debouncedSearchTerm = useDebounce(searchTerm, 1000);

  // Update the initial useEffect to prevent multiple calls
  useEffect(() => {
    // Only fetch data once on mount and if we haven't already fetched
    if (!initialFetchDone.current) {
      fetchData(`ticket?page=1&number_of_records=${recordsPerPage}`);
      initialFetchDone.current = true;
    }
  }, []); // Empty dependency array means this runs once on mount

  // Update the search/filter useEffect to prevent unnecessary calls
  useEffect(() => {
    // Skip this effect on initial render
    if (!initialFetchDone.current) {
      return;
    }

    console.log("Search/filter effect triggered", {
      debouncedSearchTerm,
      statusFilter,
      priorityFilter,
    });

    if (
      debouncedSearchTerm ||
      statusFilter !== "All" ||
      priorityFilter !== "All"
    ) {
      // Reset to page 1 when search/filter changes
      setCurrentPage(1);

      // Use searchTickets function directly
      setLoading(true);
      searchTickets(
        debouncedSearchTerm,
        1,
        recordsPerPage,
        statusFilter,
        priorityFilter
      )
        .then((response) => {
          if (response && response.data) {
            const { data, meta } = response;
            setTickets(data || []);
            setCurrentPage(meta.current_page);
            setPaginationLinks(meta.links);
            setTotal(meta.total);
            setRecordsToDisplay(meta.to);

            setStatusCounts(computeStatusCounts(data));
          }
        })
        .catch((error) => {
          console.error("Error searching tickets:", error);
          showErrorToast("Failed to search tickets");
        })
        .finally(() => setLoading(false));
    } else if (initialFetchDone.current) {
      // If all filters are cleared and we've already done the initial fetch,
      // fetch first page again
      fetchData("ticket?page=1&number_of_records=" + recordsPerPage);
    }
  }, [debouncedSearchTerm, statusFilter, priorityFilter]); // Remove recordsPerPage from dependencies

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    // Reset to page 1 when searching
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
    // Reset to page 1 when filtering
    setCurrentPage(1);
  };

  const handlePriorityFilterChange = (e) => {
    setPriorityFilter(e.target.value);
    // Reset to page 1 when filtering
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = async (url) => {
    try {
      setLoading(true);

      // Check if the URL already has number_of_records parameter
      if (!url.includes("number_of_records")) {
        // Add the current records per page to the URL
        const separator = url.includes("?") ? "&" : "?";
        url = `${url}${separator}number_of_records=${recordsPerPage}`;
      }

      console.log("Navigating to page with URL:", url);

      // Extract page number from URL if possible
      const pageMatch = url.match(/page=(\d+)/);
      if (pageMatch && pageMatch[1]) {
        const newPage = parseInt(pageMatch[1], 10);
        setCurrentPage(newPage);
      }

      // Make the API call
      const response = await getAllTickets(url);

      if (response && response.data) {
        const { data, meta } = response;

        // Update all state with the new data
        setTickets(data || []);
        setCurrentPage(meta.current_page);
        setPaginationLinks(meta.links);
        setTotal(meta.total);
        setRecordsToDisplay(meta.to);

        setStatusCounts(computeStatusCounts(data));
      }
    } catch (error) {
      console.error("Error navigating to page:", error);
      showErrorToast("Failed to navigate to page");
    } finally {
      setLoading(false);
    }
  };

  // Handle page size change
  const handlePageSizeChange = useCallback(
    async (size) => {
      // Add a console log to track when this runs
      console.log("handlePageSizeChange called with size:", size);

      try {
        // Set loading state
        setLoading(true);

        // Reset to page 1 when changing page size
        setCurrentPage(1);

        // Log the requested size to debug
        console.log("Changing records per page to:", size);

        // Construct URL with current filters if any
        let url = `ticket?page=1&number_of_records=${size}`;

        if (debouncedSearchTerm) {
          url += `&search=${encodeURIComponent(debouncedSearchTerm)}`;
        }

        if (statusFilter !== "All") {
          url += `&status=${encodeURIComponent(statusFilter)}`;
        }

        if (priorityFilter !== "All") {
          url += `&priority=${encodeURIComponent(priorityFilter)}`;
        }

        console.log("Fetching with URL:", url);

        // Make a direct API call without going through fetchData
        const response = await getAllTickets(url);

        if (response && response.data) {
          const { data, meta } = response;

          console.log("Response data length:", data.length);
          console.log("Response meta:", meta);

          // Update all state in one batch
          setTickets([...data]); // Force a new array reference
          setCurrentPage(meta.current_page);
          setRecordsPerPage(size); // Use the size parameter directly
          setPaginationLinks(meta.links);
          setTotal(meta.total);
          setRecordsToDisplay(meta.to);

          setStatusCounts(computeStatusCounts(data));
        }
      } catch (error) {
        console.error("Error changing records per page:", error);
        showErrorToast("Failed to update records per page");
      } finally {
        setLoading(false);
      }
    },
    [debouncedSearchTerm, statusFilter, priorityFilter]
  );

  // Helper to compute counts for open / in-progress / closed
  const computeStatusCounts = useCallback((ticketsList) => {
    const counts = {
      open: 0,
      inprogress: 0,
      closed: 0,
    };

    ticketsList.forEach((ticket) => {
      const status = ticket.status?.toLowerCase();
      if (status === "open") counts.open++;
      else if (status === "in progress" || status === "in_progress")
        counts.inprogress++;
      else if (status === "closed") counts.closed++;
    });

    return counts;
  }, []);

  return (
    <div className="support-page p-4">
      <div className="support-header d-flex justify-content-between align-items-center mb-4">
        <h1>Support Tickets</h1>
        <div className="d-flex gap-2">
          <Button
            variant="info"
            onClick={() => dispatch(setChatModalVisibility(true))}
            className="live-chat-btn"
            disabled={showChatModal}
          >
            <FaComments className="me-2" />
            Live Chat
            {unreadMessageCount > 0 && (
              <Badge bg="danger" pill className="ms-2">
                {unreadMessageCount > 9 ? "9+" : unreadMessageCount}
              </Badge>
            )}
          </Button>
          <Button
            variant="primary"
            onClick={openModal}
            className="create-ticket-btn"
          >
            Create New Ticket
          </Button>
        </div>
      </div>

      <CreateTicketModal
        show={isModalOpen}
        onClose={closeModal}
        onCreateTicket={handleCreateTicket}
      />

      {showTicketDetails && (
        <TicketDetailsModal
          ticket={selectedTicket}
          onClose={() => setShowTicketDetails(false)}
          onTicketUpdate={handleTicketUpdate}
        />
      )}

      <Row className="mb-3 align-items-center">
        <Col md={6} lg={3} className="mb-2 mb-md-0">
          <Form.Control
            type="text"
            placeholder="Search by Title, Description..."
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </Col>
        <Col md={3} lg={2} className="mb-2 mb-md-0">
          <Form.Select
            aria-label="Filter by status"
            value={statusFilter}
            onChange={handleStatusFilterChange}
          >
            <option value="All">All Statuses</option>
            <option value="Open">Open</option>
            <option value="In Progress">In Progress</option>
            <option value="Closed">Closed</option>
          </Form.Select>
        </Col>
        <Col md={3} lg={2} className="mb-2 mb-md-0">
          <Form.Select
            aria-label="Filter by priority"
            value={priorityFilter}
            onChange={handlePriorityFilterChange}
          >
            <option value="All">All Priorities</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </Form.Select>
        </Col>
        <Col md={12} lg={5}>
          <div className="d-flex justify-content-md-end">
            <div className="status-counts d-flex">
              <Badge bg="primary" className="me-2 p-2">
                Open: {statusCounts.open}
              </Badge>
              <Badge bg="warning" className="me-2 p-2">
                In Progress: {statusCounts.inprogress}
              </Badge>
              <Badge bg="success" className="me-2 p-2">
                Closed: {statusCounts.closed}
              </Badge>
            </div>
          </div>
        </Col>
      </Row>

      <div className="tickets-table-container">
        <Table
          responsive={"xl"}
          className="table text-center position-relative"
          {...getTableProps()}
        >
          {loading ? (
            <FetchingDataLoading />
          ) : (
            <>
              <thead>
                {headerGroups?.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers?.map((column, j) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={j}
                      >
                        {column.render("Header")}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {rows.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      style={{ cursor: "default" }}
                      key={row.original.id}
                    >
                      {row.cells.map((cell, j) => {
                        return (
                          <td {...cell.getCellProps()} key={j}>
                            {cell.render("Cell")}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
                {rows.length === 0 && !loading && (
                  <tr>
                    <td
                      colSpan={headerGroups[0].headers.length}
                      className="text-center"
                    >
                      No tickets found
                    </td>
                  </tr>
                )}
              </tbody>
            </>
          )}
        </Table>
      </div>
      {!isSearchActive && (
        <PaginationRecordsForReports
          onPageChange={handlePageChange}
          links={paginationLinks}
          handlePageSizeChange={handlePageSizeChange}
          per_page={recordsPerPage}
          to={recordsToDisplay}
          total={total}
          currentPage={currentPage}
        />
      )}
    </div>
  );
};

export default SupportPage;
