import { useState } from "react";
import * as Components from "../../components/Styles/SignIn_SignUp/SignIn_SignUp_Client.styles";
import { MdChevronLeft } from "react-icons/md";
import { ReactSVG } from "react-svg";
import dvconnect from "../../assets/media/Icons/dvconnect-gray.svg";
import { Link, useNavigate } from "react-router-dom";
import { Formik } from "formik";
import * as yup from "yup";
import Form from "react-bootstrap/Form";
import clientService from "../../services/auth/client";
import { showSuccessToast, showErrorToast } from "../../utils/toast-success-error";
import { useTranslation } from 'react-i18next';
import { FiAlertCircle } from "react-icons/fi";

const ForgotPasswordPage = () => {
    const { t, i18n } = useTranslation();
    const isRTL = i18n.language === 'ar';
    const [showOTP, setShowOTP] = useState(false);
    const [email, setEmail] = useState("");
    const [resending, setResending] = useState(false);
    const navigate = useNavigate();

    // RTL styles for form elements
    const rtlStyles = {
        formGroup: isRTL ? { textAlign: 'right' } : {},
        formLabel: isRTL ? { width: '100%', textAlign: 'right' } : {},
        errorMessage: isRTL ? { textAlign: 'right' } : {},
        resendLink: isRTL ? { textAlign: 'left' } : { textAlign: 'right' }
    };

    const emailValidationSchema = yup.object().shape({
        email: yup
            .string()
            .email(t("forms.validation.validEmail"))
            .required(t("forms.validation.emailRequired")),
    });

    const resetPasswordValidationSchema = yup.object().shape({
        otp: yup
            .string()
            .length(6, t("auth.validation.otpLength"))
            .required(t("auth.validation.otpRequired")),
        newPassword: yup
            .string()
            .min(8, t("auth.signInSignUp.validation.passwordMin"))
            .required(t("forms.newPassword")),
        confirmPassword: yup
            .string()
            .oneOf([yup.ref('newPassword')], t("auth.signInSignUp.validation.passwordsMustMatch"))
            .required(t("forms.confirmPassword")),
    });

    const handleEmailSubmit = async (values, { setSubmitting }) => {
        try {
            const response = await clientService.forgotPasswordApi(values.email);
            if (response.status === 200) {
                setEmail(values.email);
                setShowOTP(true);
                showSuccessToast(t("auth.messages.otpSent"));
            }
        } catch (error) {
            showErrorToast(t("auth.messages.emailError"));
        } finally {
            setSubmitting(false);
        }
    };

    const handleResetPassword = async (values, { setSubmitting }) => {
            const resetData = {
                email: email,
                otp: values.otp,
                password: values.newPassword
            };
            const res = await clientService.resetPasswordApi(resetData);
            if (res.status === 200) {
                showSuccessToast(t("auth.messages.passwordResetSuccess"));
                navigate("/client/login");
            }
            setSubmitting(false);
    };

    const handleResendOTP = async () => {
        if (resending) return;
            setResending(true);
            const response = await clientService.forgotPasswordApi(email);
            if (response.status === 200) {
                showSuccessToast(t("auth.messages.otpResent") || "OTP has been resent to your email");
            }
            setResending(false);
    };

    const toggleLanguage = () => {
        const newLang = i18n.language === 'en' ? 'ar' : 'en';
        i18n.changeLanguage(newLang);

        // Optional: Update document direction for RTL languages
        document.documentElement.dir = newLang === 'ar' ? 'rtl' : 'ltr';
    };

    return (
        <Components.ForgotPasswordContainer className={"position-relative"}>
            <div onClick={toggleLanguage} className={"fs-5 text-bold position-absolute top-0 end-0 p-3"} role="button">
                <div className={"ms-3 menu-item_label"}>
                    {i18n.language === 'en' ? 'عربي' : 'English'}
                </div>
            </div>
            <Components.ForgotPasswordForm>
                {!showOTP ? (
                    <Formik
                        initialValues={{ email: "" }}
                        validationSchema={emailValidationSchema}
                        onSubmit={handleEmailSubmit}
                    >
                        {({ handleSubmit, handleChange, handleBlur, values, touched, errors, isSubmitting }) => (
                            <Form onSubmit={handleSubmit} className="shadow-lg p-5 rounded-5">
                                <Components.Title className={"mb-5"}>{t("auth.forgotPassword")}</Components.Title>
                                <Form.Group className="mb-3" style={rtlStyles.formGroup}>
                                    <Form.Label style={rtlStyles.formLabel}>{t("forms.emailAddress")}</Form.Label>
                                    <Components.Input
                                        type="email"
                                        name="email"
                                        placeholder={t("forms.placeholder.email")}
                                        value={values.email}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        isInvalid={touched.email && errors.email}
                                    />
                                    {touched.email && errors.email && (
                                        <div className="text-danger mb-2" style={rtlStyles.errorMessage}>{errors.email}</div>
                                    )}
                                </Form.Group>
                                <p className={"gray-label mb-4"} style={rtlStyles.formGroup}>
                                    {t("auth.messages.resetPasswordInstructions")}
                                </p>
                                <div className={"d-flex flex-column align-items-center"}>
                                    <Components.Button
                                        type="submit"
                                        className={"submit-btn rounded-4"}
                                        disabled={isSubmitting}
                                    >
                                        {isSubmitting ? t("buttons.submitting") : t("common.submit")}
                                    </Components.Button>
                                    <Link to={"/client/login"} role={"button"} className={"mainColor my-4"}>
                                        <MdChevronLeft size={20} style={{ transform: isRTL ? 'rotate(180deg)' : 'none' }} /> {t("auth.backToLogin")}
                                    </Link>
                                    <ReactSVG src={dvconnect} className={"dvconnect-gray"} />
                                </div>
                            </Form>
                        )}
                    </Formik>
                ) : (
                    <Formik
                        initialValues={{
                            otp: "",
                            newPassword: "",
                            confirmPassword: ""
                        }}
                        validationSchema={resetPasswordValidationSchema}
                        onSubmit={handleResetPassword}
                    >
                        {({ handleSubmit, handleChange, handleBlur, values, touched, errors, isSubmitting }) => (
                            <Form onSubmit={handleSubmit} className="shadow-lg p-5 rounded-5">
                                <Components.Title className="mb-4">{t("auth.resetPassword")}</Components.Title>
                                <small className="gray-label mb-3 d-block" style={rtlStyles.formGroup}>
                                    {t("auth.messages.enterVerificationCode", { email })}
                                </small>
                                <Form.Group className="mb-3" style={rtlStyles.formGroup}>
                                    <Form.Label style={rtlStyles.formLabel}>{t("auth.signIn.verificationCode")}</Form.Label>
                                    <Components.Input
                                        type="text"
                                        name="otp"
                                        placeholder={t("auth.enterOTP")}
                                        value={values.otp}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        isInvalid={touched.otp && !!errors.otp}
                                    />
                                    {touched.otp && errors.otp && (
                                        <div className="text-danger" style={rtlStyles.errorMessage}>{errors.otp}</div>
                                    )}
                                    <div className="mt-2" style={rtlStyles.resendLink}>
                                        <Link
                                            to="#"
                                            role="button"
                                            className={`mainColor ${resending ? 'disabled' : ''}`}
                                            onClick={(e) => {
                                                e.preventDefault();
                                                handleResendOTP();
                                            }}
                                            style={{ pointerEvents: resending ? 'none' : 'auto' }}
                                        >
                                            {resending ? t("buttons.sending") || "Sending..." : t("auth.resendOTP") || "Resend OTP"}
                                        </Link>
                                    </div>
                                </Form.Group>
                                <Form.Group className="mb-3" style={rtlStyles.formGroup}>
                                    <Form.Label style={rtlStyles.formLabel}>{t("forms.newPassword")}</Form.Label>
                                    <div className="d-flex align-items-center position-relative">
                                        <Components.Input
                                            type="password"
                                            name="newPassword"
                                            placeholder={t("forms.placeholder.newPassword")}
                                            value={values.newPassword}
                                            onChange={handleChange}
                                            onBlur={handleBlur}
                                            isInvalid={touched.newPassword && !!errors.newPassword}
                                        />
                                        <span
                                            className={isRTL ? "me-2 text-warning" : "ms-2 text-warning"}
                                            title="Password must be at least 8 characters"
                                            style={{
                                                cursor: 'pointer',
                                                position: 'absolute',
                                                right: isRTL ? 'auto' : '2%',
                                                left: isRTL ? '2%' : 'auto'
                                            }}
                                        >
                                            <FiAlertCircle size={20} />
                                        </span>
                                    </div>
                                    {touched.newPassword && errors.newPassword && (
                                        <div className="text-danger" style={rtlStyles.errorMessage}>{errors.newPassword}</div>
                                    )}
                                </Form.Group>
                                <Form.Group className="mb-4" style={rtlStyles.formGroup}>
                                    <Form.Label style={rtlStyles.formLabel}>{t("forms.confirmPassword")}</Form.Label>
                                    <Components.Input
                                        type="password"
                                        name="confirmPassword"
                                        placeholder={t("forms.placeholder.confirmPassword")}
                                        value={values.confirmPassword}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        isInvalid={touched.confirmPassword && !!errors.confirmPassword}
                                    />
                                    {touched.confirmPassword && errors.confirmPassword && (
                                        <div className="text-danger" style={rtlStyles.errorMessage}>{errors.confirmPassword}</div>
                                    )}
                                </Form.Group>
                                <div className="d-flex flex-column align-items-center">
                                    <Components.Button
                                        type="submit"
                                        className="submit-btn rounded-4"
                                        disabled={isSubmitting}
                                    >
                                        {isSubmitting ? t("buttons.resetting") : t("auth.resetPassword")}
                                    </Components.Button>
                                    <Link
                                        to="#"
                                        role="button"
                                        className="mainColor my-4"
                                        onClick={() => setShowOTP(false)}
                                    >
                                        <MdChevronLeft size={20} style={{ transform: isRTL ? 'rotate(180deg)' : 'none' }} />
                                        {t("auth.backToEmail")}
                                    </Link>
                                    <ReactSVG src={dvconnect} className="dvconnect-gray" />
                                </div>
                            </Form>
                        )}
                    </Formik>
                )}
            </Components.ForgotPasswordForm>
        </Components.ForgotPasswordContainer>
    );
};

export default ForgotPasswordPage;
