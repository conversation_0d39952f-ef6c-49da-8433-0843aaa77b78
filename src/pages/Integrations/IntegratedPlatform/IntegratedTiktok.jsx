import { Form, InputGroup } from "react-bootstrap";
import { BiSearch } from "react-icons/bi";
import { useEffect } from "react";
import { FaT<PERSON><PERSON>, FaUser } from "react-icons/fa";
import { PiPlus } from "react-icons/pi";
import Cookies from "js-cookie";
import TikTokService from "../../../services/integrations/tiktok";
import IntegrationPagesTikTok from "../../../components/IntegratedPlatformPages/IntegrationPagesTikTok";
import { FaEye } from "react-icons/fa6";
import "../../Integrations/Integrations.css";
import useIntegration from "../../../hooks/useIntegration";

const IntegratedTiktok = () => {
  const {
    integrationData,
    setFilteredData,
    searchTerm,
    setSearchTerm,
    handleSignInWithPlatform,
    setAdvertisers,
    advertisers,
    selectedAdvertiser,
    setSelectedAdvertiser,
    handleFetchTikTokPages,
  } = useIntegration();

  useEffect(() => {
    setFilteredData(
      integrationData.filter((integration) =>
        integration.platform.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [searchTerm]);

  useEffect(() => {
    const fetchAccess = async () => {
      try {
        const response = await TikTokService.checkAccessTokenTikTok();
        if (response?.data.length !== 0) {
          Cookies.set("access_token_tiktok", response.data);
          await fetchAdvertisers();
        } else {
          Cookies.remove("access_token_tiktok");
          console.log("No accounts found for TikTok");
        }
      } catch (e) {
        console.error(e);
      }
    };

    const fetchAdvertisers = async () => {
      try {
        const advertisers = await TikTokService.getAdvertisers();
        setAdvertisers(advertisers?.data?.data?.list);
      } catch (e) {
        console.error(e);
      }
    };

    fetchAccess();
  }, []);

  return (
    <>
      <h2>Tiktok Integration Options</h2>

      <div className="d-flex flex-column flex-lg-row justify-content-center align-items-center my-4">
        <div className="integration-search-box my-3 ms-0 m-lg-0">
          <div className="integration-search-box_input">
            <InputGroup>
              <Form.Control
                type="search"
                placeholder="Search Integrations"
                aria-label="Large"
                aria-describedby="inputGroup-sizing-lg"
                size="lg"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
            <BiSearch
              className="integration-search-icon"
              size={30}
              color="white"
            />
          </div>
        </div>
        <div
          id="add-account"
          className="white-button mainColor me-2 shadow-sm px-2 mb-2 ms-3"
          role="button"
          onClick={() => handleSignInWithPlatform("Tiktok")}
        >
          Add Account <PiPlus className="mainColor" size={25} />
        </div>
      </div>

      <div className="my-2 fw-bold fs-5">Tiktok Advertiser</div>
      <div className={"d-flex justify-content-center align-items-center gap-5"}>
        {advertisers?.map((advertiser) => (
          <div
            key={advertiser?.advertiser_id}
            role="button"
            onClick={() => {
              setSelectedAdvertiser(advertiser);
              handleFetchTikTokPages(advertiser?.advertiser_id);
            }}
            className={`advertiser-card ${
              selectedAdvertiser?.advertiser_id === advertiser?.advertiser_id
                ? "selected-advertiser"
                : ""
            }`}
          >
            <div className="connected-pages">
              <div className="d-flex justify-content-between align-items-center flex-column advertiser-info-1">
                <div
                  className="w-100"
                  style={{ borderBottom: "1px solid rgb(182, 182, 182)" }}
                >
                  <div
                    className="px-3 py-3 mx-auto"
                    style={{ width: "fit-content" }}
                  >
                    <div className="position-relative">
                      <FaUser
                        size={60}
                        color="#fff"
                        className="rounded-circle bg-secondary-subtle p-1"
                      />
                      <FaTiktok
                        size={20}
                        color="#000000"
                        className="user-integration-icon"
                      />
                    </div>
                  </div>
                </div>
                <div className="d-flex justify-content-start flex-column text-center px-5 py-2 advertiser-info-2">
                  <div className="fw-bold fs-5 mt-1">
                    {advertiser?.advertiser_name}
                  </div>
                  <div className="mainColor">Connected</div>
                </div>
                <div className="overlay-tiktok">
                  <FaEye className="eye-icon" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <IntegrationPagesTikTok />
    </>
  );
};

export default IntegratedTiktok;
