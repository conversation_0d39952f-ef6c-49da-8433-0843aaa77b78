import { Collapse, Form, InputGroup } from "react-bootstrap";
import { BiSearch } from "react-icons/bi";
import { useEffect, useState } from "react";
import { FaFacebook } from "react-icons/fa6";
import { PiPlus } from "react-icons/pi";
import { IoIosArrowDown } from "react-icons/io";
import IntegratedPlatformPages from "../../../components/IntegratedPlatformPages/IntegratedPlatformPages.component";
import IntegrationForms from "./IntegrationForms";
import { toast } from "react-toastify";
import { Tooltip } from "react-tooltip";
import { useLocation, useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import leadService from "../../../services/leads";
import metaService from "../../../services/integrations/meta";
import CenteredModal from "../../../components/Shared/modals/CenteredModal/CenteredModal";
import CancelIntegrationModal from "../../../components/Modals/CancelIntegrationModal";
import "../../Integrations/Integrations.css";
import { useTranslation } from "react-i18next";
import useIntegration from "../../../hooks/useIntegration";

const IntegratedPlatformPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const token = Cookies.get("access_token");
  if (!token) {
    navigate("/integrations");
  }
  const {
    integrationData,
    setFilteredData,
    searchTerm,
    setSearchTerm,
    accounts,
    setAccounts,
    handleSignInWithPlatform,
    handleDisconnectFromFB,
    setShowIntegrationModal,
    showCancelIntegrationModal,
    setShowCancelIntegrationModal,
  } = useIntegration();
  const [forms, setForms] = useState();
  const [loading, setLoading] = useState(false);
  const [pageAccessTokens, setPageAccessTokens] = useState("");
  const [open, setOpen] = useState(false);
  const getLeads = async (data) => {
    try {
      const response = await metaService.getFormsLeadsApi(data);
      toast.success(response?.message || "Data Inserted Successfully", {
        position: "bottom-right",
        theme: "dark",
      });
    } catch (e) {
      toast.error(e?.response?.data?.message, {
        position: "bottom-right",
        theme: "dark",
      });
    }
  };

  useEffect(() => {
    setFilteredData(
      integrationData.filter((integration) =>
        integration.platform.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [searchTerm]);

  useEffect(() => {
    const fetchAccess = async () => {
      const response = await metaService.getAccessTokenApi();
      if (response?.data.length !== 0) {
        Cookies.set("access_token", response.data);
        await fetchAccounts();
      } else {
        Cookies.remove("access_token");
        navigate("/integrations");
      }
    };
    fetchAccess();

    const fetchAccounts = async () => {
      try {
        const accounts = await metaService.getAccountsApi(token);
        setAccounts(accounts);
      } catch (e) {
        console.log(e);
      }
    };
  }, []);
  const accountsPages = accounts?.accounts
    ? accounts?.accounts?.data
    : accounts?.pages;
  useEffect(() => {
    if (forms?.length > 0) {
      document
        .getElementById("formsSection")
        .scrollIntoView({ behavior: "smooth" });
    }
  }, [forms]);

  const location = useLocation();
  const historyState = location.state;

  useEffect(() => {
    if (historyState && historyState.source === "integrationContext") {
      setShowIntegrationModal(true);
    }
  }, [historyState]);

  return (
    <>
      <h2>{t("integrations.facebook.title")}</h2>

      <div
        className={
          "d-flex flex-column flex-lg-row justify-content-center align-items-center my-4"
        }
      >
        <div className="integration-search-box my-3 ms-0 m-lg-0">
          <div className="integration-search-box_input">
            <InputGroup>
              <Form.Control
                type="search"
                placeholder={t("integrations.facebook.searchPlaceholder")}
                aria-label="Large"
                aria-describedby="inputGroup-sizing-lg"
                size="lg"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
            <BiSearch
              className="integration-search-icon"
              size={30}
              color="white"
            />
          </div>
        </div>
        <div
          id={"add-account"}
          className={"white-button mainColor me-2 shadow-sm px-2 mb-2 ms-3"}
          role={"button"}
          onClick={() => handleSignInWithPlatform("Facebook")}
        >
          {t("integrations.facebook.addAccount")}{" "}
          <PiPlus className={"mainColor"} size={25} />
        </div>
      </div>
      <div className={"my-2 fw-bold fs-5"}>
        {t("integrations.facebook.account")}
      </div>
      <div
        className={
          "d-flex justify-content-between align-items-center align-content-center"
        }
      >
        <div
          className={"connected-pages flex-grow-1"}
          style={{ border: "2px solid rgb(146, 192, 32)" }}
        >
          <div aria-controls="connected-pages-collapse" aria-expanded={open}>
            <div
              className={"d-flex justify-content-between align-items-center"}
              style={{ borderBottom: "1px solid rgb(182, 182, 182)" }}
            >
              <div
                className={"px-3 py-3"}
                style={{ borderRight: "1px solid rgb(182, 182, 182)" }}
              >
                <div className={"position-relative"}>
                  <img
                    src={
                      accounts?.picture?.data?.url
                        ? accounts?.picture?.data?.url
                        : accounts?.picture
                    }
                    alt={"User"}
                    className={"integration-account-pic"}
                  />
                  <FaFacebook
                    size={20}
                    color="#1877f2"
                    className={"user-integration-icon"}
                  />
                </div>
              </div>
              <div
                className={"d-flex justify-content-start flex-column ps-3"}
                onClick={() => setOpen(!open)}
              >
                <div className={"fw-bold fs-5 mt-1"}>{accounts?.name}</div>
                <div className={"mainColor"}>
                  {t("integrations.facebook.connected")}
                </div>
              </div>
              <div
                className={"submit-btn ms-auto me-3"}
                onClick={() => setOpen(!open)}
              >
                {t("integrations.facebook.relatedPages")} (
                {accountsPages?.length || 0})
                <IoIosArrowDown className={"ms-2"} size={20} />
              </div>
              <Tooltip
                anchorSelect="#delete-account"
                content={t("integrations.facebook.disconnect")}
                className={"bg-dark text-white"}
              />
              <div
                id={"delete-account"}
                className={
                  "white-button me-2 bg-white shadow-sm px-2 text-danger"
                }
                role={"button"}
                onClick={() => setShowCancelIntegrationModal(true)}
              >
                {t("integrations.facebook.disconnect")}
              </div>
              <CenteredModal
                show={showCancelIntegrationModal}
                children={
                  <CancelIntegrationModal
                    cancelIntegrationFun={() =>
                      handleDisconnectFromFB(accounts?.id)
                    }
                    setShowCancelIntegrationModal={
                      setShowCancelIntegrationModal
                    }
                    onHide={() => setShowCancelIntegrationModal(false)}
                  />
                }
                onHide={() => setShowCancelIntegrationModal(false)}
              />
            </div>
          </div>
          <Collapse in={open}>
            <div id={"connected-pages-collapse"} className={"p-4"}>
              <IntegratedPlatformPages
                accountsPages={accountsPages}
                setForms={setForms}
                getLeads={getLeads}
                setLoading={setLoading}
                setPageAccessTokens={setPageAccessTokens}
              />
            </div>
          </Collapse>
        </div>
      </div>

      <IntegrationForms
        forms={forms}
        pageAccessTokens={pageAccessTokens}
        loading={loading}
        getLeads={getLeads}
      />
    </>
  );
};

export default IntegratedPlatformPage;
