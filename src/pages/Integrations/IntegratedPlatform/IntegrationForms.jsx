import { Col, Row } from "react-bootstrap";
import FetchingDataLoading from "../../../components/LoadingAnimation/FetchingDataLoading";
import { FaCircleXmark } from "react-icons/fa6";
import { HiArrowDownTray } from "react-icons/hi2";
import { SiGoogleforms } from "react-icons/si";
import { useTranslation } from "react-i18next";

const IntegrationForms = ({ forms, loading, pageAccessTokens, getLeads }) => {
  const { t } = useTranslation();

  return (
    <Row
      className={"mt-4 justify-content-evenly connected-pages column-gap-1 p-2"}
      id={"formsSection"}
    >
      <h5 className={"text-center fw-bold my-3"}>{t('integrations.facebook.forms.title')}</h5>

      <div className={"green-label"}>
        {forms?.length > 0
          ? t('integrations.facebook.forms.formsFound', { count: forms.length })
          : t('integrations.facebook.forms.selectPage')}
      </div>

      {loading ? (
        <FetchingDataLoading />
      ) : (
        forms?.map((form, index) => (
          <Col
            title={t('integrations.facebook.forms.getLeadsForm')}
            role={"button"}
            xl={4}
            md={4}
            sm={6}
            xs={12}
            className={
              "d-flex justify-content-between flex-column connected-page-content my-2 px-0 mb-4"
            }
            style={{
              border:
                form?.status === "ACTIVE"
                  ? "3px solid rgb(146, 192, 32)"
                  : "3px solid rgb(255, 0, 0)",
            }}
            key={form?.id}
            onClick={() =>
              form?.status === "ACTIVE" &&
              getLeads({
                form_id: form?.id,
                page_access_token: pageAccessTokens,
              })
            }
          >
            <div
              className={`${form?.status === "ACTIVE" ? "mainColor" : "text-danger"} pt-2`}
            >
              <SiGoogleforms
                size={25}
                color={`${form?.status === "ACTIVE" ? "rgb(165, 211, 52)" : "rgb(255, 0, 0)"} `}
              />{" "}
              {t('integrations.facebook.forms.form', { number: index + 1 })}
            </div>
            <div className={"fs-5 fw-bold px-2"}>{form?.name}</div>
            <div
              className={"form-icon-import py-2"}
              style={{
                backgroundColor: `${form?.status === "ACTIVE" ? "rgb(165, 211, 52)" : "rgb(255, 0, 0)"}`,
              }}
            >
              {form?.status === "ACTIVE" ? (
                <HiArrowDownTray
                  color={"#FFF"}
                  role={"button"}
                  size={40}
                  title={t('integrations.facebook.forms.getLeadsForm')}
                />
              ) : (
                <FaCircleXmark
                  color={"#FFFFFF"}
                  size={35}
                  title={t('integrations.facebook.forms.inactive')}
                />
              )}
            </div>
          </Col>
        ))
      )}
    </Row>
  );
};

export default IntegrationForms;
