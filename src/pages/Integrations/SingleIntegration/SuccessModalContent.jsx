import Lottie from "lottie-react";
import Congratulations from "../../../assets/media/animations/congratulations.json";
import {Button} from "react-bootstrap";
import {useNavigate} from "react-router-dom";

const SuccessModalContent = ({currentIntegration, setShowCenteredModal}) => {
    const navigate = useNavigate();
    const navigateBack = () => {
        navigate("./connected"); // Update the URL to /connected
    };
    return (
        <div className={"d-flex flex-column justify-content-around align-items-center"}>
            <div className={"position-relative"}>
                {currentIntegration?.successIcon}
                <Lottie animationData={Congratulations} className={"congratulations-animation"} loop={false} />
            </div>
            <div className={"centered-modal-title mt-5 mb-3"}>
                Congratulations !
            </div>
            <div className={"centered-modal-description"}>
                Connected to {currentIntegration?.platform} Successfully 😉
            </div>
            <center>
                <Button className={"submit-btn mt-3"} onClick={()=> {
                    setShowCenteredModal(false)
                    navigateBack();
                }}>
                    Continue
                </Button>
            </center>
        </div>
    );
};

export default SuccessModalContent;