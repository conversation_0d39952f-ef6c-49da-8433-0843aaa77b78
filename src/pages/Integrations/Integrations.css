.integration-search-box input {
    border-radius: 34px;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
}

.integration-search-box {
    width: 40%;
    margin: 0 auto;
}

.integration-search-box_input {
    position: relative;
}

.integration-search-box .form-control {
    padding: 0 0 0 20px;
}

[dir="rtl"] .integration-search-box .form-control {
    padding: 0 25px 0 0;
}

.integration-search-icon {
    border-radius: 0 34px 34px 0;
    background: #000;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    position: absolute;
    right: -5%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 99;
    height: 100%;
    width: 2rem;
    padding: 5px;
}

.integration-card {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    text-align: left;
    padding: 0.75rem 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 3rem;
}

.integration-card:hover {
    box-shadow: 0 4px 64px -7.516px rgba(0, 0, 0, 0.10);
    scale: 1.1;
    transition: ease-in-out all 0.25s;
}

.integration-card .form-check-input {
    min-height: 25px;
    min-width: 50px;
}

.integration-card .form-check-input:checked {
    background-color: #92C020;
    border-color: #92C020;
}

.integration-card .form-switch .form-check-input {
    margin-top: 0;
}

.integration-card-icon {
    border-radius: 5px;
    border: 1px solid #FFF;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.25);
}

.integration-animation {
    width: 150px;
    height: 150px;
    transform: rotate(45deg);
}

.integration-description {
    opacity: 0.6;
    color: #000;
    font-size: 1rem;
    font-weight: 700;
    text-align: center;
    width: 60%;
    margin: 0 auto;
}

.integration-submit {
    width: fit-content;
    margin: 0 auto;
}

.white-button {
    border-radius: 53px;
    background: #FFF;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.10);
    padding: 9px 31px;
}

/*.integration-account-card {*/
/*    border-radius: 14px;*/
/*    border: 2px solid #FFF;*/
/*    background: #FFF;*/
/*    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);*/
/*    width: fit-content;*/
/*    min-width: 320px;*/
/*    min-height: 210px;*/
/*    margin: 0 auto;*/
/*}*/

.integration-account-pic {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.user-integration-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    border-radius: 50%;
    background-color: white;
}

.user-integration-page {
    position: absolute;
    bottom: 0;
    right: 30%;
    transform: translate(-50%, 0);
    background-color: #1877f2;
}

.connected-label {
    border-radius: 44px;
    background: #525252;
    font-weight: 700;
    color: #FFFFFF;
    padding: 8px 20px;
}

.single-connected-card {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    width: fit-content;
    display: flex;
    flex-direction: column;
    text-align: center;
}

.connected-page-content {
    text-align: center;
    max-width: 250px;
    box-sizing: border-box;
    border: 2px solid rgb(255, 255, 255);
    border-radius: 17px;
    box-shadow: 0 5px 72px -8.35px rgba(0, 0, 0, 0.1);
    background: rgb(255, 255, 255);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 150px;
}

.connected-count {
    border-radius: 14px;
    background: #FFF;
    padding: 15px 17px;
    font-weight: 700;
    font-size: 0.8rem;
    margin-top: 1rem;
    cursor: pointer;
}

.connected-count:hover {
    box-shadow: 0 -5px 11px 0 rgba(0, 0, 0, 0.10);
    background-color: #92C020;
    color: white;
    transition: all ease-in-out 0.25s;
}

.import-title {
    color: #00C1A2;
}

.import-animation {
    width: 125px;
    height: 125px;
}

.import-description {
    color: #7B7B7B;
    line-height: 1.5;
}

.horizontal-divider {
    border-top: 1px solid #BFBFBF;
    width: 50%;
}

.import-selection {
    background-color: white;
    color: #00C1A2;
    border: 1px solid #00C1A2;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    width: 82px;
    height: 82px;
}

.import-selection.selected {
    background-color: #00C1A2;
    color: white;
    transition: all ease-in-out 0.25s;
}

.modal-close-icon {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(-50%, -50%);
    cursor: pointer;
}

.connected-pages {
    box-sizing: border-box;
    border-radius: 14px;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.1);
    background: rgb(255, 255, 255);
}

.selected-advertiser .connected-pages {
    border: 2px solid rgb(146, 192, 32);
}

.form-icon-integration-r, .form-icon-integration-l {
    width: 50%;
    box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.1);
    background: #FFFFFF;
    color: rgb(146, 192, 32);
}

.form-icon-integration-r {
    border-radius: 0 0 17px 0;
}

.form-icon-integration-l {
    border-radius: 0 0 0 17px;
}

.form-icon-integration-l.active-page, .form-icon-integration-r.active-page {
    background-color: rgb(146, 192, 32);
    color: white;
    transition: all 0.3s ease-in-out;
}


.form-icon-import {
    border-radius: 0 0 14px 14px;
    box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.1);
}

.selected-advertiser .advertiser-info-2 {
    background-color: rgb(146, 192, 32);
}

.advertiser-info-1 {
    border-top-right-radius: 14px;
    border-top-left-radius: 14px;
}

.advertiser-info-2 {
    border-bottom-right-radius: 14px;
    border-bottom-left-radius: 14px;
}

.selected-advertiser .advertiser-info-2, .selected-advertiser .advertiser-info-2 .mainColor {
    /*border-color: white;*/
    color: white;
}

.advertiser-card {
    position: relative;
    transition: all 0.3s ease-in-out;
}

.advertiser-card:hover .overlay {
    opacity: 1;
    visibility: visible;
    backdrop-filter: blur(5px);
}
.overlay-tiktok {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    border-radius: 14px;
}

.eye-icon {
    color: #fff;
    font-size: 2rem;
}
