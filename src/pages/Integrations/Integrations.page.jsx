import "./Integrations.css";
import {Row} from "react-bootstrap";
import AllSourcesPage from "../AllSources/AllSources.page";
const IntegrationsPage = () => {
    // const integrationsTabs = [
    //     {label: "Load Sources", eventKey: "all_sources"},
    //     // {label: "Import/Export Clients", eventKey: "import_export"}
    // ];
    return (
        <>
        {/*<Tab.Container id="integrations-tabs" defaultActiveKey="all_sources">*/}
        {/*    <Nav variant="pills" className={"justify-content-center mx-auto leads-tabs-navs"}>*/}
        {/*        {integrationsTabs.map((tab, index) => (*/}
        {/*            <Nav.Item key={index}>*/}
        {/*                <Nav.Link eventKey={tab.eventKey}>{tab.label}</Nav.Link>*/}
        {/*            </Nav.Item>*/}
        {/*        ))}*/}
        {/*    </Nav>*/}
            <div className={"mt-5"}>
                {/*<Tab.Content>*/}
                {/*    <Tab.Pane eventKey="all_sources">*/}
                        <AllSourcesPage />
                {/*    </Tab.Pane>*/}
                {/*    <Tab.Pane eventKey="import_export">*/}
                {/*        Import Export*/}
                {/*    </Tab.Pane>*/}
                {/*</Tab.Content>*/}
            </div>

        {/*</Tab.Container>*/}
        </>
    );
};

export default IntegrationsPage;
