import { useEffect, useState } from "react";
import "./reports.css";
import { Nav, Row, Tab } from "react-bootstrap";
import "react-datepicker/dist/react-datepicker.css";
import ChartsForLeadAssignment from "../../components/Reports/ChartsForLeadAssignment";
import LeadAssignmentTable from "../../components/Reports/LeadAssignmentTable";
import TeamMembersTable from "../../components/Reports/TeamMembersTable";
import ChartsForTeamMembers from "../../components/Reports/ChartsForTeamMembers";
import SalesTable from "../../components/Reports/SalesTable";
import DepartmentReportsTable from "../../components/Reports/DepartmentReportsTable";
import getAllTeamMembers from "../../services/teams/get-teams.api";
import { toast } from "react-toastify";
import { useSelector, useDispatch } from "react-redux";
import {
  setTeamMembers,
  setActiveTab,
  setDisableAddMember,
} from "../../redux/features/clientSlice"; // Keep this
import TeamMemberStatisticsTable from "../../components/Reports/TeamMemberStatisticsTable";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { setTeamMembersFilters } from "../../redux/features/reportsSlice";
import {
  shouldShowPackageFeatures,
  isUserExcluded,
} from "../../config/packageVisibility";

const ReportsPage = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const activeTab = useSelector((state) => state.client.activeTab);
  const { user } = useSelector((state) => state.auth);

  const [showStatistics, setShowStatistics] = useState(false);
  const savedTMFilters = useSelector(
    (state) => state.reports.teamMembersFilters
  );
  const savedSalesFilters = useSelector((state) => state.reports.salesFilters);
  const savedLeadFilters = useSelector(
    (state) => state.reports.leadAssignmentFilters
  );
  const savedDepartmentFilters = useSelector(
    (state) => state.reports.departmentFilters
  );

  const [startDate, setStartDate] = useState(
    savedTMFilters.startDate ? new Date(savedTMFilters.startDate) : null
  );
  const [endDate, setEndDate] = useState(
    savedTMFilters.endDate ? new Date(savedTMFilters.endDate) : null
  );
  const [selectedTM, setSelectedTM] = useState(savedTMFilters.selectedTM);
  const [leadsSelectedTM, setLeadsSelectedTM] = useState(
    savedLeadFilters.selectedTM ? { id: savedLeadFilters.selectedTM } : null
  );
  const [selectedSalesTM, setSelectedSalesTM] = useState(
    savedSalesFilters.selectedTM
  );
  const [salesStatus, setSalesStatus] = useState(
    savedSalesFilters.status !== undefined ? savedSalesFilters.status : null
  );
  const [salesService, setSalesService] = useState(savedSalesFilters.service);
  const [role, setRole] = useState(savedTMFilters.role);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const teamsData = await getAllTeamMembers();
        dispatch(setTeamMembers(teamsData?.data?.members));
        dispatch(setDisableAddMember(teamsData?.data?.quota === 0));
      } catch (error) {
        console.error("Error fetching initial data:", error);
        toast.error(t("reports.errors.fetchTeams"), {
          position: "bottom-right",
          theme: "dark",
        });
      }
    };
    fetchData();
  }, [dispatch, t]);
  const allTabs = [
    {
      label: t("reports.tabs.teamMembers"),
      eventKey: "members",
      disabled:
        user?.user?.package_id === 1 ||
        (user?.user?.parent_id !== null && user?.user?.role !== 1),
      content: (
        <>
          <ChartsForTeamMembers activeTab={activeTab} />
          <TeamMembersTable
            role={role}
            setRole={setRole}
            selectedTM={selectedTM}
            setSelectedTM={setSelectedTM}
            startDate={startDate}
            endDate={endDate}
            setEndDate={setEndDate}
            setStartDate={setStartDate}
            setShowStatistics={setShowStatistics}
            activeTab={activeTab}
          />
        </>
      ),
      class: "members-tab",
    },
    {
      label: t("reports.tabs.leadAssignment"),
      eventKey: "assignment",
      content: (
        <>
          <ChartsForLeadAssignment activeTab={activeTab} />
          <LeadAssignmentTable
            activeTab={activeTab}
            leadsSelectedTM={leadsSelectedTM}
            setLeadsSelectedTM={setLeadsSelectedTM}
          />
        </>
      ),
      class: "assignment-tab",
    },
    {
      label: t("reports.tabs.salesPerformance"),
      disabled:
        user?.user?.package_id === 1 ||
        (user?.user?.parent_id !== null && user?.user?.role !== 1),
      eventKey: "sales",
      content: (
        <SalesTable
          activeTab={activeTab}
          selectedSalesTM={selectedSalesTM}
          setSelectedSalesTM={setSelectedSalesTM}
          salesStatus={salesStatus}
          setSalesStatus={setSalesStatus}
          salesService={salesService}
          setSalesService={setSalesService}
        />
      ),
      class: "sales-tab",
    },
    {
      label: t("reports.tabs.departmentSources", "Department by Sources"),
      disabled:
        user?.user?.package_id === 1 ||
        (user?.user?.parent_id !== null && user?.user?.role !== 1),
      eventKey: "departmentSources",
      content: (
        <DepartmentReportsTable activeTab={activeTab} reportType="sources" />
      ),
      class: "department-sources-tab",
      specialUserOnly: true, // Mark this tab as special user only
    },
    {
      label: t("reports.tabs.departmentStatuses", "Department by Statuses"),
      disabled:
        user?.user?.package_id === 1 ||
        (user?.user?.parent_id !== null && user?.user?.role !== 1),
      eventKey: "departmentStatuses",
      content: (
        <DepartmentReportsTable activeTab={activeTab} reportType="statuses" />
      ),
      class: "department-statuses-tab",
      specialUserOnly: true, // Mark this tab as special user only
    },
  ];

  // Filter tabs based on user permissions
  const teamTabs = allTabs.filter((tab) => {
    // If tab is marked as special user only, only show it to excluded users (IDs 423 and 1)
    if (tab.specialUserOnly) {
      return isUserExcluded(user?.user?.id);
    }
    // Show all other tabs to everyone
    return true;
  });
  return (
    <div className={"px-3 px-sm-0"}>
      {showStatistics ? (
        <TeamMemberStatisticsTable
          selectedTM={selectedTM}
          endDate={endDate}
          setEndDate={setEndDate}
          setStartDate={setStartDate}
          startDate={startDate}
          setShowStatistics={setShowStatistics}
        />
      ) : (
        <>
          <Tab.Container
            id="integrations-tabs"
            defaultActiveKey={activeTab}
            onSelect={(eventKey) => dispatch(setActiveTab(eventKey))}
          >
            <h2 className={"page-title"}>{t("reports.title")}</h2>
            {user?.user?.package_id === 1 &&
              shouldShowPackageFeatures(user?.user?.id) && (
                <div className="quota-container">
                  <div className="radial-gradient-border">
                    {t("reports.quotaExceeded")}
                  </div>
                  <Link to={"/packages"}>
                    <button className="submit-btn">
                      {t("common.upgradeNow")}
                    </button>{" "}
                  </Link>
                </div>
              )}
            <Nav
              variant="pills"
              className={"justify-content-center mx-auto leads-tabs-navs mb-4"}
            >
              {teamTabs.map((tab, index) => (
                <Nav.Item
                  key={index}
                  className={`${tab.class} ${
                    activeTab === "members" && index === 1
                      ? "border-right"
                      : activeTab === "assignment" && index === 0
                      ? ""
                      : activeTab === "sales" && index === 1
                      ? "border-left"
                      : ""
                  }`}
                >
                  <Nav.Link
                    className={tab?.disabled && "text-muted"}
                    disabled={tab?.disabled}
                    eventKey={tab.eventKey}
                  >
                    {tab.label}
                  </Nav.Link>
                </Nav.Item>
              ))}
            </Nav>
            <Row>
              <Tab.Content>
                {teamTabs.map((tab, index) => (
                  <Tab.Pane eventKey={tab.eventKey} key={index}>
                    {tab.content}
                  </Tab.Pane>
                ))}
              </Tab.Content>
            </Row>
          </Tab.Container>
        </>
      )}
    </div>
  );
};

export default ReportsPage;
