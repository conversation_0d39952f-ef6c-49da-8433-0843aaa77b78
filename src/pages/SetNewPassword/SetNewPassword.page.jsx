import {Link} from "react-router-dom";
import {MdChevronLeft} from "react-icons/md";
import {AiOutlineEye, AiOutlineEyeInvisible} from "react-icons/ai";
import {useState} from "react";

const SetNewPasswordPage = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };
    const toggleConfirmPasswordVisibility = () => {
        setShowConfirmPassword(!showConfirmPassword);
    };
    return (
        <div className={"user-auth-containers"}>
            <div className={"user-auth-form"}>
                <div className={"form-title"}>
                    Set your new password
                </div>
                <div className={"form-description my-5 text-center"}>
                    Your new password should be different from
                    passwords previously used
                </div>
                <div className={"password-input-container"}>
                    <input className={"auth-form-input mb-3"} type={showPassword ? 'text' : 'password'}
                                      placeholder="New Password"/>
                    <div className={"password-input-icon"} onClick={togglePasswordVisibility}>
                        {showPassword ? <AiOutlineEye size={25}/> : <AiOutlineEyeInvisible size={25}/>}
                    </div>
                </div>
                <div className={"password-input-container"}>
                    <input className={"auth-form-input mb-3"} type={showConfirmPassword ? 'text' : 'password'}
                           placeholder="Confirm password"/>
                    <div className={"password-input-icon"} onClick={toggleConfirmPasswordVisibility}>
                        {showConfirmPassword ? <AiOutlineEye size={25}/> : <AiOutlineEyeInvisible size={25}/>}
                    </div>
                </div>
                    <div role={"button"} className={"submit-btn my-4 px-5 py-3"}>
                        Confirm
                    </div>
                <Link to={"/client/login"} role={"button"} className={"green-label"}> <MdChevronLeft size={20} /> Back to login</Link>
            </div>
        </div>
    );
};

export default SetNewPasswordPage;
