import "./PrivacyPolicy.css";
import { Col, Nav, Row, Tab, Form } from "react-bootstrap";
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from "react";

const PrivacyPolicyPage = () => {
    const { t } = useTranslation();
    const [rolesData, setRolesData] = useState([]);
    const [selectedSection, setSelectedSection] = useState('introduction');
    const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth <= 768);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    useEffect(() => {
        // Create sections with translations directly in the component
        setRolesData([
            {
                key: 'introduction',
                title: t('privacy.introduction'),
                content: [
                    { 
                        subtitle: t('privacy.overview'), 
                        description: t('privacy.overviewDetails') 
                    },
                    { 
                        subtitle: t('privacy.lastUpdated'),
                        description: ""
                    }
                ],
            },
            {
                key: 'information',
                title: t('privacy.informationWeCollect'),
                content: [
                    { 
                        subtitle: t('privacy.personalData'), 
                        description: t('privacy.personalDataDetails') 
                    },
                    { 
                        subtitle: t('privacy.automaticData'), 
                        description: t('privacy.automaticDataDetails') 
                    }
                ],
            },
            {
                key: 'use',
                title: t('privacy.howWeUseInfo'),
                content: [
                    { 
                        subtitle: t('privacy.weUseCollectedInfo'), 
                        description: t('privacy.useDetails') 
                    }
                ],
            },
            {
                key: 'share',
                title: t('privacy.howWeShareInfo'),
                content: [
                    { 
                        subtitle: '', 
                        description: t('privacy.shareDetails') 
                    }
                ],
            },
            {
                key: 'security',
                title: t('privacy.dataSecurity'),
                content: [
                    { 
                        subtitle: '', 
                        description: t('privacy.securityDetails') 
                    }
                ],
            },
            {
                key: 'rights',
                title: t('privacy.yourRights'),
                content: [
                    { 
                        subtitle: '', 
                        description: t('privacy.rightsDetails') 
                    },
                    { 
                        subtitle: '', 
                        description: t('privacy.exerciseRights') 
                    }
                ],
            },
            {
                key: 'changes',
                title: t('privacy.changes'),
                content: [
                    { 
                        subtitle: '', 
                        description: t('privacy.changesDetails') 
                    }
                ],
            },
            {
                key: 'contact',
                title: t('privacy.contactUs'),
                content: [
                    { 
                        subtitle: '', 
                        description: t('privacy.contactDetails') 
                    }
                ],
            }
        ]);
    }, [t]);

    const handleSectionChange = (eventKey) => {
        setSelectedSection(eventKey);
    };

    return (
        <div className={"content-container"}>
            <h1 className="policy-header mb-5">{t('privacy.title')}</h1>
            <Tab.Container 
                defaultActiveKey="introduction" 
                id="privacy-tabs"
                activeKey={selectedSection}
                onSelect={handleSectionChange}
            >
                <Row>
                    <Col sm={isMobile ? 12 : 3} className="mb-4">
                        {isMobile ? (
                            <Form.Select 
                                value={selectedSection}
                                onChange={(e) => handleSectionChange(e.target.value)}
                                className="w-100 mb-4"
                            >
                                {rolesData.map((role) => (
                                    <option key={role.key} value={role.key}>
                                        {role.title}
                                    </option>
                                ))}
                            </Form.Select>
                        ) : (
                            <Nav variant="pills" className="flex-column policy-navs">
                                {rolesData.map((role) => (
                                    <Nav.Item key={role.key} title={role.title}>
                                        <Nav.Link eventKey={role.key}>{role.title}</Nav.Link>
                                    </Nav.Item>
                                ))}
                            </Nav>
                        )}
                    </Col>
                    <Col sm={isMobile ? 12 : 9}>
                        <Tab.Content>
                            {rolesData.map((role) => (
                                <Tab.Pane key={role.key} eventKey={role.key}>
                                    <>
                                        <h2>{role.title}</h2>
                                        {role.content.map((item, index) => (
                                            <div key={index} className="my-5">
                                                {item.subtitle && <h3 className="mb-2">{item.subtitle}</h3>}
                                                {item.description && (
                                                    <div className="privacy-content">
                                                        {item.description.split('\n').map((line, i) => (
                                                            <p key={i}>{line}</p>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </>
                                </Tab.Pane>
                            ))}
                        </Tab.Content>
                    </Col>
                </Row>
            </Tab.Container>
        </div>
    );
};

export default PrivacyPolicyPage;