import { useEffect, useState } from "react";
import clearBackEndCacheAPI from "../../services/clear-cache-backend";

const ClearBackEndCache = () => {
  const [cleared, setCleared] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const clearFn = async () => {
      try {
        await clearBackEndCacheAPI();
        if (isMounted) {
          setCleared(true);
        }
      } catch (e) {
        console.error("Error clearing backend cache:", e);
        if (isMounted) {
          setError("Failed to clear cache.");
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    clearFn();

    // Cleanup function to update the mounted status
    return () => {
      isMounted = false;
    };
  }, []);

  if (loading) {
    return <div>Clearing...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  return <div>{cleared ? "Cleared" : "Clearing..."}</div>;
};

export default ClearBackEndCache;
