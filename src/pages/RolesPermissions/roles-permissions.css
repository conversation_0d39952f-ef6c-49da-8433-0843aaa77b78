.roles-tabs-navs {
    border-radius: 50px;
    background: #FFF;
    box-shadow: 0 4px 28px 0 rgba(103, 103, 103, 0.10);
    width: fit-content;
}

.roles-tabs-navs.nav-pills .nav-link {
    border-radius: 50px;
    color: #B1B1B1;
    font-size: 16px;
    font-weight: 400;
    padding: 17px 32px;
}

.roles-tabs-navs.nav-pills .nav-link.active {
    background: linear-gradient(191deg, #92C020 -12.94%, #CAD511 110.68%);
    box-shadow: 0 4px 28px 0 rgba(103, 103, 103, 0.10);
    color: #FFFFFF;
    font-weight: 900;
    transition: ease-in-out all 0.25s;
}

.admin-navs {
    border-radius: 19px;
    background: #FFF;
}

.admin-navs.nav-underline .nav-link {
    color: #000;
    font-size: 1rem;
    font-weight: 400;
    width: fit-content;
}

.admin-navs.nav-underline .nav-link.active{
    color: #000;
    font-weight: 900;
    background-color: transparent;
    transition: ease-in-out all 0.5s;
}

.role-switch .form-check-input:checked {
    background-color: #92C020;
    border-color: #92C020;
}

.role-switch.form-switch .form-check-input {
    margin-top: 0;
    width: 45px;
    height: 25px;
}

.role-card {
    padding: 14px;
    border-radius: 12px;
    background: #FFF;
    box-shadow: 0 4px 50px -7px rgba(0, 0, 0, 0.10);
}

.role-card:has(.form-check-input:checked) {
    border-radius: 12px;
    background: #F6FFE1;
}

.permission-accordion .accordion-button {
    background-color: #f8f9fa;
    border: none;
    padding: 1rem;
}

.permission-accordion .accordion-button:not(.collapsed) {
    background-color: #e9ecef;
    color: #212529;
    box-shadow: none;
}

.permission-accordion .accordion-body {
    padding: 1rem;
    background-color: white;
}

.permission-accordion .permission-item {
    padding: 0.4rem 0;
    border-bottom: 1px solid #dee2e6;  /* Made border thicker and darker for visibility */
}

.permission-accordion .permission-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.permission-accordion .permission-item:first-child {
    padding-top: 0;
}

.permission-accordion .form-group {
    margin: 0;  /* Remove default margin */
}

.permission-accordion .form-group:last-child {
    border-bottom: none;  /* Remove border from last item */
    padding-bottom: 0;    /* Remove padding from last item */
}

.permission-accordion .form-group:first-child {
    padding-top: 0;       /* Remove padding from first item */
}

.permission-select-all {
    font-size: 0.9rem;
    padding-top: 3px;
}

.permission-select-all .form-check-label {
    padding-top: 2px;
}

.permission-switch {
    margin-left: 1rem;
}

.permission-accordion .form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.permission-accordion .accordion-button:focus {
    box-shadow: none;
    border-color: rgba(0, 0, 0, 0.125);
}

.global-controls {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.global-control-switch {
    font-weight: 500;
}

.global-control-switch .form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

/* Fix switch direction for both RTL and LTR */
[dir="rtl"] .form-switch {
    padding-right: 2.5em;
    padding-left: 0;
}

[dir="rtl"] .form-switch .form-check-input {
    margin-right: -2.5em;
    margin-left: 0;
    float: right;
}

[dir="rtl"] .form-switch .form-check-input:checked {
    background-position: right center;
}

/* Ensure consistent switch behavior in both directions */
.form-switch .form-check-input:checked {
    background-position: right center;
}

.form-switch .form-check-input {
    margin-left: -2.5em;
    background-position: left center;
    transition: background-position .15s ease-in-out;
}

/* Specific styles for permission switches */
.permission-switch.form-check {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.permission-switch .form-check-input {
    cursor: pointer;
}
