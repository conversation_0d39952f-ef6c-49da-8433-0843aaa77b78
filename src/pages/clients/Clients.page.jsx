import AllLeadsComponent from "../../components/ClientsTabsContent/AllClients/AllLeadsComponent";
import { useTranslation } from "react-i18next";
const ClientsPage = () => {
const { t } = useTranslation();
    // const leadsTabs = [{label: "All Leads", eventKey: "all_leads"}, {label: "Follow Ups", eventKey: "follow_up"}, {label: "Recently Active", eventKey: "recently_active"}];
    return (
        // <Tab.Container id="leads-tabs" defaultActiveKey="all_leads">
        //     <Nav variant="pills" className={"justify-content-center mx-auto leads-tabs-navs"}>
        //         {leadsTabs.map((tab, index) => (
        //                 <Nav.Item key={index}>
        //                     <Nav.Link eventKey={tab.eventKey}>{tab.label}</Nav.Link>
        //                 </Nav.Item>
        //             ))}
        //     </Nav>
        <>
        <h2 className={"page-title"}>
            {t("pageHeaders.allLeads")}
        </h2>
                       <AllLeadsComponent />
        </>
        // </Tab.Container>
);
};

export default ClientsPage;
