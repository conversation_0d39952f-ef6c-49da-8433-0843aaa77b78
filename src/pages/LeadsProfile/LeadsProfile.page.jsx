import { Nav, Tab, Stack } from "react-bootstrap";
import { BiSolidUserCheck } from "react-icons/bi";
import { PiTrashFill } from "react-icons/pi";
import "./LeadsProfile.css";
import { useEffect, useState } from "react";
import "../../components/UserActivitiesTabs/UserActivities.css";
import { useNavigate, useParams } from "react-router-dom";
import AssignTeamModal from "../../components/Modals/AssignTeamModal";
import { IoCaretBack } from "react-icons/io5";
import ProfilePictureComponent from "../../components/ProfilePicture/ProfilePicture.component";
import leadService from "../../services/leads";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import DeleteLeadModalContent from "../../components/Modals/DeleteLeadModal";
import { toast } from "react-toastify";
import ActivitiesTab from "../../components/LeadProfile/ActivitiesTab";
import { format, parseISO } from "date-fns";
import Overview from "../../components/LeadProfile/Overview";
import getAllTeamMembers from "../../services/teams/get-teams.api";
import { useSelector, useDispatch } from "react-redux";
import {
  setTeamMembers,
  setDisableAddMember,
} from "../../redux/features/clientSlice";
import { ReactSVG } from "react-svg";
import { useTranslation } from "react-i18next";
import { sourceToIcon } from "../../constants/sourceIcons";

const LeadsProfilePage = () => {
  const [verticalModalShow, setVerticalModalShow] = useState(false);
  const [leadDetails, setLeadDetails] = useState(null);
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [lastActivityDate, setLastActivityDate] = useState(null);
  const [notFound, setNotFound] = useState(false);
  const params = useParams();
  const { currentUserPermissions } = useSelector((state) => state.auth);
  const [selectedTeamMember, setSelectedTeamMember] = useState(null);
  const dispatch = useDispatch();
  const { teamMembers } = useSelector((state) => state.client);
  const { t } = useTranslation();
  useEffect(() => {
    const fetchData = async () => {
      try {
        const leadsData = await leadService.getSingleLeadApi({id: params.id, flag: "user"});
        if (leadsData.message === "Lead Not Found") {
          setNotFound(true);
          return;
        }
        setLeadDetails(leadsData?.data);
        if (leadsData && leadsData.data && leadsData.data.activities) {
          const lastActivity =
            leadsData?.data?.activities[leadsData.data.activities.length - 1];
          if (lastActivity && lastActivity.next_date) {
            setLastActivityDate(lastActivity.next_date);
          } else if (lastActivity && lastActivity.created_at) {
            const parsedDate = parseISO(lastActivity.created_at);
            const formattedDate = format(parsedDate, "yyyy-MM-dd HH:mm");
            setLastActivityDate(formattedDate);
          } else if (lastActivity && lastActivity.note) {
            const dateRegex = /(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/;
            const match = lastActivity.note.match(dateRegex);
            if (match) {
              const extractedDate = match[1];
              setLastActivityDate(extractedDate);
            } else {
              setLastActivityDate(null);
            }
          }
        } else {
          setLastActivityDate(null);
        }
      } catch (error) {
        toast.error(error?.response?.data?.message, {
          position: "bottom-right",
          theme: "dark",
        });
        console.error("Error fetching initial data:", error);
      }
    };
    fetchData();
  }, [params.id]);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const result = await getAllTeamMembers();
        dispatch(setTeamMembers(result?.data?.members));
        dispatch(setDisableAddMember(result?.data?.quota === 0));
      } catch (error) {
        console.log(error);
      }
    };
    fetchTeamMembers();
  }, []);
  const handleSelect = (member) => {
    if (selectedTeamMember && selectedTeamMember.id === member.id)
      setSelectedTeamMember(null);
    else setSelectedTeamMember(member);
  };

  useEffect(() => {
    setSelectedTeamMember(leadDetails?.assigned_to);
  }, [teamMembers]);

  const navigate = useNavigate();

  const parsedDate = leadDetails?.date ? parseISO(leadDetails?.date) : null;
  const parsedCreatedAt = leadDetails?.date
    ? parseISO(leadDetails?.date)
    : leadDetails?.created_at
    ? parseISO(leadDetails?.created_at)
    : null;
  const formattedCreatedAt = parsedCreatedAt
    ? format(parsedCreatedAt, "yyyy-MM-dd HH:mm")
    : null;
  const formattedDate = parsedDate
    ? format(parsedDate, "yyyy-MM-dd HH:mm")
    : null;
  const IconComponent = sourceToIcon[leadDetails?.source] || null;

  return (
    <>
      {notFound ? (
        <>
          <div
            className={"content-container mainColor fs-1 text-center fw-bold"}
          >
            Lead Not Found
          </div>
          <center onClick={() => navigate(-1)}>
            <div className={"submit-btn"}>Go Back</div>
          </center>
        </>
      ) : (
        <>
          <div
            role={"button"}
            onClick={() => navigate(-1)}
            title={"Back To Clients"}
          >
            <IoCaretBack
              color={"#000"}
              className={"bg-white rounded-circle p-1"}
              size={35}
            />
          </div>
          <section className={"content-container lead-profile"}>
            <Tab.Container defaultActiveKey={"activities"}>
              <div className={"text-center"}>
                <ProfilePictureComponent />
                <div
                  className={
                    "my-2 fw-bold d-flex justify-content-center align-content-center"
                  }
                >
                  <div
                    className={
                      "me-2 d-flex justify-content-between align-items-center"
                    }
                  >
                    <div>{leadDetails?.name}</div>

                    {IconComponent && (
                      <ReactSVG
                        className={"lead-source-icon"}
                        src={IconComponent}
                      />
                    )}
                  </div>
                </div>
                <div className={"opacity-50"}>{leadDetails?.email}</div>
                <Stack
                  direction={"horizontal"}
                  className={
                    "align-content-center align-items-center justify-content-between flex-column flex-md-row"
                  }
                >
                  {currentUserPermissions?.includes("lead-edit") ? (
                    <div onClick={() => setVerticalModalShow(true)}>
                      <div className={"assign-client-icon"}>
                        <BiSolidUserCheck size={25} />
                      </div>
                      <div className={"text-nowrap"}>
                        {leadDetails?.assigned_to
                          ? `${t("leadProfile.assignedTo")}: ${
                              leadDetails?.assigned_to?.name
                            }`
                          : "Assign"}
                      </div>
                    </div>
                  ) : null}
                  <Nav
                    variant="pills"
                    className={
                      "justify-content-center mx-auto user-profile-tabs my-4"
                    }
                  >
                    <Nav.Item className={"overview-tab"}>
                      <Nav.Link eventKey="overview">
                        {t("leadProfile.overview")}
                      </Nav.Link>
                    </Nav.Item>
                    <Nav.Item className={"activities-tab"}>
                      <Nav.Link eventKey="activities">
                        {t("leadProfile.activities")}
                      </Nav.Link>
                    </Nav.Item>
                  </Nav>
                  {currentUserPermissions?.includes("lead-delete") ? (
                    <div
                      className={"text-danger"}
                      onClick={() => setShowCenteredModal(true)}
                    >
                      <PiTrashFill
                        size={40}
                        className={"profile-icon-container"}
                      />
                      <p>{t("common.delete")}</p>
                    </div>
                  ) : null}
                </Stack>
              </div>

              <Tab.Content>
                <Tab.Pane eventKey="overview">
                  <Overview
                    leadDetails={leadDetails}
                    formattedCreatedAt={formattedDate || formattedCreatedAt}
                    lastActivityDate={lastActivityDate || formattedCreatedAt}
                    setLeadDetails={setLeadDetails}
                  />
                </Tab.Pane>
                <Tab.Pane eventKey="activities">
                  <ActivitiesTab
                    leadDetails={leadDetails}
                    setLeadDetails={setLeadDetails}
                  />
                </Tab.Pane>
              </Tab.Content>
            </Tab.Container>
          </section>
          <AssignTeamModal
            setSelectedTeamMember={setSelectedTeamMember}
            selectedTeamMember={selectedTeamMember}
            handleSelect={handleSelect}
            show={verticalModalShow}
            onHide={() => setVerticalModalShow(false)}
            setLeadDetails={setLeadDetails}
            leadDetails={leadDetails}
          />
          <CenteredModal
            show={showCenteredModal}
            children={
              <DeleteLeadModalContent
                onHide={() => setShowCenteredModal(false)}
              />
            }
            onHide={() => setShowCenteredModal(false)}
          />
        </>
      )}
    </>
  );
};

export default LeadsProfilePage;
