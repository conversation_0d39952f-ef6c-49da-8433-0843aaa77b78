import { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Card, Form, Row, Col, Button } from "react-bootstrap";
import { FaMagnifyingGlass, FaArrowLeft } from "react-icons/fa6";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import PaginationRecordsForReports from "../../components/Reports/PaginationRecordsForReports";
import { Tooltip } from "react-tooltip";
import { ReactSVG } from "react-svg";
import { sourceToIcon } from "../../constants/sourceIcons";
import { format } from "date-fns";
import { Link } from "react-router-dom";
import adsService from "../../services/ads";
import NavigateBackComponent from "../AdminDashboard/NavigateBack.component";
import "./Ads.page.css";

export default function AdLeadsPage() {
  const { t } = useTranslation();
  const { adId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [leads, setLeads] = useState([]);
  const [leadsApiResponse, setLeadsApiResponse] = useState(null);
  const [leadsSearchTerm, setLeadsSearchTerm] = useState("");
  const [selectedAd, setSelectedAd] = useState(null);

  useEffect(() => {
    if (adId) {
      // Check if ad info was passed via navigation state
      const adInfo = location.state?.adInfo;
      if (adInfo) {
        setSelectedAd(adInfo);
      } else {
        // Fallback: set loading state and try to get info from API response
        setSelectedAd({ ad_id: adId, ad_name: `Loading...` });
      }

      fetchLeadsForAd(adId, 1, 10);
    }
  }, [adId, location.state]);

  const fetchLeadsForAd = async (adId, page = 1, recordsPerPageParam = 10) => {
    setLoading(true);
    try {
      const response = await adsService.getAdLeadsApi(
        adId,
        recordsPerPageParam,
        page
      );

      if (
        (response?.status === 200 ||
          response?.success ||
          response?.message === "Success") &&
        response?.data
      ) {
        // Handle nested data structure - leads are in response.data.data
        setLeads(response.data.data || []);
        setLeadsApiResponse(response.data); // Store full API response for pagination

        // Only try to extract ad info from response if we don't already have it from navigation state
        if (!location.state?.adInfo) {
          if (response.data.ad_name) {
            // If the API response includes ad information
            setSelectedAd({
              ad_id: adId,
              ad_name: response.data.ad_name,
            });
          } else if (response.data.data && response.data.data.length > 0) {
            // If leads exist, we can try to get ad info from the first lead
            const firstLead = response.data.data[0];
            if (firstLead.ad_name) {
              setSelectedAd({
                ad_id: adId,
                ad_name: firstLead.ad_name,
              });
            } else {
              // Fallback to ad ID
              setSelectedAd({ ad_id: adId, ad_name: `Ad ${adId}` });
            }
          } else {
            // No leads, fallback to ad ID
            setSelectedAd({ ad_id: adId, ad_name: `Ad ${adId}` });
          }
        }
      } else {
        setLeads([]);
        setLeadsApiResponse(null);
      }
    } catch (error) {
      console.error("Error fetching leads for ad:", error);
      setLeads([]);
      setLeadsApiResponse(null);
    } finally {
      setLoading(false);
    }
  };

  const handleLeadsSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setLeadsSearchTerm(newSearchTerm);
  };

  const handleBackToAds = () => {
    navigate("/ads");
  };

  // Pagination handlers for leads
  const handleLeadsPageChange = (url) => {
    if (!url || !adId) return;

    // Extract page and per_page from URL
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const page = parseInt(
      urlParams.get("current_page") || urlParams.get("page") || "1"
    );
    const perPage = parseInt(urlParams.get("per_page") || "10");

    fetchLeadsForAd(adId, page, perPage);
  };

  const handleLeadsPageSizeChange = (size) => {
    if (!adId) return;
    fetchLeadsForAd(adId, 1, size); // Reset to first page when changing page size
  };

  // Leads table columns
  const leadsColumns = useMemo(
    () => [
      {
        Header: t("leadsTable.columns.id"),
        accessor: "id",
        Cell: ({ row }) => (
          <div className="text-center">
            <span>{row.index + 1}</span>
          </div>
        ),
      },
      {
        Header: t("leadsTable.columns.contactName"),
        accessor: "name",
        Cell: ({ row }) => {
          const name = row.original.name;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-name-${row.original.id} mx-auto text-decoration-none text-dark`}
                style={{ maxWidth: "200px", color: "inherit" }}
              >
                {name}
              </Link>
              <Tooltip
                anchorSelect={`.lead-name-${row.original.id}`}
                content={name}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.phone"),
        accessor: "phone",
        Cell: ({ row }) => {
          const phone = row.original.phone;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-phone-${row.original.id} mx-auto text-decoration-none text-dark`}
                style={{ maxWidth: "150px", color: "inherit" }}
              >
                {phone}
              </Link>
              <Tooltip
                anchorSelect={`.lead-phone-${row.original.id}`}
                content={phone}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("tables.headers.email"),
        accessor: "email",
        Cell: ({ row }) => {
          const email = row.original.email;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-email-${row.original.id} mx-auto text-decoration-none text-dark`}
                style={{ maxWidth: "200px", color: "inherit" }}
              >
                {email}
              </Link>
              <Tooltip
                anchorSelect={`.lead-email-${row.original.id}`}
                content={email}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.source"),
        accessor: "source",
        Cell: ({ row }) => {
          const source = row.original.source;
          const IconComponent = sourceToIcon[source] || null;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className="mx-auto social-icon-container text-decoration-none"
                style={{ color: "inherit" }}
              >
                {IconComponent && <ReactSVG src={IconComponent} />}
              </Link>
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.status"),
        accessor: "status",
        Cell: ({ row }) => {
          const status = row.original.status;
          const statusMapping = {
            0: {
              label: t("status.pending"),
              className: "status-badge--pending",
            },
            1: {
              label: t("status.inProgress"),
              className: "status-badge--in-progress",
            },
            2: {
              label: t("status.completed"),
              className: "status-badge--completed",
            },
            3: {
              label: t("status.rejected"),
              className: "status-badge--rejected",
            },
            4: {
              label: t("status.wrongLead"),
              className: "status-badge--wrong-lead",
            },
            5: {
              label: t("status.notQualified"),
              className: "status-badge--not-qualified",
            },
            6: {
              label: t("status.noCommunication"),
              className: "status-badge--no-communication",
            },
            7: { label: t("status.booked"), className: "status-badge--booked" },
            8: {
              label: t("status.bookedReserved"),
              className: "status-badge--booked-reserved",
            },
            9: {
              label: t("status.canceled"),
              className: "status-badge--canceled",
            },
            10: {
              label: t("status.quotation"),
              className: "status-badge--quotation-sent",
            },
            11: {
              label: t("status.assigned"),
              className: "status-badge--in-progress",
            },
          };

          const { label, className } = statusMapping[status] || {
            label: "Unknown",
            className: "status-badge--unknown",
          };

          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`status-badge ${className} rounded-pill p-1 text-decoration-none`}
                style={{
                  fontSize: "0.8rem",
                  fontWeight: 600,
                  color: "inherit",
                }}
              >
                {label}
              </Link>
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.createdAt"),
        accessor: "created_at",
        Cell: ({ value, row }) => {
          let display = value;
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              display = format(parsedDate, "yyyy-MM-dd HH:mm:ss");
            }
          }
          const classKey = `lead-created-${row.original.id}`;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line ${classKey} mx-auto text-decoration-none text-dark`}
                style={{ maxWidth: "180px", color: "inherit" }}
              >
                {display || "-"}
              </Link>
              {display && (
                <Tooltip
                  anchorSelect={`.${classKey}`}
                  content={display}
                  className={"bg-dark text-white"}
                />
              )}
            </div>
          );
        },
      },
    ],
    [t]
  );

  // Filter leads based on search term (frontend filtering)
  const filteredLeads = useMemo(() => {
    // Ensure leads is always an array
    const leadsArray = Array.isArray(leads) ? leads : [];

    if (!leadsSearchTerm.trim()) {
      return leadsArray;
    }

    const searchLower = leadsSearchTerm.toLowerCase();
    return leadsArray.filter((lead) => {
      return (
        lead.name?.toLowerCase().includes(searchLower) ||
        lead.email?.toLowerCase().includes(searchLower) ||
        lead.phone?.toLowerCase().includes(searchLower)
      );
    });
  }, [leads, leadsSearchTerm]);

  return (
    <>
      <div className="d-flex justify-content-start align-items-center">
        <NavigateBackComponent tooltip={"Back to Ads"} />
        <h4 className="my-4">
          {t("adsTable.leadsForAd", {
            adName: selectedAd?.ad_name || `Ad ${adId}`,
          })}
        </h4>
      </div>
      {loading ? (
        <FetchingDataLoading />
      ) : (
        <>
          <div
            className="mb-3 content-container"
            style={{
              opacity: loading ? 0.5 : 1,
            }}
          >
            <Row className="justify-content-end align-items-center">
              <Col lg={4} md={4} sm={12} className="mb-3">
                <Form.Group className="position-relative">
                  <Form.Control
                    placeholder={`${t(
                      "tableControls.placeholders.searchTable"
                    )} ${filteredLeads.length} leads...`}
                    value={leadsSearchTerm}
                    onChange={handleLeadsSearchChange}
                    className="rounded-pill"
                  />
                  <FaMagnifyingGlass
                    className="text-muted position-absolute"
                    style={{
                      right: "10px",
                      top: "50%",
                      transform: "translateY(-50%)",
                    }}
                  />
                </Form.Group>
              </Col>
            </Row>

            <DataTableComponent
              columns={leadsColumns}
              data={filteredLeads || []}
              loading={loading}
              initialSortBy={[]}
              hiddenColumns={[]}
              noDataFound={t("leadsTable.noLeadsFound") || "No Leads Found"}
            />

            <PaginationRecordsForReports
              onPageChange={handleLeadsPageChange}
              links={leadsApiResponse?.links || []}
              handlePageSizeChange={handleLeadsPageSizeChange}
              per_page={leadsApiResponse?.per_page || 10}
              to={leadsApiResponse?.to || 0}
              total={leadsApiResponse?.total || 0}
              currentPage={leadsApiResponse?.current_page || 1}
            />
          </div>
        </>
      )}
    </>
  );
}
