import WidgestsRow from "../../components/AdminDashboard/WidgestsRow";
import LeadsStatusLineChart from "../../components/AdminDashboard/LeadsStatusLineChart";
import LeadsSourcePieChart from "../../components/AdminDashboard/LeadsSourcePieChart";
import { Col, Container, Row } from "react-bootstrap";
import SalesFunnelBarChart from "../../components/AdminDashboard/SalesFunnelBarChart";
import "../../components/CustomDataTable/datatable.css";
import TopTeamMemberPieChart from "../../components/AdminDashboard/TopTeamMemberPieChart";
import { useEffect, useState } from "react";
import NewLeadsTodayTable from "../../components/AdminDashboard/NewLeadsTodayTable";
import "../../components/LoadingAnimation/LoadingAnimation.css";
import IntegrationsTable from "../../components/AdminDashboard/IntegrationsTable";
import SubscriptionPlans from "../../components/AdminDashboard/SubscriptionPlans";
import adminDashboardService from "../../services/admin/admin-dashboard-services";
import { useSelector } from "react-redux";

const AdminDashboard = () => {
  const [pieChartRoundedData, setPieChartRoundedData] = useState([]);
  const [barChartData, setBarChartData] = useState([]);
  const [lineChartData, setLineChartData] = useState([]);
  const [pieChartSource, setPieChartSource] = useState([]);
  const [lineChartDataDomain, setLineChartDataDomain] = useState(100);
  const [widgetData, setWidgetData] = useState([]);

  const { selectedClient } = useSelector((state) => state.admin);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const results = await Promise.allSettled([
          adminDashboardService.getAdminFilterDataApi(
            selectedClient ? selectedClient?.id : 144
          ),
          adminDashboardService.getAdminLeadsStatusApi(
            selectedClient ? selectedClient?.id : 144
          ),
          adminDashboardService.getAdminLeadsSourceApi(
            selectedClient ? selectedClient?.id : 144
          ),
          adminDashboardService.getAdminFilterCompletedLeads(
            selectedClient ? selectedClient?.id : 144
          ),
          adminDashboardService.getAdminNewLeadsCountApi(
            selectedClient ? selectedClient?.id : 144
          ),
        ]);

        const [
          chartDataResult,
          statusDataResult,
          sourcesDataResult,
          completedLeadsDataResult,
          widgetsDataResult,
        ] = results;

        // Check each result's status and set the data accordingly
        if (chartDataResult.status === "fulfilled") {
          const transformedData = Object.entries(
            chartDataResult.value?.result
          ).map(([month, value]) => ({
            x: month,
            y: value,
          }));
          setBarChartData(transformedData);
        } else {
          console.error("Error fetching chart data:", chartDataResult.reason);
        }

        if (statusDataResult.status === "fulfilled") {
          const statusData = statusDataResult.value;
          setLineChartData(
            Object.keys(statusData?.result)?.map((month) => ({
              x: month,
              y1: {
                value: statusData?.result[month]?.pendding || 0,
                label: `Pending: ${statusData?.result[month]?.pendding || 0}`,
              },
              y2: {
                value: statusData?.result[month]?.inprogress || 0,
                label: `In Progress: ${
                  statusData.result[month].inprogress || 0
                }`,
              },
              y3: {
                value: statusData?.result[month]?.completed || 0,
                label: `Completed: ${
                  statusData?.result[month]?.completed || 0
                }`,
              },
              y4: {
                value: statusData?.result[month]?.rejected || 0,
                label: `Rejected: ${statusData?.result[month]?.rejected || 0}`,
              },
            }))
          );
        } else {
          console.error("Error fetching status data:", statusDataResult.reason);
        }

        if (sourcesDataResult.status === "fulfilled") {
          const sourcesData = sourcesDataResult.value;
          setPieChartRoundedData(
            Object.entries(sourcesData?.result).map(
              ([platform, percentage]) => ({
                x: platform,
                y: parseFloat(percentage),
              })
            )
          );
        } else {
          console.error(
            "Error fetching sources data:",
            sourcesDataResult.reason
          );
        }

        if (completedLeadsDataResult.status === "fulfilled") {
          const completedLeadsData = completedLeadsDataResult.value;
          const transformedPieChartSource = Object.entries(
            completedLeadsData?.result
          ).map(([name, value]) => ({
            x: name,
            y: value,
            color: getRandomColor(),
          }));
          setPieChartSource(transformedPieChartSource);
        } else {
          console.error(
            "Error fetching completed leads data:",
            completedLeadsDataResult.reason
          );
        }

        if (widgetsDataResult.status === "fulfilled") {
          setWidgetData(widgetsDataResult.value?.result);
        } else {
          console.error(
            "Error fetching widget data:",
            widgetsDataResult.reason
          );
        }
      } catch (error) {
        console.error("Unexpected error:", error);
      }
    };

    fetchData();
  }, [selectedClient?.id]);

  useEffect(() => {
    const maxLineChartDataY =
      lineChartData?.length > 0
        ? Math.max(
            ...lineChartData?.map((month) =>
              Math.max(
                month.y1.value,
                month.y2.value,
                month.y3.value,
                month.y4.value
              )
            )
          )
        : 0;
    const maxYAxis = Math.ceil(maxLineChartDataY / 10) * 10;
    setLineChartDataDomain(maxYAxis);
  }, [lineChartData]);

  const getRandomColor = () => {
    // Generate random color code
    return "#" + Math.floor(Math.random() * 16777215).toString(16);
  };

  const getColor = (label) => {
    switch (label) {
      case "facebook":
        return "#1b47f8";
      case "linked":
        return "#0077B5";
      case "tiktok":
        return "#191919";
      case "snapchat":
        return "#BED754";
      case "insta":
        return "red";
      case "global":
        return "#750E21";
      case "google":
        return "#EA4335";
      case "phone":
        return "gray";
      case "whatsapp":
        return "#075E54";
      default:
        return "black";
    }
  };

  return (
    <Container className={"mt-5"}>
      <WidgestsRow widgetData={widgetData} classes={"leads-count-widget"} />
      <Row className={"justify-content-between"}>
        <Col
          lg={8}
          className={"admin-theme content-container"}
          style={{ maxWidth: "800px", height: "fit-content" }}
        >
          <LeadsStatusLineChart lineChartData={lineChartData} />
        </Col>
        <Col
          lg={4}
          md={12}
          className={
            "admin-theme content-container d-flex flex-column justify-content-between"
          }
        >
          <LeadsSourcePieChart
            pieChartRoundedData={pieChartRoundedData}
            getColor={getColor}
          />
        </Col>
      </Row>
      <Row>
        <NewLeadsTodayTable />
      </Row>
      <Row className={"justify-content-between"}>
        <Col lg={4} className={"my-3 admin-theme content-container"}>
          <TopTeamMemberPieChart pieChartSource={pieChartSource} />
        </Col>
        <Col
          lg={8}
          className={"my-3 admin-theme content-container"}
          style={{ maxWidth: "800px" }}
        >
          {/*<MapComponent />*/}
          <SalesFunnelBarChart barChartData={barChartData} />
        </Col>
      </Row>
      <Row className={"justify-content-between"}>
        <Col lg={5}>
          <IntegrationsTable />
        </Col>
        <Col lg={7}>
          <SubscriptionPlans />
        </Col>
      </Row>
    </Container>
  );
};

export default AdminDashboard;
