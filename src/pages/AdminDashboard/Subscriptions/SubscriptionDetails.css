.subscription-tabs .nav-item .nav-link {
  color: #fff !important;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.subscription-tabs .nav-link:hover {
  opacity: 0.8;
}

/* Remove default Bootstrap focus styles */
.subscription-tabs .nav-link:focus {
  box-shadow: none;
}

/* Remove default active blue background */
.subscription-tabs .nav-link.active {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Success (Plus & Total Active) */
.subscription-tabs .nav-link.active[class*="border-success"] {
  background-color: #38B000 !important;
}

/* Danger (Downgraded) */
.subscription-tabs .nav-link.active[class*="border-danger"] {
  background-color: #EF476F !important;
}

/* Warning (Custom & Expiring) */
.subscription-tabs .nav-link.active[class*="border-warning"] {
  background-color: #FFD60A !important;
  color: #000 !important; /* Dark text for better contrast on yellow */
}

/* Info (Free) */
.subscription-tabs .nav-link.active[class*="border-info"] {
  background-color: #4AC1E9 !important;
}

/* Override any Bootstrap default styles */
.subscription-tabs .nav-pills .nav-link.active,
.subscription-tabs .nav-pills .show > .nav-link {
  background-color: transparent;
}

