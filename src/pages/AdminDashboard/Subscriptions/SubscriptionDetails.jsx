import { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Card, Col, FormControl, InputGroup, Nav, Row, Table, Tab } from 'react-bootstrap';
import { AiOutlineSearch } from 'react-icons/ai';
import { BsArrowLeft } from 'react-icons/bs';
import { useNavigate } from 'react-router-dom';
import { useTable, useSortBy, usePagination } from 'react-table';
import { getAnalysisDataApi, getAnalysisTablesApi } from '../../../services/admin';
import AdminPaginationComponent from '../../../components/AdminPagination/AdminPaginationComponent';
import { cleanData } from '../../../utils/clean-data';
import DateFilter from '../../../components/DateFilter/DateFilter';
import { format } from 'date-fns';
import { MdClear } from 'react-icons/md';
import './SubscriptionDetails.css';
import useDebounce from '../../../utils/use-debounce';
import { IoCaretBack } from 'react-icons/io5';
import NavigateBackComponent from '../NavigateBack.component';

// Separate component for the search bar
const SearchBar = ({ searchTerm, onSearch }) => {
  const handleClear = () => {
    onSearch('');
  };

  return (
    <InputGroup>
      <FormControl
        placeholder="Search for user subscription"
        className="bg-secondary text-light border-0"
        value={searchTerm}
        onChange={(e) => onSearch(e.target.value)}
      />
      <Button
        variant="outline-light"
        onClick={searchTerm ? handleClear : undefined}
      >
        {searchTerm ? (
          <MdClear size={20} />
        ) : (
          <AiOutlineSearch size={20} />
        )}
      </Button>
    </InputGroup>
  );
};

const SubscriptionDetails = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('total');
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showDateFilter, setShowDateFilter] = useState(false);
  const [dateRange, setDateRange] = useState({ startDate: null, endDate: null });
  const [appliedDateRange, setAppliedDateRange] = useState({ startDate: null, endDate: null });
  const [tableData, setTableData] = useState({
    total: [],
    downgraded: [],
    ending_soon: [],
    free: [],
    plus: [],
    custom: []
  });
  const [counters, setCounters] = useState({
    total: 0,
    downgraded: 0,
    ending_soon: 0,
    free: 0,
    plus: 0,
    custom: 0
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);

  const debouncedSearchTerm = useDebounce(searchTerm, 2000);

  // Date filter handlers
  const handleDateChange = (newRange) => setDateRange(newRange);

  const handleApplyDateFilter = () => {
    if (dateRange.startDate && dateRange.endDate) {
      setAppliedDateRange({
        startDate: new Date(dateRange.startDate),
        endDate: new Date(dateRange.endDate)
      });
      setShowDateFilter(false);
      setCurrentPage(1);
    }
  };

  const handleClearDateFilter = () => {
    const clearedRange = { startDate: null, endDate: null };
    setDateRange(clearedRange);
    setAppliedDateRange(clearedRange);
    setShowDateFilter(false);
    setCurrentPage(1);
  };

  // Add this helper function for counter colors
  const getCounterColor = () => {
    switch (activeTab) {
      case 'downgraded':
        return '#EF476F';
      case 'ending_soon':
        return '#FFD60A';
      case 'free':
        return '#4AC1E9';
      case 'plus':
        return '#38B000';
      case 'custom':
        return '#F29E4C';
      default:
        return '#38B000';
    }
  };

  // Add this computed value for filtered and sorted data
  const filteredAndSortedData = useMemo(() => {
    const result = {};
    Object.keys(tableData).forEach(tab => {
      result[tab] = tableData[tab].filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
    return result;
  }, [tableData, searchTerm]);

  // Fetch data
  useEffect(() => {
    const fetchTableData = async () => {
      setLoading(true);
        const params = {
          name: debouncedSearchTerm,
          page: currentPage,
          per_page: recordsPerPage,
          type: activeTab,
          start_date: appliedDateRange.startDate,
          end_date: appliedDateRange.endDate
        };
        const validParams = cleanData(params);
        const response = await getAnalysisTablesApi(validParams);
        const Analysis = response.analysis;

        setCounters({
          total: Analysis.total_active || 0,
          downgraded: Analysis.downgraded_users_count || 0,
          ending_soon: Analysis.subscriptions_ending_soon || 0,
          free: Analysis.free || 0,
          plus: Analysis.plus || 0,
          custom: Analysis.custom || 0
        })

        setTableData(prev => ({
          ...prev,
          [activeTab]: response.data.data.map(item => ({
            id: item.id,
            name: item.user.name,
            startDate: item.subscription_at,
            endDate: item.end_date,
            duration: calculateDuration(item.subscription_at, item.end_date),
            type: item.package.title,
            before: item.previous_package?.title
          }))
        }));

        setTotalPages(prev => ({
          ...prev,
          [activeTab]: response.data.last_page
        }));
        setLoading(false);
    };

    fetchTableData();
  }, [debouncedSearchTerm, activeTab, currentPage, recordsPerPage, appliedDateRange]);

  // Helper function to calculate duration
  const calculateDuration = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffMonths = (end.getFullYear() - start.getFullYear()) * 12 +
                      (end.getMonth() - start.getMonth());
    return `${diffMonths} months`;
  };

  // Define columns based on active tab
  const columns = useMemo(() => {
    const baseColumns = [
      {
        Header: '#',
        Cell: ({ row }) => row.index + 1,
        style: { width: '50px' }
      },
      {
        Header: 'Name',
        accessor: 'name'
      },
      {
        Header: 'Start Date',
        accessor: 'startDate'
      },
      {
        Header: 'Duration',
        accessor: 'duration'
      },
      {
        Header: 'Type',
        accessor: 'type',
        Cell: ({ value }) => (
          <Badge bg={getTypeColor(value)}>{value}</Badge>
        )
      }
    ];

    if (activeTab === 'ending_soon') {
      baseColumns.splice(3, 0, {
        Header: 'End Date',
        accessor: 'endDate'
      });
    }

    if (activeTab === 'downgraded') {
      baseColumns.push({
        Header: 'Previous Package',
        accessor: 'before',
        Cell: ({ value }) => (
          <Badge bg={getTypeColor(value)}>{value}</Badge>
        )
      });
    }

    return baseColumns;
  }, [activeTab]);

  // Initialize react-table
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page
  } = useTable(
    {
      columns,
      data: tableData[activeTab] || [],
      initialState: {
        pageIndex: currentPage - 1,
        pageSize: recordsPerPage
      },
      manualPagination: true,
      pageCount: totalPages[activeTab]
    },
    useSortBy,
    usePagination
  );

  // Helper function for badge colors
  const getTypeColor = (type) => {
    switch (type?.toLowerCase()) {
      case 'free': return 'info';
      case 'plus': return 'success';
      case 'custom': return 'warning';
      default: return 'secondary';
    }
  };

  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Active Filters component
  const ActiveFilters = () => {
    if (!appliedDateRange?.startDate && !debouncedSearchTerm) {
      return null;
    }

    return (
      <div className="mb-3 d-flex align-items-center">
        <small className="text-muted me-2">Active Filters:</small>
        <div className="d-flex gap-2 flex-wrap align-items-center">
          {debouncedSearchTerm && (
            <Badge
              bg="secondary"
              className="d-flex align-items-center gap-2"
              style={{ cursor: 'pointer' }}
              onClick={() => setSearchTerm('')}
            >
              Search: {debouncedSearchTerm}
              <MdClear />
            </Badge>
          )}
          {appliedDateRange?.startDate && appliedDateRange?.endDate && (
            <Badge
              bg="secondary"
              className="d-flex align-items-center gap-2"
              style={{ cursor: 'pointer' }}
              onClick={handleClearDateFilter}
            >
              Date: {format(appliedDateRange.startDate, 'MM/dd/yyyy')} - {format(appliedDateRange.endDate, 'MM/dd/yyyy')}
              <MdClear />
            </Badge>
          )}
          {(debouncedSearchTerm || appliedDateRange?.startDate) && (
            <Badge
              bg="secondary"
              className="d-flex align-items-center gap-2"
              style={{ cursor: 'pointer' }}
              onClick={() => {
                setSearchTerm('');
                handleClearDateFilter();
              }}
            >
              Clear All
              <MdClear />
            </Badge>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
    <NavigateBackComponent title={"Back to Subscriptions"} />
        <Card bg="dark" text="light" className="mb-5 border-success">

      <Card.Body>
        <Row className="mb-4">
          <Col>
            <h1 className="text-center">Subscriptions</h1>
            <h4 className="text-center mb-4" style={{
              color: getCounterColor()
            }}>
              {activeTab === 'downgraded' ? 'Downgrade Subscriptions' :
               activeTab === 'ending_soon' ? 'Subscriptions Expiring Within 7 Days' :
               activeTab === 'free' ? 'Free Subscriptions' :
               activeTab === 'plus' ? 'Plus Subscriptions' :
               activeTab === 'custom' ? 'Custom Subscriptions' : 'Total Active Subscriptions'}
            </h4>
          </Col>
        </Row>

        <Row className="align-items-center mb-4 px-2 justify-content-between">
          <Col md={6}>
            <SearchBar searchTerm={searchTerm} onSearch={setSearchTerm} />
          </Col>
          <Col md={6} className="text-end d-flex justify-content-end">
            <DateFilter
              showFilter={showDateFilter}
              onToggle={setShowDateFilter}
              dateRange={dateRange}
              onDateChange={handleDateChange}
              onApply={handleApplyDateFilter}
              onClear={handleClearDateFilter}
            />
          </Col>
        </Row>

        <ActiveFilters />

        <Tab.Container id="subscription-tabs" activeKey={activeTab} onSelect={(key) => {
          setActiveTab(key);
          setCurrentPage(1);
        }}>
          <Row>
            <Col>
              <Nav variant="pills" className="mb-4 subscription-tabs justify-content-center gap-2">
                <Nav.Item>
                  <Nav.Link
                    eventKey="total"
                    className={`bg-dark border border-success ${activeTab === 'total' ? 'active' : ''}`}
                  >
                    Total Active <Badge bg={activeTab === 'total' ? 'dark' : 'success'} className="ms-2">
                      {counters.total}
                    </Badge>
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link
                    eventKey="downgraded"
                    className={`bg-dark border border-danger ${activeTab === 'downgraded' ? 'active' : ''}`}
                  >
                    Downgrade <Badge bg={activeTab === 'downgraded' ? 'dark' : 'danger'} className="ms-2">
                      {counters.downgraded}
                    </Badge>
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link
                    eventKey="ending_soon"
                    className={`bg-dark border border-warning ${activeTab === 'ending_soon' ? 'active' : ''}`}
                  >
                    Expiring <Badge bg={activeTab === 'ending_soon' ? 'dark' : 'warning'} className="ms-2">
                      {counters.ending_soon}
                    </Badge>
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link
                    eventKey="free"
                    className={`bg-dark border border-info ${activeTab === 'free' ? 'active' : ''}`}
                  >
                    Free <Badge bg={activeTab === 'free' ? 'dark' : 'info'} className="ms-2">
                      {counters.free}
                    </Badge>
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link
                    eventKey="plus"
                    className={`bg-dark border border-success ${activeTab === 'plus' ? 'active' : ''}`}
                  >
                    Plus <Badge bg={activeTab === 'plus' ? 'dark' : 'success'} className="ms-2">
                      {counters.plus}
                    </Badge>
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link
                    eventKey="custom"
                    className={`bg-dark border border-warning ${activeTab === 'custom' ? 'active' : ''}`}
                  >
                    Custom <Badge bg={activeTab === 'custom' ? 'dark' : 'warning'} className="ms-2">
                      {counters.custom}
                    </Badge>
                  </Nav.Link>
                </Nav.Item>
              </Nav>
            </Col>
          </Row>

          {loading ? (
            <div className="text-center p-5">
              <span className="spinner-border text-light" role="status" />
            </div>
          ) : (
            <>
              {tableData[activeTab].length === 0 ? (
                <div className="text-center py-5 text-muted">
                  <h5>No subscriptions found</h5>
                </div>
              ) : (
                <>
                  <Table {...getTableProps()} responsive striped bordered hover variant="dark">
                    <thead>
                      {headerGroups.map(headerGroup => (
                        <tr {...headerGroup.getHeaderGroupProps()}>
                          {headerGroup.headers.map(column => (
                            <th {...column.getHeaderProps(column.getSortByToggleProps())}>
                              {column.render('Header')}
                              <span>
                                {column.isSorted
                                  ? column.isSortedDesc
                                    ? ' 🔽'
                                    : ' 🔼'
                                  : ''}
                              </span>
                            </th>
                          ))}
                        </tr>
                      ))}
                    </thead>
                    <tbody {...getTableBodyProps()}>
                      {page.map(row => {
                        prepareRow(row);
                        return (
                          <tr {...row.getRowProps()}>
                            {row.cells.map(cell => (
                              <td {...cell.getCellProps()}>{cell.render('Cell')}</td>
                            ))}
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>

                  <AdminPaginationComponent
                    currentPage={currentPage}
                    totalPages={totalPages[activeTab]}
                    itemsPerPage={recordsPerPage}
                    onPageChange={(page) => {
                      setCurrentPage(page);
                    }}
                    onItemsPerPageChange={(size) => {
                      setRecordsPerPage(size);
                      setCurrentPage(1);
                    }}
                    itemsPerPageOptions={[10, 20, 30, 40, 50]}
                  />
                </>
              )}
            </>
          )}
        </Tab.Container>

        <Row className="mt-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                Number of {activeTab === 'downgraded' ? 'Downgrade' :
                          activeTab === 'ending_soon' ? 'Expiring' :
                          activeTab === 'free' ? 'Free' :
                          activeTab === 'plus' ? 'Plus' :
                          activeTab === 'custom' ? 'Custom' : 'Active'} Subscriptions
              </div>
              <div className="fs-3" style={{ color: getCounterColor() }}>
                {filteredAndSortedData[activeTab].length}<span className="text-secondary fs-5">/{counters.total}</span>
              </div>
            </div>
          </Col>
        </Row>
      </Card.Body>
    </Card>
    </>
  );
};

export default SubscriptionDetails;
