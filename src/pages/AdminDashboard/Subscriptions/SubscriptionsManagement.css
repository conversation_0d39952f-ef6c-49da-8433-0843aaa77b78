.svg-container {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  border-radius: 10%;
  align-items: center;
  border: 1px solid #ffffff;
  cursor: pointer;
}

.icon-svg {
  cursor: pointer;
  border-radius: 2px;
  display: flex;
  transition: fill 0.3s ease;
}

.icon-svg svg {
  fill: #ffffff;
  width: 100%;
  height: 100%;
  width: 20px;
  height: 20px;
}

.icon-svg svg path {
  fill: #ffffff;
}

.chart-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.chart-inner {
  min-width: 800px; /* Ensures minimum width for all months */
  height: 500px;
}

/* Custom scrollbar styling */
.chart-container::-webkit-scrollbar {
  height: 8px;
}

.chart-container::-webkit-scrollbar-track {
  background: #2a2a2a;
  border-radius: 4px;
}

.chart-container::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 4px;
}

.chart-container::-webkit-scrollbar-thumb:hover {
  background: #888;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-inner {
    min-width: 1000px; /* Increased minimum width for better mobile scrolling */
  }
}


.subscription-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.subscription-tabs .nav-link {
  color: #ccc;
  background-color: #343a40;
  border-color: #444;
  border-bottom: none;
  padding: 10px 20px;
  /* border-radius: 5px; */
}

.subscription-tabs .nav-link.active {
  color: white;
  background-color: #198754;
  border-color: #198754;
}

.subscription-tabs .nav-link:hover:not(.active) {
  background-color: #444;
}

.subscription-list li {
  transition: transform 0.2s ease;
  padding: 8px;
  border-radius: 4px;
}

.subscription-list li:hover {
  transform: translateX(5px);
  background: rgba(190, 215, 84, 0.1);
}

.donut-chart-container {
  position: relative;
  width: 300px;
  height: 300px;
}

.donut-center-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.total-label {
  font-size: 1.2rem;
  opacity: 0.8;
}

.total-value {
  font-size: 2rem;
  font-weight: bold;
}

.year-selector {
  background-color: #2c2c2c !important;
  color: #fff !important;
  border: 1px solid #BED754 !important;
  min-width: 120px;
}

.chart-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.revenue-stats {
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #444;
}

.total-revenue-footer {
  background: rgba(190, 215, 84, 0.1);
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

/* DatePicker dark theme customization */
.react-datepicker {
  background-color: #343a40;
  border-color: #495057;
}

.react-datepicker__header {
  background-color: #212529;
  border-bottom-color: #495057;
}

.react-datepicker__current-month,
.react-datepicker__day-name,
.react-datepicker__day {
  color: #fff;
}

.react-datepicker__day:hover {
  background-color: #495057;
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background-color: #0d6efd;
}

.react-datepicker__day--disabled {
  color: #6c757d;
}

.react-datepicker__input-container input {
  background-color: #343a40;
  color: #fff;
  border-color: #495057;
}
