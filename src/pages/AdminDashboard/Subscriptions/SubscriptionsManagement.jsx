import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Form } from "react-bootstrap";
import { Doughn<PERSON>, Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
} from "chart.js";

import "./SubscriptionsManagement.css";
import SubscriptionsDataTable from "./SubscriptionsDataTable";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  getYearlySubscriptionDataApi,
  getAnalysisDataApi,
} from "../../../services/admin";

ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement
);

export default function SubscriptionDashboard() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonthIndex, setSelectedMonthIndex] = useState(0);
  const [yearlyData, setYearlyData] = useState({
    totalRevenue: 0,
    totalOrders: 0,
    months: [],
  });
  const [donutData, setDonutData] = useState({
    labels: [
      "Total Active Subscriptions",
      "Downgrade Subscriptions",
      "Expiring Within 7 Days",
      "Free Subscriptions",
      "Plus Subscriptions",
      "Custom Subscriptions",
    ],
    datasets: [
      {
        data: [0, 0, 0, 0, 0, 0],
        backgroundColor: [
          "#38B000",
          "#EF476F",
          "#FFD60A",
          "#4AC1E9",
          "#166534",
          "#F29E4C",
        ],
        borderWidth: 0,
      },
    ],
  });

  useEffect(() => {
    const fetchAnalysisData = async () => {
      try {
        const response = await getAnalysisDataApi();
        setDonutData((prevData) => ({
          ...prevData,
          datasets: [
            {
              ...prevData.datasets[0],
              data: [
                response.total_active,
                response.downgraded_users_count,
                response.subscriptions_ending_soon,
                response.free,
                response.plus,
                response.custom,
              ],
            },
          ],
        }));
      } catch (error) {
        console.error("Error fetching analysis data:", error);
      }
    };

    fetchAnalysisData();
  }, []);

  const donutOptions = {
    cutout: "70%",
    plugins: { legend: { display: false } },
  };

  // Calculate total for donut chart percentages
  const total = donutData.datasets[0].data.reduce((acc, curr) => acc + curr, 0);

  // Generate array of years (e.g., last 5 years)
  const years = Array.from(
    { length: 5 },
    (_, i) => new Date().getFullYear() - i
  );

  useEffect(() => {
    const fetchYearlyData = async () => {
      setIsLoading(true);
      try {
        const response = await getYearlySubscriptionDataApi(selectedYear);
        if (response.status === "success") {
          setYearlyData(response.data);
        }
      } catch (error) {
        console.error("Error fetching yearly data:", error);
        // Here you might want to show an error message to the user
      } finally {
        setIsLoading(false);
      }
    };

    fetchYearlyData();
  }, [selectedYear]);

  // Prepare chart data from the API response
  const barChartData = {
    labels: yearlyData.months.map((month) => month.shortMonth),
    datasets: [
      {
        label: "Revenue",
        data: yearlyData.months.map((month) => month.revenue / 1000), // Convert to K
        borderRadius: 4,
        barThickness: 24,
        backgroundColor: "#BED754",
        borderColor: "#BED754",
        borderWidth: 1,
      },
    ],
  };

  // Add this enhanced CSS override for better tooltip styling
  useEffect(() => {
    // Create a style element to override chart.js tooltip display
    const styleEl = document.createElement("style");
    styleEl.id = "chart-tooltip-override";
    styleEl.innerHTML = `
    /* Enhanced tooltip container */
    .chartjs-tooltip {
      min-width: 220px !important;
      max-width: 300px !important;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
      border: 1px solid #444 !important;
      padding: 0 !important;
      font-family: 'Cairo', -apple-system, BlinkMacSystemFont, sans-serif !important;
    }

    /* Header styling */
    .chartjs-tooltip-header {
      border-bottom: 1px solid #eee !important;
      background-color: #f8f9fa !important;
      margin: 0 !important;
    }

    .chartjs-tooltip-header-item th {
      font-weight: 600 !important;
      font-size: 14px !important;
      padding: 12px 16px !important;
      text-align: center !important;
    }

    /* Body container */
    .chartjs-tooltip-body {
      padding: 12px 0 !important;
    }

    /* Each tooltip item becomes a proper line */
    .chartjs-tooltip-body-item td {
      display: block !important;
      white-space: pre-wrap !important;
      padding: 6px 16px !important;
      line-height: 1.5 !important;
      font-size: 13px !important;
    }

    /* Section headers in the tooltip */
    .chartjs-tooltip-body-item td[data-section-header="true"] {
      font-weight: 600 !important;
      color: #444 !important;
      padding-top: 10px !important;
    }

    /* Section content items */
    .chartjs-tooltip-body-item td[data-section-content="true"] {
      padding-left: 24px !important;
      color: #555 !important;
    }

    /* Dividers between sections */
    .chartjs-tooltip-body-item td[data-divider="true"] {
      border-bottom: 1px solid #eee !important;
      margin: 6px 16px !important;
      padding: 0 !important;
      height: 1px !important;
    }

    /* Hide the color indicator dot */
    .chartjs-tooltip-body-item span {
      display: none !important;
    }
  `;
    document.head.appendChild(styleEl);

    // Clean up on component unmount
    return () => {
      const existingStyle = document.getElementById("chart-tooltip-override");
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);

  // Then modify your barOptions with a more structured tooltip approach
  const barOptions = {
    maintainAspectRatio: false,
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: "white",
        titleColor: "#333",
        bodyColor: "#333",
        borderColor: "#444444",
        borderWidth: 0, // We're handling border in CSS
        padding: 0, // We're handling padding in CSS
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          title: (tooltipItems) => {
            const monthIndex = tooltipItems[0].dataIndex;
            const monthData = yearlyData.months[monthIndex];
            return `${monthData.month} ${yearlyData.year}`;
          },
          label: (context) => {
            const monthIndex = context.dataIndex;
            const monthData = yearlyData.months[monthIndex];

            return (
              `Revenue: ${(monthData.revenue / 1000).toFixed(1)}k AED\n\n` +
              `Orders: ${monthData.orders}\n\n` +
              `Subscription Distribution:\n` +
              `  Free: ${monthData.subscriptions.free}\n` +
              `  Plus: ${monthData.subscriptions.plus}\n` +
              `  Custom: ${monthData.subscriptions.custom}\n\n` +
              `Share of Annual Revenue: ${(
                (monthData.revenue / yearlyData.totalRevenue) *
                100
              ).toFixed(1)}%`
            );
          },
          // Add these tooltip options to control spacing
          tooltip: {
            backgroundColor: "white",
            titleColor: "#333",
            bodyColor: "#333",
            bodySpacing: 8,
            padding: 12,
            callbacks: {
              // ... other callbacks ...
              labelTextColor: () => "#333",
              beforeLabel: () => "\n", // Add space before each label
              afterLabel: () => "\n", // Add space after each label
            },
          },
          // Use labelTextColor to ensure all lines use the same text color
          labelTextColor: () => "#333",
        },
      },
    },
    onHover: (event, elements) => {
      if (elements && elements.length > 0) {
        setSelectedMonthIndex(elements[0].index);
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: (v) => `${v}k AED`,
          color: "#ccc",
        },
        grid: { color: "#444" },
        // Add suggestedMax to create padding at the top
        suggestedMax: Math.max(...barChartData.datasets[0].data) * 1.1, // 10% padding
      },
      x: {
        ticks: { color: "#ccc" },
        grid: { color: "#444" },
      },
    },
  };

  const setupEnhancedTooltip = () => {
    // Create a custom tooltip element
    let tooltipEl = document.getElementById("enhanced-custom-tooltip");
    if (!tooltipEl) {
      tooltipEl = document.createElement("div");
      tooltipEl.id = "enhanced-custom-tooltip";
      tooltipEl.style.opacity = "0";
      tooltipEl.style.position = "absolute";
      tooltipEl.style.backgroundColor = "white";
      tooltipEl.style.borderRadius = "8px";
      tooltipEl.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
      tooltipEl.style.border = "1px solid #444";
      tooltipEl.style.pointerEvents = "none";
      tooltipEl.style.zIndex = "10000";
      tooltipEl.style.minWidth = "220px";
      tooltipEl.style.maxWidth = "300px";
      tooltipEl.style.fontFamily =
        "'Inter', -apple-system, BlinkMacSystemFont, sans-serif";
      tooltipEl.style.fontSize = "13px";
      tooltipEl.style.transition = "opacity 0.15s ease";
      document.body.appendChild(tooltipEl);
    }

    setTimeout(() => {
      const chartElement = document.querySelector(".sales-bar-chart canvas");
      if (!chartElement) return;

      // Mouse events to show/hide tooltip
      chartElement.addEventListener("mousemove", (e) => {
        const rect = chartElement.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Get chart instance
        const chart = chartElement.chart || chartElement.__chartjs__;
        if (!chart) return;

        const elements = chart.getElementsAtEventForMode(
          e,
          "nearest",
          { intersect: true },
          false
        );

        if (elements && elements.length > 0) {
          const dataIndex = elements[0].index;
          const monthData = yearlyData.months[dataIndex];

          // Position tooltip
          tooltipEl.style.opacity = "1";
          tooltipEl.style.left = `${e.clientX}px`;
          tooltipEl.style.top = `${e.clientY + 10}px`;
          tooltipEl.style.transform = "translate(-50%, 0)";

          // Format tooltip with nice HTML structure
          tooltipEl.innerHTML = `
          <div style="padding: 12px 16px; text-align: center; border-bottom: 1px solid #eee; background-color: #f8f9fa; font-weight: 600; font-size: 14px;">
            ${monthData.month} ${yearlyData.year}
          </div>
          <div style="padding: 12px 16px;">
            <div style="font-weight: 600; margin-bottom: 4px;">
              Revenue: ${(monthData.revenue / 1000).toFixed(1)}k AED
            </div>
            <div style="margin-left: 8px; color: #555; margin-bottom: 12px;">
              Orders: ${monthData.orders}
            </div>

            <div style="height: 1px; background-color: #eee; margin: 8px 0;"></div>

            <div style="font-weight: 600; margin-bottom: 4px;">
              Subscription Distribution:
            </div>
            <div style="margin-left: 8px; color: #555;">
              Free: ${monthData.subscriptions.free}
            </div>
            <div style="margin-left: 8px; color: #555;">
              Plus: ${monthData.subscriptions.plus}
            </div>
            <div style="margin-left: 8px; color: #555; margin-bottom: 12px;">
              Custom: ${monthData.subscriptions.custom}
            </div>

            <div style="height: 1px; background-color: #eee; margin: 8px 0;"></div>

            <div style="font-weight: 600;">
              Share of Annual Revenue: ${(
                (monthData.revenue / yearlyData.totalRevenue) *
                100
              ).toFixed(1)}%
            </div>
          </div>
        `;
        }
      });

      chartElement.addEventListener("mouseout", () => {
        tooltipEl.style.opacity = "0";
      });
    }, 500); // Give the chart a moment to initialize
  };

  // Call this function after your component mounts
  useEffect(() => {
    if (!isLoading && yearlyData.months.length > 0) {
      setupEnhancedTooltip();
    }
  }, [isLoading, yearlyData]);

  const handleSubscriptionCardClick = () => {
    navigate("/admin/subscriptions/management/details");
  };

  return (
    <Container fluid className="py-4 admin-theme text-light min-vh-100 px-5">
      {/* Subscriptions Table */}
      <SubscriptionsDataTable />

      {/* Analysis Cards */}
      <Row>
        <Col xs={12} className="mb-5">
          <Card
            bg="dark"
            text="light"
            className="h-100 border-success py-3 subscription-card"
          >
            <Card.Body>
              <Card.Title className="text-center mb-5">
                Analysis for all subscriptions
              </Card.Title>
              <Row className="px-5">
                <Col style={{ cursor: "pointer" }} md={6}>
                  <ul
                    className="list-unstyled d-flex flex-column justify-content-between h-100"
                    style={{ fontSize: "1.2rem" }}
                    onClick={handleSubscriptionCardClick}
                  >
                    <li>
                      <span style={{ color: "#38B000", fontSize: "1.4rem" }}>
                        ●
                      </span>{" "}
                      Total Active Subscriptions
                    </li>
                    <li>
                      <span style={{ color: "#EF476F", fontSize: "1.4rem" }}>
                        ●
                      </span>{" "}
                      Number of Downgrade Subscriptions
                    </li>
                    <li>
                      <span style={{ color: "#FFD60A", fontSize: "1.4rem" }}>
                        ●
                      </span>{" "}
                      Subscriptions Expiring Within 7 Days
                    </li>
                    <li>
                      <span style={{ color: "#4AC1E9", fontSize: "1.4rem" }}>
                        ●
                      </span>{" "}
                      Free Subscriptions
                    </li>
                    <li>
                      <span style={{ color: "#166534", fontSize: "1.4rem" }}>
                        ●
                      </span>{" "}
                      Plus Subscriptions
                    </li>
                    <li>
                      <span style={{ color: "#F29E4C", fontSize: "1.4rem" }}>
                        ●
                      </span>{" "}
                      Custom Subscriptions
                    </li>
                  </ul>
                </Col>
                <Col md={6} className="d-flex justify-content-end">
                  <div
                    style={{ position: "relative", width: 300, height: 300 }}
                  >
                    <Doughnut
                      data={donutData}
                      onClick={handleSubscriptionCardClick}
                      options={{
                        ...donutOptions,
                        plugins: {
                          ...donutOptions.plugins,
                          tooltip: {
                            enabled: false,
                          },
                        },
                        onHover: (event, chartElements) => {
                          const tooltipEl =
                            document.getElementById("custom-tooltip");
                          if (!tooltipEl) return;

                          // Calculate total subscriptions (free + plus + custom)
                          const totalSubscriptions =
                            donutData.datasets[0].data[3] +
                            donutData.datasets[0].data[4] +
                            donutData.datasets[0].data[5];

                          if (chartElements.length === 0) {
                            // Return to default view (total active subscriptions)
                            tooltipEl.innerHTML = `
                              <div style="font-size: 24px; font-weight: bold; color: #38B000">
                                ${donutData.labels[0]}
                              </div>
                              <div style="opacity: 0.8; font-size: 1rem; color: #38B000">
                                ${totalSubscriptions} ${
                              totalSubscriptions > 0
                                ? `(${Math.round(
                                    (totalSubscriptions / totalSubscriptions) *
                                      100
                                  )}%)`
                                : "(0%)"
                            }
                              </div>
                            `;
                          } else {
                            // Show hovered section data
                            const index = chartElements[0].index;
                            const colors = [
                              "#38B000",
                              "#EF476F",
                              "#FFD60A",
                              "#4AC1E9",
                              "#166534",
                              "#F29E4C",
                            ];
                            tooltipEl.innerHTML = `
                              <div style="font-size: 24px; font-weight: bold; color: ${
                                colors[index]
                              }">
                                ${donutData.labels[index]}
                              </div>
                              <div style="opacity: 0.8; font-size: 1rem; color: ${
                                colors[index]
                              }">
                                ${donutData.datasets[0].data[index]} ${
                              totalSubscriptions > 0
                                ? `(${Math.round(
                                    (donutData.datasets[0].data[index] /
                                      totalSubscriptions) *
                                      100
                                  )}%)`
                                : "(0%)"
                            }
                              </div>
                            `;
                          }
                        },
                      }}
                      style={{ width: "100%", height: "100%" }}
                    />
                    {/* Initial render of the tooltip */}
                    {(() => {
                      const totalSubscriptions =
                        donutData.datasets[0].data[3] +
                        donutData.datasets[0].data[4] +
                        donutData.datasets[0].data[5];
                      return (
                        <div
                          id="custom-tooltip"
                          style={{
                            position: "absolute",
                            top: "50%",
                            left: "50%",
                            transform: "translate(-50%, -50%)",
                            textAlign: "center",
                            pointerEvents: "none",
                            userSelect: "none",
                          }}
                        >
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: "bold",
                              color: "#38B000",
                            }}
                          >
                            {donutData.labels[0]}
                          </div>
                          <div
                            style={{
                              opacity: 0.8,
                              fontSize: "1rem",
                              color: "#38B000",
                            }}
                          >
                            {totalSubscriptions}{" "}
                            {totalSubscriptions > 0
                              ? `(${Math.round(
                                  (totalSubscriptions / totalSubscriptions) *
                                    100
                                )}%)`
                              : "(0%)"}
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                </Col>
              </Row>
              <center>
                <Button
                  variant="outline-light"
                  className="mt-3"
                  onClick={handleSubscriptionCardClick}
                >
                  View Details
                </Button>
              </center>
            </Card.Body>
          </Card>
        </Col>

        <Col xs={12} className="mb-4">
          <Card bg="dark" text="light" className="h-100 border-success py-3">
            <Card.Body>
              <div className="d-flex justify-content-between align-items-center mb-4">
                <div>
                  <Card.Title className="mb-0">Revenue Analysis</Card.Title>
                  {!isLoading && yearlyData.months.length > 0 && (
                    <div className="text-white">
                      <div>
                        Monthly Revenue:{" "}
                        {yearlyData.months[
                          selectedMonthIndex
                        ]?.revenue.toLocaleString()}{" "}
                        AED
                      </div>
                      <div>
                        Monthly Orders:{" "}
                        {yearlyData.months[selectedMonthIndex]?.orders}
                      </div>
                      <div>
                        Total Revenue:{" "}
                        {yearlyData.totalRevenue.toLocaleString()} AED | Total
                        Orders: {yearlyData.totalOrders.toLocaleString()}
                      </div>
                    </div>
                  )}
                </div>
                <Form.Select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(Number(e.target.value))}
                  style={{
                    width: "auto",
                    backgroundColor: "#2c2c2c",
                    color: "#fff",
                    border: "1px solid #BED754",
                  }}
                  disabled={isLoading}
                >
                  {years.map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </Form.Select>
              </div>
              <div className="chart-container" style={{ minHeight: "400px" }}>
                {isLoading ? (
                  <div className="d-flex justify-content-center align-items-center h-100">
                    <div className="spinner-border text-light" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                ) : (
                  <Bar
                    options={barOptions}
                    className="sales-bar-chart"
                    data={{
                      ...barChartData,
                      datasets: [
                        {
                          ...barChartData.datasets[0],
                          backgroundColor: barChartData.datasets[0].data.map(
                            (value) => {
                              const maxValue = Math.max(
                                ...barChartData.datasets[0].data
                              );
                              const percentage = (value / maxValue) * 100;

                              if (percentage >= 80) return "#38B000";
                              if (percentage >= 60) return "#70B533";
                              if (percentage >= 40) return "#FF7F50";
                              return "#FF4D4D";
                            }
                          ),
                          borderRadius: 4,
                          barThickness: 40,
                        },
                      ],
                    }}
                    style={{ height: "440px", minWidth: "100%" }}
                  />
                )}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
}
