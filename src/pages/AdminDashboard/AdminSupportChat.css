.admin-support-chat-page {
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
}

.chat-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1rem;
  display: flex;
  align-items: center;
  height: 60px;
}

.chat-header h2 {
  margin: 0 auto;
  font-size: 1.5rem;
}

.back-button {
  color: #6c757d;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.back-button:hover {
  color: #343a40;
}

.chat-content-container {
  flex: 1;
  overflow: hidden;
}

/* RTL support */
.flip-rtl {
  transform: scaleX(-1);
}

[dir="rtl"] .ms-2 {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

@media (max-width: 768px) {
  .chat-header h2 {
    font-size: 1.25rem;
  }
}
