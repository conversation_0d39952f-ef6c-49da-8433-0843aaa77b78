import React, { useEffect, useMemo, useState } from "react";
import adminDashboardService from "../../services/admin/admin-dashboard-services";
import { useTranslation } from "react-i18next";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import AdminPaginationComponent from "../../components/AdminPagination/AdminPaginationComponent";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import { format } from "date-fns";
import { Card, Form, InputGroup, Row, Col } from "react-bootstrap";
import { BsSearch } from "react-icons/bs";
import "./NewClientsDataTable.page.css";
import "../../components/ClientsTabsContent/AllClients/Clients.css";
import { FaMagnifyingGlass } from "react-icons/fa6";

export default function NewLeadsDataTable() {
  const [loading, setLoading] = useState(false);
  const [leads, setLeads] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const { t } = useTranslation();

  useEffect(() => {
    const fetchLeads = async () => {
      setLoading(true);
      try {
        const response = await adminDashboardService.getAdminNewLeads({
          current_page: currentPage,
          per_page: recordsPerPage,
        });
        const data = response?.data?.["All Leads"] || [];
        setLeads(data);
        setTotalPages(response?.data?.["Number Of Pages"] || 1);
      } catch (error) {
        setLeads([]);
        setTotalPages(0);
      } finally {
        setLoading(false);
      }
    };
    fetchLeads();
  }, [currentPage, recordsPerPage]);

  // Map backend data to table data structure (include fields used by shared columns)
  const mappedData = useMemo(() => {
    return (Array.isArray(leads) ? leads : []).map((lead, idx) => ({
      leadId: idx + 1, // for index column
      id: lead?.id,
      client_id: lead?.client_id,
      client_name: lead?.client_name || "-",
      name: lead?.name || "-",
      email: lead?.email || "-",
      phone: lead?.phone || "-",
      source: lead?.source,
      create: { name: lead?.created_by || "-" },
      status: lead?.status,
      createdAt: lead?.created_at || "-",
      createdBy: lead?.created_by || "-",
    }));
  }, [leads]);

  // Filter data based on search term
  const data = useMemo(() => {
    let filteredData = mappedData;

    // Filter by search term
    if (searchTerm.trim()) {
      const lowercasedSearch = searchTerm.toLowerCase();
      filteredData = filteredData.filter((lead) => {
        return (
          lead.name.toLowerCase().includes(lowercasedSearch) ||
          lead.email.toLowerCase().includes(lowercasedSearch) ||
          lead.phone.toLowerCase().includes(lowercasedSearch) ||
          lead.client_name.toLowerCase().includes(lowercasedSearch) ||
          (lead.createdBy &&
            lead.createdBy.toLowerCase().includes(lowercasedSearch))
        );
      });
    }

    return filteredData;
  }, [mappedData, searchTerm]);

  // Shared columns from ColumnsForTables.module
  const { newLeadsTodayColumns } = useTranslatedColumns();

  const columns = newLeadsTodayColumns;

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  return (
    <Card bg="dark" text="light" className="mb-5 border-success">
      <Card.Header className="text-center fs-5">
        {t("leadsTable.newLeadsTitle") || "New Leads Today"}
      </Card.Header>
      <Card.Body>
        {loading ? (
          <FetchingDataLoading className="my-5" />
        ) : (
          <>
            <Row className="mb-3">
              <Col>
                <div
                  className="filter-container p-3"
                  style={{
                    backgroundColor: "#2c3033",
                    borderRadius: "8px",
                    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.15)",
                  }}
                >
                  <Row className="justify-content-end align-items-center">
                    <Col lg={4} md={4} sm={12}>
                      <Form.Group className="position-relative">
                        <Form.Control
                          placeholder={`${t(
                            "tableControls.placeholders.searchTable"
                          )} ${mappedData.length} ${t(
                            "tableControls.placeholders.records"
                          )}...`}
                          value={searchTerm}
                          onChange={handleSearchChange}
                          className="rounded-pill"
                        />
                        <FaMagnifyingGlass
                          className="text-muted position-absolute"
                          style={{
                            right: "10px",
                            top: "50%",
                            transform: "translateY(-50%)",
                          }}
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>

            <div className="admin-theme">
              <DataTableComponent
                columns={columns}
                data={data}
                loading={loading}
                initialSortBy={[]}
                hiddenColumns={[]}
              />
            </div>
            <AdminPaginationComponent
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={recordsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setRecordsPerPage}
              itemsPerPageOptions={[10, 20, 30, 40, 50]}
            />
          </>
        )}
      </Card.Body>
    </Card>
  );
}
