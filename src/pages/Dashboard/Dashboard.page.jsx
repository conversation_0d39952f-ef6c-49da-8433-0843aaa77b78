import "./dashboard.css";
import MeetingCalendarComponent from "../../components/DashboardComponents/MeetingCalendar/MeetingCalendar.component";
import StatisticsComponent from "../../components/DashboardComponents/Statistics/Statistics.component";
import ChartsComponent from "../../components/DashboardComponents/Charts/Charts.component";
import { useSelector, useDispatch } from "react-redux";
import useAuth from "../../redux/hooks/useAuth";
import { Link } from "react-router-dom"; // Added missing import for Link
import Button from "react-bootstrap/Button";
import { safeNumber } from "../../utils/safe-number";
import getAllInvoicesSubscriptionsApi from "../../services/package/get-subscription-invoices.api";
import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";
import FacebookTokenExpiredModal from "../../components/ClientsTabsContent/AllClients/FacebookTokenExpiredModal";
import {
  showModal,
  handleModalClose,
} from "../../redux/features/facebookTokenModalSlice";

const DashboardPage = () => {
  const dispatch = useDispatch();
  const { user, setUser } = useAuth();
  const { showTokenModal } = useSelector((state) => state.facebookTokenModal);
  const packageStatus =
    (user?.user?.package_id !== null && user?.user?.package_id !== undefined) ||
    user?.user?.parent_id !== null;
  useEffect(() => {
    if (
      user?.user?.access_token_status === false &&
      user?.user["access-token"]
    ) {
      dispatch(showModal());
    }
  }, [user, dispatch]);

  useEffect(() => {
    const fetchSubDetails = async () => {
      try {
        // Skip package API calls for excluded users
        if (!shouldShowPackageFeatures(user?.user?.id)) {
          return;
        }

        const userId = user?.user?.id;
        const response = await getAllInvoicesSubscriptionsApi(userId);
        if (response?.data?.curunt || response?.status === 200) {
          setUser({
            ...user,
            user: {
              ...user.user,
              package_id: response?.data?.curunt?.package?.id,
              package_name: response?.data?.curunt?.package?.title,
              package_price: response?.data?.curunt?.package?.price,
            },
          });
          const userCookie = Cookies.get("userData");
          if (userCookie) {
            const parsedUserData = JSON.parse(userCookie);
            const updatedUserData = {
              ...parsedUserData,
              user: {
                ...parsedUserData.user,
                package_id: response?.data?.curunt?.package?.id,
                package_name: response?.data?.curunt?.package?.title,
                package_price: response?.data?.curunt?.package?.price,
              },
            };
            Cookies.set("userData", JSON.stringify(updatedUserData));
          }
        }
      } catch (error) {
        console.error("Error fetching user subscription details:", error);
      }
    };

    fetchSubDetails();
  }, []);

  return (
    <div className={"px-3 px-sm-0"}>
      {user && packageStatus ? (
        <>
          <MeetingCalendarComponent />
          {safeNumber(user?.user?.role) === 0 ||
          safeNumber(user?.user?.role === 1) ? (
            <>
              <StatisticsComponent />
              <ChartsComponent />
            </>
          ) : null}
        </>
      ) : (
        <center className="d-flex flex-column justify-content-center align-items-center content-container">
          <h1>Please login to view the dashboard</h1>
          <Button as={Link} to="/client/login">
            Login
          </Button>
        </center>
      )}

      <CenteredModal
        show={showTokenModal}
        onHide={() => dispatch(handleModalClose())}
      >
        <FacebookTokenExpiredModal
          onHide={() => dispatch(handleModalClose())}
        />
      </CenteredModal>
    </div>
  );
};

export default DashboardPage;
