.account-settings .user-profile-picture {
    border-radius: 50%;
    border: 2px solid #000000;
    box-shadow: 0 4px 45px 16px rgba(255, 255, 255, 0.20);
    width: 180px;
    height: 180px;
    margin-bottom: 1rem;
}

.user-profile-picture {
    border-radius: 50%;
    border: 2px solid #FFF;
    box-shadow: 0 4px 45px 16px rgba(255, 255, 255, 0.20);
    width: 125px;
    height: 125px;
    margin: 0 auto;
}

.account-settings .user-profile-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.account-settings .profile-pic-settings-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.account-settings .profile-pic-edit {
    position: absolute;
    right: 1%;
    bottom: 0;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    color: #000;
    background-color: #FFFFFF;
    border: none;
}

.profile-pic-edit {
    position: absolute;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    color: #000;
    background-color: #FFFFFF;
    border: none;
}

.account-settings .profile-pic-edit:after{
    display: none;
}

.profile-pic-edit:after{
    display: none;
}

.account-settings .profile-pic-edit:hover, .profile-pic-edit:focus-within {
    background-color: #9DC41D;
}

.profile-pic-edit:hover, .profile-pic-edit:focus-within {
    background-color: #9DC41D;
}

.contact-info-container {
    border-radius: 30px;
    background: #F1F1F1;
    box-shadow: 0 4px 45px 0 rgba(0, 0, 0, 0.15);
}

.password-rules-container {
    border-radius: 8px;
    background: #FFF;
    /*padding: 23px 21px;*/
    margin-top: 30px;
}

.dropItem-profile-pic {
    padding: 0 1rem;
    cursor: pointer;
}

.dropItem-profile-pic:hover {
    background-color: #EBEBEB;
}

.lead-profile .user-profile-picture {
    border: 2px solid #000000;
    background-color: #5B5B5B;
}

.edit-icon button {
    background-color: #9DC41D;
    border: unset;
    border-radius: 50%;
}

.edit-icon button:hover, .edit-icon button:focus, .edit-icon .btn:first-child:active, .edit-icon .btn:focus-visible, .edit-icon .btn.show {
    box-shadow: unset;
    background-color: #9DC41D;
}

.edit-icon button:after {
    content: unset;
}

.input-with-icon {
  position: relative;
}

.input-icon-container {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #00000033;
  z-index: 2;
}

.input-icon-rtl {
  left: auto;
  right: 10px;
}

.password-input-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #00000033;
  z-index: 2;
}

.password-icon-rtl {
  right: auto;
  left: 10px;
}

.password-toggle-icon {
  opacity: 1;
  transition: opacity 0.2s;
}

.password-toggle-icon:hover {
  opacity: 1;
}

.password-input-container .form-control.is-invalid {
  padding-right: calc(1.5em + .75rem + 35px) !important;
}

.password-input-container .form-control.is-invalid ~ .password-input-icon {
  transform: translateY(-50%);
}

[dir="rtl"] .password-input-container .form-control.is-invalid {
  padding-left: calc(1.5em + .75rem + 35px) !important;
  padding-right: 40px !important;
}

/* Add these new styles to fix icon positioning */
.input-icon-container {
  position: relative;
  margin-bottom: 24px; /* Add space for error message */
}

.password-input-container .form-control.is-invalid ~ .password-input-icon,
.password-input-container .form-control.is-valid ~ .password-input-icon {
  top: 50%;
}

.input-icon-container .invalid-feedback,
.input-icon-container .valid-feedback {
  position: absolute;
  bottom: -20px;
  left: 0;
  margin-top: 0;
}

[dir="rtl"] .input-icon-container .invalid-feedback,
[dir="rtl"] .input-icon-container .valid-feedback {
  left: auto;
  right: 0;
}
