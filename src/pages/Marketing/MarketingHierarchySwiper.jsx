import {Swiper, SwiperSlide} from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import {Navigation, Pagination} from "swiper/modules";
import {useState} from "react";
import {Col, Row} from "react-bootstrap";
import {FaDollarSign} from "react-icons/fa";
import {MdAdsClick} from "react-icons/md";
import {PiTargetBold} from "react-icons/pi";
import {FaSackDollar} from "react-icons/fa6";
import {GoDotFill} from "react-icons/go";
import {numberWithCommas} from "../../utils/number-with-commas";
import {useTranslation} from "react-i18next";

const MarketingHierarchySwiper = ({data}) => {
    const { t } = useTranslation();
    const platforms = data || [];

    const [selectedPlatformIndex, setSelectedPlatformIndex] = useState(0);
    const [selectedPageIndex, setSelectedPageIndex] = useState(0);
    const [selectedCampaignIndex, setSelectedCampaignIndex] = useState(0);
    const [selectedAdSetIndex, setSelectedAdSetIndex] = useState(0);

    const selectedPlatform = platforms[selectedPlatformIndex] || {};
    const selectedPage = selectedPlatform?.children?.[selectedPageIndex] || {};
    const selectedCampaign = selectedPage?.children?.[selectedCampaignIndex] || {};
    const selectedAdSet = selectedCampaign?.children?.[selectedAdSetIndex] || {};
    const ads = selectedAdSet?.children || [];

    // Aggregating root metrics
    const rootMetrics = platforms.reduce((acc, platform) => {
        const {clicks = 0, reach = 0, cpm = 0, spend = 0} = platform.metrics || {};
        acc.clicks += Number(clicks);
        acc.reach += Number(reach);
        acc.cpm += Number(cpm);
        acc.spend += Number(spend);
        return acc;
    }, {clicks: 0, reach: 0, cpm: 0, spend: 0});

    return (<div className="my-4">
        <Row className={"justify-content-center"}>
            <Col lg={6} className="card-container">
                <div className="text-center mb-4 fw-bold fs-5">{t("marketing.performanceTitle", "Marketing Performance")}</div>
                <Row
                    className="column-gap-0 column-gap-lg-4 row-gap-3 row-gap-sm-5 justify-content-center align-content-center">
                    {/* Total Spend */}
                    <Col xs={12} sm={6} md={6} lg={5}
                         className="d-flex align-items-center justify-content-center justify-content-sm-end">
                        <div
                            className="mainColorBgLight rounded-2 p-2 me-3"
                            style={{width: "fit-content"}}
                        >
                            <FaDollarSign
                                className="mainColorLight p-1 bg-white rounded-circle"
                                size={30}
                            />
                        </div>
                        <div>
                            <span>{t("marketing.totalSpend")}</span>
                            <strong className="d-block">${numberWithCommas(rootMetrics.spend.toFixed(2))}</strong>
                        </div>
                    </Col>
                    {/* Total Clicks */}
                    <Col xs={12} sm={6} md={6} lg={5}
                         className="d-flex align-items-center justify-content-center justify-content-sm-start">
                        <div
                            className="mainColorBgLight rounded-2 p-2 me-3"
                            style={{width: "fit-content"}}
                        >
                            <MdAdsClick
                                color={"white"}
                                size={30}
                            />
                        </div>
                        <div>
                            <span>{t("marketing.totalClicks")}</span>
                            <strong className="d-block">{numberWithCommas(rootMetrics.clicks)}</strong>
                        </div>
                    </Col>
                    {/* Total Reach */}
                    <Col xs={12} sm={6} md={6} lg={5}
                         className="d-flex align-items-center justify-content-center justify-content-sm-end">
                        <div
                            className="mainColorBgLight rounded-2 p-2 me-3"
                            style={{width: "fit-content"}}
                        >
                            <PiTargetBold
                                color={"white"}
                                size={30}
                            />
                        </div>
                        <div>
                            <span>{t("marketing.totalReach")}</span>
                            <strong className="d-block">{numberWithCommas(rootMetrics.reach)}</strong>
                        </div>
                    </Col>
                    {/* Average CPM */}
                    <Col xs={12} sm={6} md={6} lg={5}
                         className="d-flex align-items-center justify-content-center justify-content-sm-start">
                        <div
                            className="mainColorBgLight rounded-2 p-2 me-3"
                            style={{width: "fit-content"}}
                        >
                            <FaSackDollar
                                className="mainColorBgLight"
                                color={"white"}
                                size={30}
                            />
                        </div>
                        <div>
                            <span>{t("marketing.averageCPM")}</span>
                            <strong className="d-block">
                                {platforms.length > 0 ? numberWithCommas((rootMetrics.cpm / platforms.length).toFixed(2)) : "N/A"}
                            </strong>
                        </div>
                    </Col>
                </Row>
            </Col>
                </Row>
        {/* Social Platforms */}
<Row className={"justify-content-center column-gap-5"}>
    <Col lg={6}>
        <HierarchySlider
            textAlign={"text-center"}
                        label={t("marketing.socialPlatforms")}
                        data={platforms}
                        onSlideChange={(index) => {
                            setSelectedPlatformIndex(index);
                            setSelectedPageIndex(0);
                            setSelectedCampaignIndex(0);
                            setSelectedAdSetIndex(0);
                        }}
                    />
    </Col>
</Row>
            <Row>
                {/* Pages */}
                <Col lg={6}>
                    <HierarchySlider
                        textAlign={"text-start"}
                        label={t("marketing.pages")}
                        data={selectedPlatform.children}
                        onSlideChange={(index) => {
                            setSelectedPageIndex(index);
                            setSelectedCampaignIndex(0);
                            setSelectedAdSetIndex(0);
                        }}
                    />
                </Col>
                {/* Campaigns */}
                <Col lg={6}>
                   <HierarchySlider
                        textAlign={"text-start"}
                            label={t("marketing.campaigns")}
                            data={selectedPage.children}
                            onSlideChange={(index) => {
                                setSelectedCampaignIndex(index);
                                setSelectedAdSetIndex(0);
                            }}
                        />
                </Col>

            </Row>
            <Row>
                {/* Ad Sets */}
                <Col lg={6}>
                    <HierarchySlider
                        textAlign={"text-start"}
                            label={t("marketing.adSets")}
                            data={selectedCampaign.children}
                            onSlideChange={(index) => setSelectedAdSetIndex(index)}
                        />
                </Col>
                {/* Ads */}
                <Col lg={6}>
               <HierarchySlider textAlign={"text-start"} label={t("marketing.ads")} data={ads}/>
                </Col>
            </Row>
        </div>);
};

const HierarchySlider = ({label, data, onSlideChange, textAlign}) => {
    const { t } = useTranslation();

    return (<div className="slider-container">
            <div className={`${textAlign} mb-2 fw-bold fs-6`}>{label}</div>
            <Swiper
                spaceBetween={10}
                slidesPerView={1}
                pagination={{clickable: true}}
                navigation
                modules={[Pagination, Navigation]}
                onSlideChange={(swiper) => onSlideChange?.(swiper.activeIndex)}
                className="marketingSwiper"
            >
                {data?.length > 0 ? (data.map((item, index) => (
                    <SwiperSlide key={index}>
                            <div className="slider-item">
                            <div className="d-flex justify-content-between">
                            <div className={`mb-2 ${textAlign} fw-bold fs-6`}>{item.name}</div>
                            {item.status ?
                                        <div className={"fw-bold d-flex justify-content-between align-items-center"}>
                                            {item.status === "ACTIVE" ? <GoDotFill size={25} className={"mainColor"} /> : <GoDotFill size={25} color={"red"} />}
                                            {item.status}</div>
                                : null}
                            </div>
                                <div className={"d-flex justify-content-between"}>
                                    {item.date_start ? (
                                        <p>
                                            <span className="fw-bold">{item.date_start} to {item.date_stop}</span>
                                        </p>
                                    ): null}
                                </div>

                                {item.metrics && (<Row
                                    className="column-gap-0 column-gap-lg-4 row-gap-3 row-gap-sm-5 justify-content-center align-content-center">
                                        {/* Spend */}
                                        <Col xs={12} sm={6} md={6} lg={5}
                                             className="d-flex align-items-center justify-content-center justify-content-sm-end">
                                            <div
                                                className="mainColorBgLight rounded-2 p-2 me-3"
                                                style={{width: "fit-content"}}
                                            >
                                                <FaDollarSign
                                                    className="mainColorLight p-1 bg-white rounded-circle"
                                                    size={30}
                                                />
                                            </div>
                                            <div className={"text-start"}>
                                                <span>{t("marketing.spend")}</span>
                                                <strong className="d-block">${numberWithCommas(item.metrics.spend) || 0}</strong>
                                            </div>
                                        </Col>

                                        {/* Clicks */}
                                        <Col xs={12} sm={6} md={6} lg={5}
                                             className="d-flex align-items-center justify-content-center justify-content-sm-start">
                                            <div
                                                className="mainColorBgLight rounded-2 p-2 me-3"
                                                style={{width: "fit-content"}}
                                            >
                                                <MdAdsClick
                                                    color={"white"}
                                                    size={30}
                                                />
                                            </div>
                                            <div className={"text-start"}>
                                                <span>{t("marketing.clicks")}</span>
                                                <strong className="d-block">{numberWithCommas(item.metrics.clicks) || 0}</strong>
                                            </div>
                                        </Col>

                                        {/* Reach */}
                                        <Col xs={12} sm={6} md={6} lg={5}
                                             className="pe-4 d-flex align-items-center justify-content-center justify-content-sm-end">
                                            <div
                                                className="mainColorBgLight rounded-2 p-2 me-3"
                                                style={{width: "fit-content"}}
                                            >
                                                <PiTargetBold
                                                    color={"white"}
                                                    size={30}
                                                />
                                            </div>
                                            <div className={"text-start"}>
                                                <span>{t("marketing.reach")}</span>
                                                <strong className="d-block">{numberWithCommas(item.metrics.reach) || 0}</strong>
                                            </div>
                                        </Col>

                                        {/* CPM */}
                                        <Col xs={12} sm={6} md={6} lg={5}
                                             className="d-flex align-items-center justify-content-center justify-content-sm-start">
                                            <div
                                                className="mainColorBgLight rounded-2 p-2 me-3"
                                                style={{width: "fit-content"}}
                                            >
                                                <FaSackDollar
                                                    className="mainColorBgLight"
                                                    color={"white"}
                                                    size={30}
                                                />
                                            </div>
                                            <div className={"text-start"}>
                                                <span>{t("marketing.cpm")}</span>
                                                <strong className="d-block">
                                                    {numberWithCommas(item.metrics.cpm) || "N/A"}
                                                </strong>
                                            </div>
                                        </Col>
                                    </Row>)}
                            </div>
                        </SwiperSlide>))) : (<SwiperSlide>
                        <div className="no-data">
                            <h4>{t("marketing.noData")}</h4>
                        </div>
                    </SwiperSlide>)}
            </Swiper>
        </div>);
};

export default MarketingHierarchySwiper;
