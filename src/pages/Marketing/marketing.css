/* icicleChart.css */

/* General Styles */
.icicle-chart {
    max-width: 100%;
    height: auto;
    font-family: sans-serif;
}

/* Rectangles (bars) in the Icicle chart */
.icicle-chart .icicle-rect {
    cursor: pointer;
    fill-opacity: 0.6;
}

/* Text in the Icicle chart */
.icicle-chart text {
    user-select: none;
    pointer-events: none;
    font-size: 12px;
}

/* Bold Name Text */
.icicle-chart .icicle-name {
    font-weight: bold;
}

.icicle-chart .icicle-rect {
    stroke: #fff;
}

.icicle-text {
    font-size: 1.5rem;
}

.icicle-name {
    font-size: 1.5rem;
    font-weight: bold;
}

.icicle-total-leads, .icicle-completed-leads {
    font-size: 1rem;

}

.icicle-text-content {
    font-size: 1rem;
    text-align: left;
    color: #fff;
}

.icicle-text-content ul {
    padding-left: 1.5rem;  /* Add padding for bullets */
    margin: 0;
    list-style-type: disc;  /* Bullet points */
}

.icicle-text-content li {
    margin-top: 0.5rem;
}


.slider-container {
    border-radius: 30px;
    padding: 2rem 1rem;
    margin: 20px 0;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.15);
    background: linear-gradient(134.41deg, rgba(224, 232, 221, 0.7) 0.794%,rgba(152, 154, 151, 0.7) 98.344%);
}

.slider-item {
    transition: all 0.3s ease;
}

.slider-item:hover {
    border-color: #007bff;
}

.slider-item.selected {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.card-container {
    border-radius: 30px;
    background: linear-gradient(134.41deg, rgba(224, 232, 221, 0.7) 0.794%,rgba(152, 154, 151, 0.7) 98.344%);
    min-height: 350px;
    padding: 2rem 0;
    margin: 0 auto;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
}

.custom-nav-btn {
    background: none;
    border: none;
    color: #333;
    cursor: pointer;
    margin: 0 10px;
}

.custom-nav-btn:hover {
    color: #007bff;
}

.marketingSwiper .swiper-button-next::after{
    color: white;
    background-color: #92C020;
    border-radius: 50%;
    padding: 1rem;
    font-size: 0.8rem;
    font-weight: 900;
}

.marketingSwiper .swiper-button-prev::after{
    color: white;
    background-color: #92C020;
    border-radius: 50%;
    padding: 1rem;
    font-size: 0.8rem;
    font-weight: 900;
}

.marketingSwiper .swiper-pagination-bullets.swiper-pagination-horizontal {
    bottom: 0;
}

.marketingSwiper .swiper-wrapper {
    min-height: 250px;
}

.marketingSwiper .swiper-pagination-bullet-active {
    padding: 0 10px;
    border-radius: 20%;
    background: linear-gradient(90.00deg, rgb(202, 213, 17),rgb(146, 192, 32) 99.997%);
    transition: all 0.3s ease;
}
