import {ReactSVG} from "react-svg";
import imgUnAuthorized from "../../assets/media/Icons/UnAuthorizedBG.svg";
import {Button} from "react-bootstrap";
import {Link} from "react-router-dom";
import NotFoundBG from "../../assets/media/Icons/NotFoundBG.svg";
import { useSelector } from 'react-redux';
const UnAuthorized = () => {
    const userFlag = useSelector((state) => state.auth.userFlag);
    const url = userFlag === "admin" ? "/admin/dashboard" : "/";
    return (
        <div className={"d-flex flex-column align-items-center align-content-center justify-content-start vh-100"} style={{backgroundImage: `url(${NotFoundBG})`}}>
            <ReactSVG src={imgUnAuthorized} />
            <div className={"fs-5 text-uppercase my-5"} style={{color: "#565872"}}>UnAuthorized Access!</div>
            <Link to={url}>
                <Button className={"submit-btn"}>
                    Back To Home
                </Button>
            </Link>
        </div>
    );
};

export default UnAuthorized;
