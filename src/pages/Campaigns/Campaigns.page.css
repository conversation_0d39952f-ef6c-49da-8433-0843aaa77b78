.content-container {
  padding: 20px;
  margin: 0 auto;
  max-width: 100%;
}

.view-campaign-ads-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  font-size: 0.875rem;
}

.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.campaign-name-cell {
  max-width: 200px;
}

.campaign-status-active {
  color: #28a745;
  font-weight: bold;
}

.campaign-status-paused {
  color: #ffc107;
  font-weight: bold;
}

.campaign-status-inactive {
  color: #dc3545;
  font-weight: bold;
}

.insights-metrics {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.875rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-weight: 500;
  color: #6c757d;
}

.metric-value {
  font-weight: bold;
  color: #495057;
}

.filters-card {
  background-color: #f8f9fa !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 0.375rem !important;
}

.filters-card h5 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
}

.filters-card .form-label {
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.filters-card .input-group-text {
  background-color: #e9ecef;
  border-color: #ced4da;
  color: #6c757d;
}

.filters-card .form-control,
.filters-card .form-select {
  border-color: #ced4da;
}

.filters-card .form-control:focus,
.filters-card .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .view-campaign-ads-btn {
    font-size: 0.75rem;
    padding: 2px 6px;
  }

  .insights-metrics {
    font-size: 0.75rem;
  }

  .filters-card {
    padding: 1rem !important;
  }

  .filters-card .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .filters-card .d-flex.gap-2 .btn {
    width: 100%;
  }
}
