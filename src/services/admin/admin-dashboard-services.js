import apiRequest from "../../utils/apiRequest";

const getAdminDashboardClientsApi = async () => {
    return apiRequest("admin/dashboard/clients", "get");
};

const getAdminLeadsStatusApi = async (clientId, signal) => {
    return apiRequest(`admin/dashboard/leadsFilterStatus/${clientId}`, "get", null, {}, signal);
};

const getAdminNewLeadsCountApi = async (clientId, signal) => {
    return apiRequest(`admin/dashboard/widget/${clientId}`, "get", null, {}, signal);
};

const getAdminFilterDataApi = async (clientId, signal) => {
    return apiRequest(`admin/dashboard/leadsFilterDate/${clientId}`, "get", null, {}, signal);
};

const getAdminFilterCompletedLeads = async (clientId, signal) => {
    return apiRequest(`admin/dashboard/leadsFilterCompleted/${clientId}`, "get", null, {}, signal);
};

const getAdminLeadsSourceApi = async (clientId, signal) => {
    return apiRequest(`admin/dashboard/leadsFilterSource/${clientId}`, "get", null, {}, signal);
};

// Utility function to build query parameters
const buildQueryParams = (params) => {
    const queryParams = new URLSearchParams();
    if (params.recordsPerPage) queryParams.append("number_of_records", params.recordsPerPage);
    if (params.currentPage) queryParams.append("page", params.currentPage);
    if (params.from) queryParams.append("from", params.from);
    if (params.to) queryParams.append("to", params.to);
    if (params.assigned) queryParams.append("assigned", params.assigned);
    if (params.source) queryParams.append("source", params.source);

    return queryParams.toString();
};

const getAdminNewLeadsTodayApi = async ({ signal, params }) => {
    const queryParams = buildQueryParams(params);
    const endpoint = params.url
        ? `${params.url}&${queryParams}`
        : `admin/dashboard/today_leads/${params.clientId}?${queryParams}`;

    return apiRequest(endpoint, "get", null, {}, signal);
};

// Fetch paginated completed leads for the whole system
const getAdminCompletedLeadsApi = async ({ current_page = 1, per_page = 10 } = {}) => {
    return apiRequest('admin/completed-leads', 'get', { current_page, per_page });
};

const getAdminNewLeads = async ({ current_page = 1, per_page = 10 } = {}) => {
    return apiRequest('admin/new-leads', 'get', { current_page, per_page });
}

export default {
    getAdminDashboardClientsApi,
    getAdminLeadsStatusApi,
    getAdminNewLeadsCountApi,
    getAdminFilterDataApi,
    getAdminFilterCompletedLeads,
    getAdminLeadsSourceApi,
    getAdminNewLeadsTodayApi,
    getAdminCompletedLeadsApi,
    getAdminNewLeads,
}
