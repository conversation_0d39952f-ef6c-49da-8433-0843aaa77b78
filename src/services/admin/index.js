import apiRequest from "../../utils/apiRequest";
import { shouldShowPackageFeatures } from "../../config/packageVisibility";

const createAdminMember = async (memberData) => {
    return apiRequest("admin/member", "post", memberData);
};

const deleteAdminMemberApi = async (id) => {
    return apiRequest(`admin/member/${id}`, "delete");
};

const editAdminMemberApi = async (id, updatedData) => {
    return await apiRequest(`admin/member/${id}`, "put", updatedData);
};

const updateAdminMemberStatusApi = async (id) => {
    return await apiRequest(`admin/client/${id}`, "delete", null, {}, null, {}, "Member updated successfully");
};


// Statistics
const getSingleAdminMemberApi = async (id) => {
    return await apiRequest(`admin/member/${id}`, "get");
};

const getAllAdminMembersApi = async () => {
    return await apiRequest("admin/member", "get");
};

const getTodayClients = async () => {
    return await apiRequest("admin/today_clients", "get");
};

const getTodayLeads = async () => {
    return await apiRequest("admin/today_Leads", "get");
};

const getTodayCompletedLeads = async () => {
    return await apiRequest("admin/today_Completed_Leads", "get");
};

export const getSubscriptionsDataTableApi = async (params, userId = null) => {
    // Check if user should have access to package features
    if (userId && !shouldShowPackageFeatures(userId)) {
        throw new Error('Package features are not available for this user');
    }

    return await apiRequest("admin/subscription/table", "get", params);
};

export const getYearlySubscriptionDataApi = async (year, userId = null) => {
    // Check if user should have access to package features
    if (userId && !shouldShowPackageFeatures(userId)) {
        throw new Error('Package features are not available for this user');
    }

    return await apiRequest("admin/subscription/revenue", "get", { year });
};

export const getAnalysisDataApi = async (year, userId = null) => {
    // Check if user should have access to package features
    if (userId && !shouldShowPackageFeatures(userId)) {
        throw new Error('Package features are not available for this user');
    }

    return await apiRequest("admin/subscription/analysis", "get");
};

export const getAnalysisTablesApi = async (params, userId = null) => {
    // Check if user should have access to package features
    if (userId && !shouldShowPackageFeatures(userId)) {
        throw new Error('Package features are not available for this user');
    }

    return await apiRequest("admin/subscription/analysis_page", "get", params);
};

export default {
    createAdminMember,
    deleteAdminMemberApi,
    editAdminMemberApi,
    updateAdminMemberStatusApi,
    getSingleAdminMemberApi,
    getAllAdminMembersApi,
    getTodayClients,
    getTodayLeads,
    getTodayCompletedLeads,
    getSubscriptionsDataTableApi,
    getYearlySubscriptionDataApi,
    getAnalysisDataApi,
    getAnalysisTablesApi
};
