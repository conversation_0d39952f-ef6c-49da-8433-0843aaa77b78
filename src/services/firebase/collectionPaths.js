/**
 * Firebase Collection Path Resolution Service
 *
 * This service provides centralized logic for determining consistent Firebase collection paths
 * for both messages and comments across different chat types (WhatsApp, Messenger, Instagram).
 *
 * The goal is to standardize collection paths so that both messages and comments use the same
 * document identifier pattern for consistent data access.
 */

/**
 * Extract the correct chat identifier based on chat type and selected cject
 * @ct} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @returns {string|null} - The chat identifier to use for Firebase collections
 */
export const getChatIdentifier = (selectedChat, chatType) => {
    if (!selectedChat) {
        console.warn('getChatIdentifier: selectedChat is null or undefined');
        return null;
    }

    if (chatType === 'whatsapp') {
        // For WhatsApp, use the normalized phone number
        const phoneNumber = selectedChat.sender_phone_number;
        if (!phoneNumber) {
            console.warn('getChatIdentifier: WhatsApp chat missing sender_phone_number');
            return null;
        }

        // Normalize phone number by removing + prefix and spaces
        return phoneNumber.toString().trim().replace(/^\+|\s+/g, "");
    }

    // For Messenger/Instagram, use the backend chat ID for consistency
    if (selectedChat.id) {
        return selectedChat.id.toString();
    }

    // Fallback to participant ID for legacy compatibility
    if (chatType === 'instagram' && selectedChat.participants?.data?.[1]?.id) {
        console.warn('getChatIdentifier: Using fallback participant ID for Instagram chat');
        return selectedChat.participants.data[1].id.toString();
    }

    if (chatType === 'messenger' && selectedChat.participants?.data?.[0]?.id) {
        console.warn('getChatIdentifier: Using fallback participant ID for Messenger chat');
        return selectedChat.participants.data[0].id.toString();
    }

    console.error('getChatIdentifier: Unable to determine chat identifier', {
        chatType,
        selectedChat: {
            id: selectedChat.id,
            sender_phone_number: selectedChat.sender_phone_number,
            participants: selectedChat.participants
        }
    });
    return null;
};

/**
 * Get sender ID for Firebase collection paths
 * This is different from getChatIdentifier - it gets the actual sender/participant ID
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @returns {string|null} - The sender ID for Firebase paths
 */
export const getSenderId = (selectedChat, chatType) => {
    if (!selectedChat) {
        console.warn('getSenderId: selectedChat is null or undefined');
        return null;
    }

    console.log('getSenderId: Debug info', {
        chatType,
        chatId: selectedChat.id,
        hasParticipants: !!selectedChat.participants,
        participantsData: selectedChat.participants?.data,
        availableKeys: Object.keys(selectedChat)
    });

    if (chatType === 'whatsapp') {
        // For WhatsApp, use the normalized phone number
        const phoneNumber = selectedChat.sender_phone_number;
        if (!phoneNumber) {
            console.warn('getSenderId: WhatsApp chat missing sender_phone_number');
            return null;
        }
        return phoneNumber.toString().trim().replace(/^\+|\s+|-/g, "");
    }

    // For Messenger/Instagram, use participant ID for consistency with backend webhook
    // The backend webhook typically uses the participant ID, not the chat ID
    let senderId = null;

    // First, try to use the participant ID (most consistent with backend webhook)
    if (selectedChat.participants?.data) {
        senderId = chatType === 'instagram'
            ? selectedChat.participants.data[1]?.id
            : selectedChat.participants.data[0]?.id;

        console.log('getSenderId: Using participant ID', {
            chatType,
            participantIndex: chatType === 'instagram' ? 1 : 0,
            senderId,
            participantsData: selectedChat.participants.data,
            participantsLength: selectedChat.participants.data.length
        });
    } else {
        console.log('getSenderId: No participants.data found', {
            selectedChat: {
                id: selectedChat.id,
                participants: selectedChat.participants,
                hasParticipants: !!selectedChat.participants,
                hasParticipantsData: !!selectedChat.participants?.data
            }
        });
    }
    // Fallback to chat ID if participant ID not available
    if (!senderId && selectedChat.id) {
        senderId = selectedChat.id;
        console.log('getSenderId: Using chat ID as fallback', { senderId });
    }
    // Last resort: try other common ID fields
    if (!senderId && selectedChat.sender_id) {
        senderId = selectedChat.sender_id;
        console.log('getSenderId: Using sender_id as fallback', { senderId });
    }
    if (!senderId && selectedChat.user_id) {
        senderId = selectedChat.user_id;
        console.log('getSenderId: Using user_id as fallback', { senderId });
    }

    if (!senderId) {
        console.error('getSenderId: Unable to determine sender ID', {
            chatType,
            chatId: selectedChat.id,
            participants: selectedChat.participants?.data,
            senderId: selectedChat.sender_id,
            userId: selectedChat.user_id,
            availableKeys: Object.keys(selectedChat)
        });
        return null;
    }

    console.log('getSenderId: Successfully determined sender ID', { senderId });
    return senderId.toString();
};

/**
 * Get Firebase collection paths using the clean structure
 * Structure: ${pageId}/${senderId}/messages and ${pageId}/${senderId}/comments
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier
 * @returns {Object} - Object containing messages and comments collection paths
 */
export const getCollectionPaths = (selectedChat, chatType, pageId) => {
    if (!selectedChat) {
        console.error('getCollectionPaths: selectedChat is required');
        return { messages: null, comments: null };
    }

    if (!chatType) {
        console.error('getCollectionPaths: chatType is required');
        return { messages: null, comments: null };
    }

    if (!pageId) {
        console.error('getCollectionPaths: pageId is required');
        return { messages: null, comments: null };
    }

    // Ensure pageId is a string
    const pageIdStr = pageId.toString();

    // Validate chatType
    const validChatTypes = ['whatsapp', 'messenger', 'instagram'];
    if (!validChatTypes.includes(chatType)) {
        console.error('getCollectionPaths: Invalid chatType', { chatType, validTypes: validChatTypes });
        return { messages: null, comments: null };
    }

    // Get the sender ID for Firebase paths
    const senderId = getSenderId(selectedChat, chatType);

    if (!senderId) {
        console.error('getCollectionPaths: Could not determine sender ID');
        return { messages: null, comments: null };
    }

    // Updated structure:
    // - Messages: pages/${pageId}/chats/${senderId}/messages
    // - Comments: pages/${pageId}/chats/${senderId}/comments (next to messages)
    const paths = {
        messages: `pages/${pageIdStr}/chats/${senderId}/messages`,
        comments: `pages/${pageIdStr}/chats/${senderId}/comments`
    };

    console.log('[GET_COLLECTION_PATHS] Generated paths:', {
        pageId: pageIdStr,
        senderId,
        chatType,
        paths
    });

    return paths;
};







/**
 * Validate collection path parameters
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat
 * @param {string} pageId - The page identifier
 * @returns {Object} - Validation result with isValid flag and errors array
 */
export const validateCollectionParams = (chatId, chatType, pageId) => {
    const errors = [];

    if (!chatId || typeof chatId !== 'string' || chatId.trim() === '') {
        errors.push('chatId must be a non-empty string');
    }

    if (!chatType || typeof chatType !== 'string') {
        errors.push('chatType must be a string');
    } else {
        const validChatTypes = ['whatsapp', 'messenger', 'instagram'];
        if (!validChatTypes.includes(chatType)) {
            errors.push(`chatType must be one of: ${validChatTypes.join(', ')}`);
        }
    }

    if (!pageId || typeof pageId !== 'string' || pageId.trim() === '') {
        errors.push('pageId must be a non-empty string');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Helper function to extract chat type from selected chat object
 * @param {Object} selectedChat - The selected chat object
 * @param {string} activeFilter - The active filter from UI ('whatsapp' or other)
 * @returns {string|null} - The determined chat type
 */




export const determineChatType = (selectedChat, activeFilter = null) => {
    if (!selectedChat) {
        return null;
    }

    // If activeFilter is explicitly 'whatsapp', use that
    if (activeFilter === 'whatsapp') {
        return 'whatsapp';
    }

    // Check for WhatsApp indicators in the chat object
    if (selectedChat.sender_phone_number) {
        return 'whatsapp';
    }

    // Check for Instagram flag
    if (selectedChat.flage === 'instagram') {
        return 'instagram';
    }

    // Default to messenger for other cases
    if (selectedChat.participants?.data) {
        return 'messenger';
    }

    console.warn('determineChatType: Unable to determine chat type', { selectedChat, activeFilter });
    return null;
};
