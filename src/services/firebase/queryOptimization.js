/**
 * Firebase Query Optimization Service
 *
 * This service provides optimized Firebase queries that use chat ID-based collection paths consistently.
 * It includes performance monitoring, query caching, and compound query optimization for both messages and comments.
 */

import {
    collection,
    query,
    where,
    orderBy,
    limit,
    startAfter,
    getDocs,
    onSnapshot,
    collectionGroup,
    doc,
    getDoc,
    enableNetwork,
    disableNetwork
} from 'firebase/firestore';
import { db } from '../../utils/firebase.config';
import { getCollectionPaths, getChatIdentifier, determineChatType } from './collectionPaths';

// Performance monitoring utilities
const queryPerformanceLog = {
    queries: new Map(),

    startTimer: (queryId) => {
        queryPerformanceLog.queries.set(queryId, {
            startTime: performance.now(),
            queryId
        });
    },

    endTimer: (queryId, metadata = {}) => {
        const queryData = queryPerformanceLog.queries.get(queryId);
        if (queryData) {
            const duration = performance.now() - queryData.startTime;
            const logData = {
                queryId,
                duration: Math.round(duration * 100) / 100, // Round to 2 decimal places
                timestamp: new Date().toISOString(),
                ...metadata
            };

            console.log(`[QUERY_PERFORMANCE] ${queryId}:`, logData);

            // Log slow queries as warnings
            if (duration > 1000) { // Queries taking more than 1 second
                console.warn(`[SLOW_QUERY] ${queryId} took ${duration}ms:`, logData);
            }

            queryPerformanceLog.queries.delete(queryId);
            return logData;
        }
        return null;
    }
};

// Query cache for frequently accessed data
const queryCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

const getCachedQuery = (cacheKey) => {
    const cached = queryCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.data;
    }
    queryCache.delete(cacheKey);
    return null;
};

const setCachedQuery = (cacheKey, data) => {
    queryCache.set(cacheKey, {
        data,
        timestamp: Date.now()
    });
};

/**
 * Optimized query for fetching messages using clean Firebase paths
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {Object} options - Query options (limit, orderDirection, startAfterDoc)
 * @returns {Promise<Array>} - Array of messages
 */
export const fetchOptimizedMessages = async (selectedChat, chatType, options = {}, pageId = null) => {
    const chatId = getChatIdentifier(selectedChat, chatType);
    const queryId = `fetch_messages_${chatId}_${chatType}`;
    queryPerformanceLog.startTimer(queryId);

    try {
        const {
            limit: queryLimit = 50,
            orderDirection = 'asc',
            startAfterDoc = null,
            useCache = true
        } = options;

        // Check cache first
        const cacheKey = `messages_${chatId}_${chatType}_${queryLimit}_${orderDirection}`;
        if (useCache && !startAfterDoc) {
            const cached = getCachedQuery(cacheKey);
            if (cached) {
                queryPerformanceLog.endTimer(queryId, { source: 'cache', count: cached.length });
                return cached;
            }
        }

        const paths = getCollectionPaths(selectedChat, chatType, pageId);
        if (!paths.messages) {
            throw new Error(`Unable to determine messages collection path for chatId: ${chatId}, chatType: ${chatType}`);
        }

        // Create collection reference using the path string split into segments
        const messagesRef = collection(db, ...paths.messages.split('/'));
        let messagesQuery = query(
            messagesRef,
            orderBy('created_time', orderDirection),
            limit(queryLimit)
        );

        if (startAfterDoc) {
            messagesQuery = query(messagesQuery, startAfter(startAfterDoc));
        }

        const snapshot = await getDocs(messagesQuery);
        const messages = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            _docRef: doc.ref // Store reference for pagination
        }));

        // Cache the results if not paginated
        if (useCache && !startAfterDoc) {
            setCachedQuery(cacheKey, messages);
        }

        queryPerformanceLog.endTimer(queryId, {
            source: 'firestore',
            count: messages.length,
            path: paths.messages,
            cached: useCache && !startAfterDoc
        });

        return messages;
    } catch (error) {
        queryPerformanceLog.endTimer(queryId, { error: error.message });
        console.error(`Error in fetchOptimizedMessages for ${queryId}:`, error);
        throw error;
    }
};

/**
 * Optimized query for fetching comments using consistent chat ID-based paths
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {Object} options - Query options (limit, orderDirection, startAfterDoc)
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Promise<Array>} - Array of comments
 */
export const fetchOptimizedComments = async (chatId, chatType, options = {}, pageId = null) => {
    const queryId = `fetch_comments_${chatId}_${chatType}`;
    queryPerformanceLog.startTimer(queryId);

    try {
        const {
            limit: queryLimit = 100,
            orderDirection = 'asc',
            startAfterDoc = null,
            useCache = true
        } = options;

        // Check cache first
        const cacheKey = `comments_${chatId}_${chatType}_${queryLimit}_${orderDirection}`;
        if (useCache && !startAfterDoc) {
            const cached = getCachedQuery(cacheKey);
            if (cached) {
                queryPerformanceLog.endTimer(queryId, { source: 'cache', count: cached.length });
                return cached;
            }
        }

        const paths = getCollectionPaths(chatId, chatType, pageId);
        if (!paths.comments) {
            throw new Error(`Unable to determine comments collection path for chatId: ${chatId}, chatType: ${chatType}`);
        }

        // Create collection reference using the path string split into segments
        const commentsRef = collection(db, ...paths.comments.split('/'));
        let commentsQuery = query(
            commentsRef,
            orderBy('createdAt', orderDirection),
            limit(queryLimit)
        );

        if (startAfterDoc) {
            commentsQuery = query(commentsQuery, startAfter(startAfterDoc));
        }

        const snapshot = await getDocs(commentsQuery);
        const comments = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            _docRef: doc.ref // Store reference for pagination
        }));

        // Cache the results
        if (useCache && !startAfterDoc) {
            setCachedQuery(cacheKey, comments);
        }

        queryPerformanceLog.endTimer(queryId, { source: 'firebase', count: comments.length });
        return comments;

    } catch (error) {
        queryPerformanceLog.endTimer(queryId, { error: error.message });
        throw error;
    }
};

/**
 * Optimized compound query for fetching both messages and comments for a chat
 * Uses shared chat ID for consistent data access
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Object containing both messages and comments
 */
export const fetchChatDataOptimized = async (chatId, chatType, options = {}, pageId = null) => {
    const queryId = `fetch_chat_data_${chatId}_${chatType}`;
    queryPerformanceLog.startTimer(queryId);

    try {
        const {
            messagesLimit = 50,
            commentsLimit = 100,
            useCache = true
        } = options;

        // Check cache first
        const cacheKey = `chat_data_${chatId}_${chatType}_${messagesLimit}_${commentsLimit}`;
        if (useCache) {
            const cached = getCachedQuery(cacheKey);
            if (cached) {
                queryPerformanceLog.endTimer(queryId, { source: 'cache', messagesCount: cached.messages.length, commentsCount: cached.comments.length });
                return cached;
            }
        }

        // Execute both queries in parallel for better performance
        const [messages, comments] = await Promise.all([
            fetchOptimizedMessages(chatId, chatType, { limit: messagesLimit, useCache: false }, pageId),
            fetchOptimizedComments(chatId, chatType, { limit: commentsLimit, useCache: false }, pageId)
        ]);

        const result = { messages, comments };

        // Cache the combined result
        if (useCache) {
            setCachedQuery(cacheKey, result);
        }

        queryPerformanceLog.endTimer(queryId, {
            source: 'firestore',
            messagesCount: messages.length,
            commentsCount: comments.length,
            cached: useCache
        });

        return result;
    } catch (error) {
        queryPerformanceLog.endTimer(queryId, { error: error.message });
        console.error(`Error in fetchChatDataOptimized for ${queryId}:`, error);
        throw error;
    }
};

/**
 * Optimized real-time listener for messages using clean Firebase paths
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {Function} callback - Callback function for real-time updates
 * @param {Object} options - Listener options
 * @returns {Function} - Unsubscribe function
 */
export const listenToMessagesOptimized = (selectedChat, chatType, callback, options = {}, pageId = null) => {
    const chatId = getChatIdentifier(selectedChat, chatType);
    const queryId = `listen_messages_${chatId}_${chatType}`;

    try {
        const {
            limit: queryLimit = 50,
            orderDirection = 'asc'
        } = options;

        const paths = getCollectionPaths(selectedChat, chatType, pageId);
        if (!paths.messages) {
            throw new Error(`Unable to determine messages collection path for chatId: ${chatId}, chatType: ${chatType}`);
        }

        // Create collection reference using the path string split into segments
        const messagesRef = collection(db, ...paths.messages.split('/'));
        const messagesQuery = query(
            messagesRef,
            orderBy('created_time', orderDirection),
            limit(queryLimit)
        );

        console.log(`[LISTENER_SETUP] ${queryId} - Path: ${paths.messages}`);

        const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
            const startTime = performance.now();

            const messages = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data(),
                _docRef: doc.ref
            }));

            const duration = performance.now() - startTime;
            console.log(`[LISTENER_UPDATE] ${queryId}:`, {
                count: messages.length,
                changes: snapshot.docChanges().length,
                processingTime: Math.round(duration * 100) / 100,
                path: paths.messages
            });

            // Invalidate cache when real-time updates occur
            const cacheKey = `messages_${chatId}_${chatType}_${queryLimit}_${orderDirection}`;
            queryCache.delete(cacheKey);

            callback(messages);
        }, (error) => {
            console.error(`[LISTENER_ERROR] ${queryId}:`, error);
            callback([], error);
        });

        return unsubscribe;
    } catch (error) {
        console.error(`Error setting up optimized messages listener for ${queryId}:`, error);
        callback([], error);
        return () => { };
    }
};

/**
 * Optimized real-time listener for comments using clean Firebase paths
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {Function} callback - Callback function for real-time updates
 * @param {Object} options - Listener options
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Function} - Unsubscribe function
 */
export const listenToCommentsOptimized = (selectedChat, chatType, callback, options = {}, pageId = null) => {
    const chatId = getChatIdentifier(selectedChat, chatType);
    const queryId = `listen_comments_${chatId}_${chatType}`;

    try {
        const {
            limit: queryLimit = 100,
            orderDirection = 'asc'
        } = options;

        const paths = getCollectionPaths(selectedChat, chatType, pageId);
        if (!paths.comments) {
            throw new Error(`Unable to determine comments collection path for chatId: ${chatId}, chatType: ${chatType}`);
        }

        // Create collection reference using the path string split into segments
        const commentsRef = collection(db, ...paths.comments.split('/'));
        const commentsQuery = query(
            commentsRef,
            orderBy('createdAt', orderDirection),
            limit(queryLimit)
        );

        console.log(`[LISTENER_SETUP] ${queryId} - Path: ${paths.comments}`);

        const unsubscribe = onSnapshot(commentsQuery, (snapshot) => {
            const startTime = performance.now();

            const comments = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data(),
                _docRef: doc.ref
            }));

            const duration = performance.now() - startTime;
            console.log(`[LISTENER_UPDATE] ${queryId}:`, {
                count: comments.length,
                changes: snapshot.docChanges().length,
                processingTime: Math.round(duration * 100) / 100,
                path: paths.comments
            });

            // Invalidate cache when real-time updates occur
            const cacheKey = `comments_${chatId}_${chatType}_${queryLimit}_${orderDirection}`;
            queryCache.delete(cacheKey);

            callback(comments);
        }, (error) => {
            console.error(`[LISTENER_ERROR] ${queryId}:`, error);
            callback([], error);
        });

        return unsubscribe;
    } catch (error) {
        console.error(`Error setting up optimized comments listener for ${queryId}:`, error);
        callback([], error);
        return () => { };
    }
};

/**
 * Optimized WhatsApp collection group query using consistent chat ID resolution
 * Replaces the existing collectionGroup queries with chat ID-based filtering
 * @param {string} businessPhoneNumber - The business phone number
 * @param {Function} callback - Callback function for real-time updates
 * @param {Object} options - Query options
 * @returns {Function} - Unsubscribe function
 */
export const listenToWhatsAppMessagesOptimized = (businessPhoneNumber, callback, options = {}) => {
    const queryId = `listen_whatsapp_messages_${businessPhoneNumber}`;

    try {
        const {
            limit: queryLimit = 100,
            orderDirection = 'desc'
        } = options;

        // Normalize business phone number for consistent matching
        const normalizedBusinessNumber = businessPhoneNumber.toString().trim().replace(/^\+|\s+/g, "");

        // Use collectionGroup but with optimized filtering
        // This query finds all messages where the business is either sender or recipient
        const incomingQuery = query(
            collectionGroup(db, "messages"),
            where("recipient", "==", normalizedBusinessNumber),
            orderBy("created_time", orderDirection),
            limit(queryLimit)
        );

        const outgoingQuery = query(
            collectionGroup(db, "messages"),
            where("sender", "==", normalizedBusinessNumber),
            orderBy("created_time", orderDirection),
            limit(queryLimit)
        );

        console.log(`[LISTENER_SETUP] ${queryId} - Business number: ${normalizedBusinessNumber}`);

        let allMessages = new Map(); // Use Map for efficient deduplication

        const handleSnapshot = (snapshot, direction) => {
            const startTime = performance.now();

            snapshot.docChanges().forEach((change) => {
                if (change.type === "added" || change.type === "modified") {
                    const data = change.doc.data();
                    const customerPhone = change.doc.ref.parent.parent.id; // Extract chat ID from path

                    // Create message with consistent chat ID
                    const message = {
                        id: change.doc.id,
                        chatId: customerPhone, // Use extracted chat ID
                        ...data,
                        _docRef: change.doc.ref
                    };

                    allMessages.set(change.doc.id, message);
                } else if (change.type === "removed") {
                    allMessages.delete(change.doc.id);
                }
            });

            const duration = performance.now() - startTime;
            console.log(`[LISTENER_UPDATE] ${queryId} (${direction}):`, {
                changes: snapshot.docChanges().length,
                totalMessages: allMessages.size,
                processingTime: Math.round(duration * 100) / 100
            });

            // Convert Map to Array and sort by timestamp
            const messagesArray = Array.from(allMessages.values()).sort((a, b) => {
                const dateA = new Date(a.created_time);
                const dateB = new Date(b.created_time);
                return orderDirection === 'desc' ? dateB - dateA : dateA - dateB;
            });

            callback(messagesArray);
        };

        const unsubscribeIncoming = onSnapshot(incomingQuery,
            (snapshot) => handleSnapshot(snapshot, 'incoming'),
            (error) => {
                console.error(`[LISTENER_ERROR] ${queryId} (incoming):`, error);
                callback([], error);
            }
        );

        const unsubscribeOutgoing = onSnapshot(outgoingQuery,
            (snapshot) => handleSnapshot(snapshot, 'outgoing'),
            (error) => {
                console.error(`[LISTENER_ERROR] ${queryId} (outgoing):`, error);
                callback([], error);
            }
        );

        // Return combined unsubscribe function
        return () => {
            unsubscribeIncoming();
            unsubscribeOutgoing();
        };
    } catch (error) {
        console.error(`Error setting up optimized WhatsApp messages listener for ${queryId}:`, error);
        callback([], error);
        return () => { };
    }
};

/**
 * Get query performance statistics
 * @returns {Object} - Performance statistics
 */
export const getQueryPerformanceStats = () => {
    return {
        activeQueries: queryPerformanceLog.queries.size,
        cacheSize: queryCache.size,
        cacheHitRate: queryCache.size > 0 ? 'Available' : 'No data'
    };
};

/**
 * Clear query cache (useful for testing or memory management)
 */
export const clearQueryCache = () => {
    queryCache.clear();
    console.log('[QUERY_CACHE] Cache cleared');
};

/**
 * Optimize Firebase connection for better query performance
 * @param {boolean} enable - Whether to enable or disable network
 */
export const optimizeFirebaseConnection = async (enable = true) => {
    try {
        if (enable) {
            await enableNetwork(db);
            console.log('[FIREBASE_OPTIMIZATION] Network enabled');
        } else {
            await disableNetwork(db);
            console.log('[FIREBASE_OPTIMIZATION] Network disabled');
        }
    } catch (error) {
        console.error('[FIREBASE_OPTIMIZATION] Error optimizing connection:', error);
    }
};

export default {
    fetchOptimizedMessages,
    fetchOptimizedComments,
    fetchChatDataOptimized,
    listenToMessagesOptimized,
    listenToCommentsOptimized,
    listenToWhatsAppMessagesOptimized,
    getQueryPerformanceStats,
    clearQueryCache,
    optimizeFirebaseConnection
};
