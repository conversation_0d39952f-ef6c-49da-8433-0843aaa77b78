# Firebase Collection Paths Troubleshooting Guide

## Overview

This guide helps developers troubleshoot issues related to the standardized Firebase collection path structure for messages and comments.

## Common Issues and Solutions

### 1. Messages Not Appearing in Chat

**Symptoms:**
- Messages sent but not visible in chat interface
- Real-time updates not working
- Empty message history

**Possible Causes:**
- Using incorrect collection path
- Chat ID resolution failure
- Migration incomplete

**Debugging Steps:**

```javascript
// 1. Check chat ID resolution
const chatType = determineChatType(selectedChat);
const chatId = getChatIdentifier(selectedChat, chatType);
console.log('Chat ID Resolution:', { chatType, chatId, selectedChat });

// 2. Verify collection paths
const paths = getCollectionPaths(chatId, chatType);
console.log('Collection Paths:', paths);

// 3. Check if messages exist in expected location
const messagesRef = collection(db, ...paths.messages.split('/'));
const snapshot = await getDocs(messagesRef);
console.log('Messages found:', snapshot.docs.length);
```

**Solutions:**
- Ensure `getChatIdentifier()` returns valid chat ID
- Verify chat object has required properties
- Check if migration is needed for the chat type

### 2. Comments and Messages Using Different Paths

**Symptoms:**
- Comments visible but messages missing (or vice versa)
- Inconsistent data between message and comment systems

**Debugging Steps:**

```javascript
// Check if both systems use same chat ID
const messagesChatId = getChatIdentifier(selectedChat, chatType);
const commentsChatId = getChatIdentifier(selectedChat, chatType); // Should be identical

const messagePaths = getCollectionPaths(messagesChatId, chatType);
const commentPaths = getCollectionPaths(commentsChatId, chatType);

console.log('Path Consistency Check:', {
    messagesChatId,
    commentsChatId,
    pathsMatch: messagePaths.messages.split('/')[1] === commentPaths.comments.split('/')[1]
});
```

**Solutions:**
- Update both systems to use `getChatIdentifier()` consistently
- Ensure chat type determination is identical in both systems
- Verify migration completed successfully

### 3. WhatsApp Messages Not Syncing

**Symptoms:**
- WhatsApp messages not appearing in real-time
- Phone number normalization issues

**Debugging Steps:**

```javascript
// Check phone number normalization
const rawPhone = selectedWhatsappChat.sender_phone_number;
const normalizedPhone = getChatIdentifier(selectedWhatsappChat, 'whatsapp');
console.log('Phone Normalization:', { rawPhone, normalizedPhone });

// Verify WhatsApp collection path
const paths = getCollectionPaths(normalizedPhone, 'whatsapp');
console.log('WhatsApp Paths:', paths);
```

**Solutions:**
- Ensure phone numbers are consistently normalized
- Check for leading + signs or spaces in phone numbers
- Verify WhatsApp chat object structure

### 4. Migration Issues

**Symptoms:**
- Data missing after migration
- Duplicate messages
- Migration script failures

**Debugging Steps:**

```javascript
// Check migration status
const migrationStatus = await getMigrationStatus(selectedChat);
console.log('Migration Status:', migrationStatus);

// Verify data integrity
const verificationResult = await verifyMigrationIntegrity(selectedChat);
console.log('Data Integrity:', verificationResult);
```

**Solutions:**
- Run migration verification utilities
- Use rollback functionality if needed
- Check migration logs for specific errors

### 5. Real-time Listeners Not Working

**Symptoms:**
- Messages appear only on page refresh
- Firebase listeners not triggering updates

**Debugging Steps:**

```javascript
// Test listener setup
const chatId = getChatIdentifier(selectedChat, chatType);
const paths = getCollectionPaths(chatId, chatType);

console.log('Setting up listener for:', paths.messages);

const unsubscribe = onSnapshot(
    collection(db, ...paths.messages.split('/')),
    (snapshot) => {
        console.log('Listener triggered:', snapshot.docs.length, 'messages');
        console.log('Changes:', snapshot.docChanges().length);
    },
    (error) => {
        console.error('Listener error:', error);
    }
);
```

**Solutions:**
- Verify Firebase permissions for collection paths
- Check network connectivity
- Ensure proper cleanup of previous listeners

## Diagnostic Tools

### Collection Path Validator

```javascript
/**
 * Validate collection path configuration for a chat
 */
const validateChatPaths = (selectedChat, chatType) => {
    const validation = {
        chatObject: !!selectedChat,
        chatType: !!chatType,
        chatId: null,
        paths: null,
        issues: []
    };

    if (!selectedChat) {
        validation.issues.push('Selected chat object is null or undefined');
        return validation;
    }

    if (!chatType) {
        validation.issues.push('Chat type is null or undefined');
        return validation;
    }

    try {
        const chatId = getChatIdentifier(selectedChat, chatType);
        validation.chatId = chatId;

        if (!chatId) {
            validation.issues.push('Unable to determine chat ID');
            return validation;
        }

        const paths = getCollectionPaths(chatId, chatType);
        validation.paths = paths;

        if (!paths.messages || !paths.comments) {
            validation.issues.push('Invalid collection paths generated');
        }

        // Check path consistency
        const messagesChatId = paths.messages.split('/')[1];
        const commentsChatId = paths.comments.split('/')[1];

        if (messagesChatId !== commentsChatId) {
            validation.issues.push('Messages and comments use different chat IDs');
        }

    } catch (error) {
        validation.issues.push(`Error during validation: ${error.message}`);
    }

    return validation;
};

// Usage
const validation = validateChatPaths(selectedChat, chatType);
console.log('Path Validation:', validation);
```

### Migration Status Checker

```javascript
/**
 * Check migration status for multiple chats
 */
const checkMigrationStatus = async (chats) => {
    const results = [];

    for (const chat of chats) {
        try {
            const status = await getMigrationStatus(chat);
            results.push({
                chatId: chat.id,
                chatType: determineChatType(chat),
                status: status.status,
                sourceCount: status.sourceCount,
                destCount: status.destCount
            });
        } catch (error) {
            results.push({
                chatId: chat.id,
                error: error.message
            });
        }
    }

    return results;
};
```

## Performance Debugging

### Query Performance Monitor

```javascript
// Monitor query performance for collection paths
const monitorQueryPerformance = (chatId, chatType) => {
    const startTime = performance.now();

    return {
        start: () => {
            console.log(`[PERF] Starting query for ${chatType} chat ${chatId}`);
        },

        end: (operation, count) => {
            const duration = performance.now() - startTime;
            console.log(`[PERF] ${operation} completed in ${duration.toFixed(2)}ms`, {
                chatId,
                chatType,
                count,
                duration
            });

            if (duration > 1000) {
                console.warn(`[PERF] Slow query detected: ${operation} took ${duration.toFixed(2)}ms`);
            }
        }
    };
};
```

## Error Codes and Messages

### Common Error Messages

| Error Message                         | Cause                                   | Solution                                    |
| ------------------------------------- | --------------------------------------- | ------------------------------------------- |
| "Unable to determine chat identifier" | Chat object missing required properties | Verify chat object structure                |
| "Invalid chatType"                    | Chat type not recognized                | Use 'whatsapp', 'messenger', or 'instagram' |
| "Collection path is null"             | Path resolution failed                  | Check chat ID and type validity             |
| "Firebase permission denied"          | Insufficient Firestore permissions      | Verify Firebase security rules              |
| "Migration verification failed"       | Data integrity issues                   | Run migration verification and fix issues   |

### Error Handling Best Practices

```javascript
// Robust error handling for collection operations
const safeCollectionOperation = async (selectedChat, operation) => {
    try {
        // Validate inputs
        if (!selectedChat) {
            throw new Error('Selected chat is required');
        }

        const chatType = determineChatType(selectedChat);
        if (!chatType) {
            throw new Error('Unable to determine chat type');
        }

        const chatId = getChatIdentifier(selectedChat, chatType);
        if (!chatId) {
            throw new Error('Unable to determine chat identifier');
        }

        const paths = getCollectionPaths(chatId, chatType);
        if (!paths.messages) {
            throw new Error('Unable to determine collection path');
        }

        // Perform operation
        return await operation(paths, chatId, chatType);

    } catch (error) {
        console.error('Collection operation failed:', {
            error: error.message,
            selectedChat: selectedChat?.id,
            stack: error.stack
        });

        // Return safe fallback or re-throw based on criticality
        throw error;
    }
};
```

## Logging and Monitoring

### Debug Logging Configuration

```javascript
// Enable detailed logging for troubleshooting
const DEBUG_COLLECTION_PATHS = true;

const debugLog = (message, data) => {
    if (DEBUG_COLLECTION_PATHS) {
        console.log(`[COLLECTION_PATHS_DEBUG] ${message}:`, data);
    }
};

// Use in collection path operations
const chatId = getChatIdentifier(selectedChat, chatType);
debugLog('Chat ID resolved', { chatId, chatType, selectedChat: selectedChat?.id });
```

### Performance Monitoring

```javascript
// Track collection path performance
const performanceMetrics = {
    pathResolutions: 0,
    averageResolutionTime: 0,
    errors: 0
};

const trackPathResolution = (startTime, success, error = null) => {
    const duration = performance.now() - startTime;
    performanceMetrics.pathResolutions++;
    performanceMetrics.averageResolutionTime =
        (performanceMetrics.averageResolutionTime + duration) / 2;

    if (!success) {
        performanceMetrics.errors++;
    }

    console.log('Path Resolution Metrics:', performanceMetrics);
};
```

## Contact and Support

For additional support with collection path issues:

1. Check the main documentation: `COLLECTION_PATHS_DOCUMENTATION.md`
2. Review test files for usage examples
3. Examine migration utilities for data integrity tools
4. Consult query optimization service for performance improvements

## Related Files

- `src/services/firebase/collectionPaths.js` - Main implementation
- `src/utils/firebase/dataMigration.js` - Migration utilities
- `src/services/firebase/queryOptimization.js` - Performance optimization
- `src/services/firebase/collectionPaths.test.js` - Test examples
