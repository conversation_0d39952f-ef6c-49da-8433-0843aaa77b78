/**
 * Unit tests for Firebase Collection Path Resolution Service
 */

import {
    getChatIdentifier,
    getCollectionPaths,
    getLegacyCollectionPaths,
    requiresMigration,
    getDualWritePaths,
    validateCollectionParams,
    determineChatType
} from './collectionPaths';

describe('Firebase Collection Path Resolution Service', () => {
    describe('getChatIdentifier', () => {
        test('returns normalized phone number for WhatsApp chats', () => {
            const whatsappChat = {
                sender_phone_number: '****** 567 8900'
            };

            const result = getChatIdentifier(whatsappChat, 'whatsapp');
            expect(result).toBe('12345678900');
        });

        test('handles phone numbers without + prefix', () => {
            const whatsappChat = {
                sender_phone_number: '1234567890'
            };

            const result = getChatIdentifier(whatsappChat, 'whatsapp');
            expect(result).toBe('1234567890');
        });

        test('returns backend chat ID for messenger chats', () => {
            const messengerChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const result = getChatIdentifier(messengerChat, 'messenger');
            expect(result).toBe('backend_chat_123');
        });

        test('returns backend chat ID for instagram chats', () => {
            const instagramChat = {
                id: 'backend_chat_789',
                participants: {
                    data: [null, { id: 'participant_101' }]
                }
            };

            const result = getChatIdentifier(instagramChat, 'instagram');
            expect(result).toBe('backend_chat_789');
        });

        test('falls back to participant ID for messenger when no backend ID', () => {
            const messengerChat = {
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const result = getChatIdentifier(messengerChat, 'messenger');
            expect(result).toBe('participant_456');
        });

        test('falls back to participant ID for instagram when no backend ID', () => {
            const instagramChat = {
                participants: {
                    data: [null, { id: 'participant_101' }]
                }
            };

            const result = getChatIdentifier(instagramChat, 'instagram');
            expect(result).toBe('participant_101');
        });

        test('returns null for null selectedChat', () => {
            const result = getChatIdentifier(null, 'whatsapp');
            expect(result).toBeNull();
        });

        test('returns null for undefined selectedChat', () => {
            const result = getChatIdentifier(undefined, 'messenger');
            expect(result).toBeNull();
        });

        test('returns null for WhatsApp chat without phone number', () => {
            const whatsappChat = {};

            const result = getChatIdentifier(whatsappChat, 'whatsapp');
            expect(result).toBeNull();
        });

        test('returns null when unable to determine identifier', () => {
            const invalidChat = {};

            const result = getChatIdentifier(invalidChat, 'messenger');
            expect(result).toBeNull();
        });
    });

    describe('getCollectionPaths', () => {
        test('returns WhatsApp collection paths', () => {
            const whatsappChat = {
                sender_phone_number: '+1234567890'
            };
            const result = getCollectionPaths(whatsappChat, 'whatsapp', '123');

            expect(result).toEqual({
                messages: 'pages/123/chats/1234567890/messages',
                comments: 'pages/123/chats/1234567890/comments'
            });
        });

        test('returns messenger collection paths', () => {
            const messengerChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };
            const result = getCollectionPaths(messengerChat, 'messenger', '123');

            expect(result).toEqual({
                messages: 'pages/123/chats/participant_456/messages',
                comments: 'pages/123/chats/participant_456/comments'
            });
        });

        test('returns instagram collection paths', () => {
            const instagramChat = {
                id: 'backend_chat_789',
                participants: {
                    data: [null, { id: 'participant_101' }]
                }
            };
            const result = getCollectionPaths(instagramChat, 'instagram', '123');

            expect(result).toEqual({
                messages: 'pages/123/chats/participant_101/messages',
                comments: 'pages/123/chats/participant_101/comments'
            });
        });

        test('returns null paths for missing selectedChat', () => {
            const result = getCollectionPaths(null, 'whatsapp', '123');

            expect(result).toEqual({
                messages: null,
                comments: null
            });
        });

        test('returns null paths for missing chatType', () => {
            const chat = {
                sender_phone_number: '+1234567890'
            };
            const result = getCollectionPaths(chat, null, '123');

            expect(result).toEqual({
                messages: null,
                comments: null
            });
        });

        test('returns null paths for missing pageId', () => {
            const chat = {
                sender_phone_number: '+1234567890'
            };
            const result = getCollectionPaths(chat, 'whatsapp', null);

            expect(result).toEqual({
                messages: null,
                comments: null
            });
        });

        test('returns null paths for invalid chatType', () => {
            const chat = {
                sender_phone_number: '+1234567890'
            };
            const result = getCollectionPaths(chat, 'invalid_type', '123');

            expect(result).toEqual({
                messages: null,
                comments: null
            });
        });
    });

    describe('getLegacyCollectionPaths', () => {
        test('returns WhatsApp legacy paths (unchanged)', () => {
            const whatsappChat = {
                sender_phone_number: '+1234567890'
            };

            const result = getLegacyCollectionPaths(whatsappChat, 'whatsapp');

            expect(result).toEqual({
                messages: 'whatsApp/1234567890/messages',
                comments: 'whatsApp/1234567890/comments'
            });
        });

        test('returns messenger legacy paths with participant ID for messages', () => {
            const messengerChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const result = getLegacyCollectionPaths(messengerChat, 'messenger');

            expect(result).toEqual({
                messages: 'chats/participant_456/messages',
                comments: 'chats/backend_chat_123/comments'
            });
        });

        test('returns instagram legacy paths with participant ID for messages', () => {
            const instagramChat = {
                id: 'backend_chat_789',
                participants: {
                    data: [null, { id: 'participant_101' }]
                }
            };

            const result = getLegacyCollectionPaths(instagramChat, 'instagram');

            expect(result).toEqual({
                messages: 'chats/participant_101/messages',
                comments: 'chats/backend_chat_789/comments'
            });
        });

        test('returns null paths for invalid input', () => {
            const result = getLegacyCollectionPaths(null, 'messenger');

            expect(result).toEqual({
                messages: null,
                comments: null
            });
        });
    });

    describe('requiresMigration', () => {
        test('returns false for WhatsApp (already consistent)', () => {
            expect(requiresMigration('whatsapp')).toBe(false);
        });

        test('returns true for messenger (needs migration)', () => {
            expect(requiresMigration('messenger')).toBe(true);
        });

        test('returns true for instagram (needs migration)', () => {
            expect(requiresMigration('instagram')).toBe(true);
        });

        test('returns false for invalid chat type', () => {
            expect(requiresMigration('invalid')).toBe(false);
        });
    });

    describe('getDualWritePaths', () => {
        test('returns dual write paths for messenger chat', () => {
            const messengerChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const result = getDualWritePaths(messengerChat, 'messenger');

            expect(result).toEqual({
                current: {
                    messages: 'chats/backend_chat_123/messages',
                    comments: 'chats/backend_chat_123/comments'
                },
                legacy: {
                    messages: 'chats/participant_456/messages',
                    comments: 'chats/backend_chat_123/comments'
                },
                chatId: 'backend_chat_123',
                requiresMigration: true
            });
        });

        test('returns dual write paths for WhatsApp chat', () => {
            const whatsappChat = {
                sender_phone_number: '+1234567890'
            };

            const result = getDualWritePaths(whatsappChat, 'whatsapp');

            expect(result).toEqual({
                current: {
                    messages: 'whatsApp/1234567890/messages',
                    comments: 'whatsApp/1234567890/comments'
                },
                legacy: {
                    messages: 'whatsApp/1234567890/messages',
                    comments: 'whatsApp/1234567890/comments'
                },
                chatId: '1234567890',
                requiresMigration: false
            });
        });
    });

    describe('validateCollectionParams', () => {
        test('returns valid for correct parameters', () => {
            const result = validateCollectionParams('chat_123', 'messenger');

            expect(result).toEqual({
                isValid: true,
                errors: []
            });
        });

        test('returns invalid for missing chatId', () => {
            const result = validateCollectionParams(null, 'messenger');

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('chatId must be a non-empty string');
        });

        test('returns invalid for empty chatId', () => {
            const result = validateCollectionParams('', 'messenger');

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('chatId must be a non-empty string');
        });

        test('returns invalid for missing chatType', () => {
            const result = validateCollectionParams('chat_123', null);

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('chatType must be a string');
        });

        test('returns invalid for invalid chatType', () => {
            const result = validateCollectionParams('chat_123', 'invalid');

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('chatType must be one of: whatsapp, messenger, instagram');
        });

        test('returns multiple errors for multiple invalid parameters', () => {
            const result = validateCollectionParams('', 'invalid');

            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(2);
            expect(result.errors).toContain('chatId must be a non-empty string');
            expect(result.errors).toContain('chatType must be one of: whatsapp, messenger, instagram');
        });
    });

    describe('determineChatType', () => {
        test('returns whatsapp for explicit activeFilter', () => {
            const chat = { id: 'some_chat' };
            const result = determineChatType(chat, 'whatsapp');

            expect(result).toBe('whatsapp');
        });

        test('returns whatsapp for chat with phone number', () => {
            const chat = { sender_phone_number: '+1234567890' };
            const result = determineChatType(chat);

            expect(result).toBe('whatsapp');
        });

        test('returns instagram for chat with instagram flag', () => {
            const chat = {
                flage: 'instagram',
                participants: { data: [null, { id: 'user_123' }] }
            };
            const result = determineChatType(chat);

            expect(result).toBe('instagram');
        });

        test('returns messenger for chat with participants but no instagram flag', () => {
            const chat = {
                participants: { data: [{ id: 'user_456' }] }
            };
            const result = determineChatType(chat);

            expect(result).toBe('messenger');
        });

        test('returns null for null selectedChat', () => {
            const result = determineChatType(null);

            expect(result).toBeNull();
        });

        test('returns null for chat without identifiable type', () => {
            const chat = {};
            const result = determineChatType(chat);

            expect(result).toBeNull();
        });
    });

    describe('Edge cases and error handling', () => {
        test('handles numeric chatId by converting to string', () => {
            const result = getCollectionPaths(123, 'messenger');

            expect(result).toEqual({
                messages: 'chats/123/messages',
                comments: 'chats/123/comments'
            });
        });

        test('handles phone number normalization edge cases', () => {
            const testCases = [
                { input: '+****************', expected: '12345678900' },
                { input: '  +1234567890  ', expected: '1234567890' },
                { input: '1234567890', expected: '1234567890' }
            ];

            testCases.forEach(({ input, expected }) => {
                const chat = { sender_phone_number: input };
                const result = getChatIdentifier(chat, 'whatsapp');
                expect(result).toBe(expected);
            });
        });

        test('handles missing participant data gracefully', () => {
            const messengerChat = {
                participants: { data: [] }
            };

            const result = getChatIdentifier(messengerChat, 'messenger');
            expect(result).toBeNull();
        });

        test('handles malformed participant data', () => {
            const instagramChat = {
                participants: { data: [null, null] }
            };

            const result = getChatIdentifier(instagramChat, 'instagram');
            expect(result).toBeNull();
        });
    });
});
