# Firebase Collection Paths Service

## Overview

This service provides centralized, consistent Firebase collection path resolution for messages and comments across different chat types (WhatsApp, Messenger, Instagram). It ensures both messages and comments use the same document identifier pattern for reliable data access and synchronization.

## Key Features

- **Consistent Identifiers**: Messages and comments use the same chat ID for each chat type
- **Chat Type Support**: Handles WhatsApp, Messenger, and Instagram chats appropriately
- **Migration Support**: Provides dual-write capabilities during migration from old to new structure
- **Error Handling**: Comprehensive validation and fallback mechanisms
- **Performance Optimized**: Efficient path resolution with minimal overhead

## Quick Start

```javascript
import {
    getCollectionPaths,
    getChatIdentifier,
    determineChatType
} from './collectionPaths';

// Basic usage
const chatType = determineChatType(selectedChat);
const chatId = getChatIdentifier(selectedChat, chatType);
const paths = getCollectionPaths(chatId, chatType);

console.log(paths.messages); // e.g., "chats/12345/messages"
console.log(paths.comments); // e.g., "chats/12345/comments"

// Use with Firebase
import { collection, query, orderBy } from 'firebase/firestore';
import { db } from '../../utils/firebase.config';

const messagesRef = collection(db, ...paths.messages.split('/'));
const messagesQuery = query(messagesRef, orderBy('created_time', 'asc'));
```

## Collection Structure

### Before (Inconsistent)
```
Messages: chats/{senderId}/messages     ❌ Used participant ID
Comments: chats/{chatId}/comments       ✅ Used backend chat ID
```

### After (Consistent)
```
Messages: chats/{chatId}/messages       ✅ Uses backend chat ID
Comments: chats/{chatId}/comments       ✅ Uses backend chat ID
```

### WhatsApp (Already Consistent)
```
Messages: whatsApp/{phoneNumber}/messages   ✅ Uses normalized phone number
Comments: whatsApp/{phoneNumber}/comments   ✅ Uses normalized phone number
```

## API Reference

### Core Functions

#### `determineChatType(selectedChat, activeFilter?)`
Automatically determines the chat type from a chat object.

**Parameters:**
- `selectedChat` (Object): The selected chat object
- `activeFilter` (string, optional): UI filter hint ('whatsapp' or other)

**Returns:** `'whatsapp' | 'messenger' | 'instagram' | null`

**Example:**
```javascript
const chatType = determineChatType(selectedChat);
// Returns: 'whatsapp', 'messenger', 'instagram', or null
```

#### `getChatIdentifier(selectedChat, chatType)`
Extracts the correct chat identifier based on chat type.

**Parameters:**
- `selectedChat` (Object): The selected chat object
- `chatType` (string): The chat type ('whatsapp', 'messenger', 'instagram')

**Returns:** `string | null`

**Example:**
```javascript
// WhatsApp: Returns normalized phone number
const whatsappId = getChatIdentifier(whatsappChat, 'whatsapp');
// Returns: "1234567890" (normalized phone number)

// Messenger/Instagram: Returns backend chat ID
const messengerId = getChatIdentifier(messengerChat, 'messenger');
// Returns: "chat_12345" (backend chat ID)
```

#### `getCollectionPaths(chatId, chatType, selectedChat?)`
Returns consistent collection paths for messages and comments.

**Parameters:**
- `chatId` (string): The chat identifier from `getChatIdentifier()`
- `chatType` (string): The chat type
- `selectedChat` (Object, optional): Additional context

**Returns:** `{ messages: string, comments: string }`

**Example:**
```javascript
const paths = getCollectionPaths('12345', 'messenger');
// Returns: {
//   messages: "chats/12345/messages",
//   comments: "chats/12345/comments"
// }

const whatsappPaths = getCollectionPaths('1234567890', 'whatsapp');
// Returns: {
//   messages: "whatsApp/1234567890/messages",
//   comments: "whatsApp/1234567890/comments"
// }
```

### Migration Functions

#### `getDualWritePaths(selectedChat, chatType)`
Returns both current and legacy paths for dual-write scenarios during migration.

**Returns:**
```javascript
{
  current: { messages: string, comments: string },
  legacy: { messages: string, comments: string },
  chatId: string,
  requiresMigration: boolean
}
```

#### `requiresMigration(chatType)`
Determines if a chat type requires migration from legacy structure.

**Returns:** `boolean`

### Validation Functions

#### `validateCollectionParams(chatId, chatType)`
Validates collection path parameters.

**Returns:**
```javascript
{
  isValid: boolean,
  errors: string[]
}
```

## Usage Patterns

### Setting Up Firebase Listeners

```javascript
import { onSnapshot, collection, query, orderBy } from 'firebase/firestore';

const setupMessageListener = (selectedChat) => {
    const chatType = determineChatType(selectedChat);
    const chatId = getChatIdentifier(selectedChat, chatType);
    const { messages } = getCollectionPaths(chatId, chatType);

    const messagesRef = collection(db, ...messages.split('/'));
    const messagesQuery = query(messagesRef, orderBy('created_time', 'asc'));

    return onSnapshot(messagesQuery, (snapshot) => {
        const messages = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
        }));

        // Handle messages update
        console.log(`Received ${messages.length} messages`);
    });
};
```

### Dual Write During Migration

```javascript
import { setDoc, doc } from 'firebase/firestore';

const sendMessageWithDualWrite = async (selectedChat, messageData) => {
    const chatType = determineChatType(selectedChat);
    const dualPaths = getDualWritePaths(selectedChat, chatType);

    if (dualPaths.requiresMigration) {
        // Write to both old and new paths during migration
        const promises = [];

        // Write to new path (primary)
        if (dualPaths.current.messages) {
            const newRef = doc(db, ...dualPaths.current.messages.split('/'), messageData.id);
            promises.push(setDoc(newRef, messageData));
        }

        // Write to legacy path (backup)
        if (dualPaths.legacy.messages) {
            const legacyRef = doc(db, ...dualPaths.legacy.messages.split('/'), messageData.id);
            promises.push(setDoc(legacyRef, messageData));
        }

        await Promise.all(promises);
    } else {
        // Single write for consistent chat types (WhatsApp)
        const { messages } = getCollectionPaths(dualPaths.chatId, chatType);
        const messageRef = doc(db, ...messages.split('/'), messageData.id);
        await setDoc(messageRef, messageData);
    }
};
```

### Error Handling

```javascript
const safeGetCollectionPaths = (selectedChat) => {
    try {
        // Validate input
        if (!selectedChat) {
            throw new Error('Selected chat is required');
        }

        // Determine chat type
        const chatType = determineChatType(selectedChat);
        if (!chatType) {
            throw new Error('Unable to determine chat type');
        }

        // Get chat identifier
        const chatId = getChatIdentifier(selectedChat, chatType);
        if (!chatId) {
            throw new Error('Unable to determine chat identifier');
        }

        // Validate parameters
        const validation = validateCollectionParams(chatId, chatType);
        if (!validation.isValid) {
            throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
        }

        // Get collection paths
        const paths = getCollectionPaths(chatId, chatType);
        if (!paths.messages || !paths.comments) {
            throw new Error('Unable to generate collection paths');
        }

        return { success: true, paths, chatId, chatType };

    } catch (error) {
        console.error('Error getting collection paths:', error);
        return {
            success: false,
            error: error.message,
            paths: null,
            chatId: null,
            chatType: null
        };
    }
};
```

## Migration Guide

### Phase 1: Dual Write Implementation
1. Update message sending logic to use `getDualWritePaths()`
2. Write to both old and new collection paths
3. Implement fallback read logic (new path first, then old path)

### Phase 2: Data Migration
1. Use migration utilities in `src/utils/firebase/dataMigration.js`
2. Run migration verification to ensure data integrity
3. Monitor migration progress and handle errors

### Phase 3: Cleanup
1. Remove dual write logic
2. Update all code to use only new collection paths
3. Clean up old collection paths after verification

## Testing

The service includes comprehensive tests covering:
- Path resolution for all chat types
- Migration scenarios and edge cases
- Error handling and validation
- Performance and consistency

Run tests with:
```bash
npm test src/services/firebase/collectionPaths.test.js
```

## Performance Considerations

- **Path Resolution**: O(1) complexity, no caching needed
- **Chat Type Detection**: Optimized for common cases first
- **Migration**: Batch processing for large datasets
- **Query Optimization**: Use with `queryOptimization.js` for best performance

## Troubleshooting

Common issues and solutions:

1. **"Unable to determine chat identifier"**
   - Check if chat object has required properties
   - Verify chat type is supported

2. **"Collection path is null"**
   - Validate chat ID and type parameters
   - Check if migration is needed

3. **Messages/comments inconsistency**
   - Ensure both systems use same chat ID resolution
   - Verify migration completed successfully

See `TROUBLESHOOTING_GUIDE.md` for detailed debugging steps.

## Related Documentation

- [Collection Paths Documentation](./COLLECTION_PATHS_DOCUMENTATION.md) - Detailed technical documentation
- [Troubleshooting Guide](./TROUBLESHOOTING_GUIDE.md) - Debug and fix common issues
- [Query Optimization](./queryOptimization.js) - Performance optimization utilities
- [Migration Utilities](../../utils/firebase/dataMigration.js) - Data migration tools

## Contributing

When adding new chat types or modifying collection paths:

1. Follow the established pattern of using consistent identifiers
2. Add comprehensive tests for new functionality
3. Update documentation and examples
4. Consider migration implications for existing data
5. Ensure backward compatibility during transitions
