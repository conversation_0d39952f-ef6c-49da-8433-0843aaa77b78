/**
 * Integration Test Runner for Firebase Collection Consistency
 *
 * This script runs comprehensive integration tests to validate that the Firebase
 * collection consistency implementation meets all requirements.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
    testFiles: [
        'src/services/firebase/finalIntegrationValidation.test.js',
        'src/services/firebase/messageCommentConsistency.e2e.test.js',
        'src/redux/features/metaBusinessChatSlice.integration.test.js',
        'src/services/firebase/collectionPaths.test.js',
        'src/utils/firebase/dataMigration.test.js'
    ],
    requirements: [
        '1.1 - Messages and comments use same document identifier pattern',
        '1.2 - Consistent collection paths across systems',
        '1.3 - Consistent data updates',
        '1.4 - Consistent Firebase listeners',
        '2.1 - Preserve existing message data during migration',
        '2.2 - Preserve existing comment data during migration',
        '2.3 - Handle both old and new collection paths gracefully',
        '3.1 - Maintain full functionality during migration',
        '3.2 - Store messages in correct collection path',
        '3.3 - Store comments in correct collection path',
        '3.4 - Display all messages and comments correctly',
        '4.1 - Use standardized collection path pattern',
        '4.2 - Updated documentation reflecting new structure',
        '4.3 - Clear guidelines for correct collection paths',
        '4.4 - Follow established consistent pattern'
    ],
    coverageThreshold: 80
};

/**
 * Validate that all test files exist and are properly structured
 */
function validateTestFiles() {
    console.log('🔍 Validating test files...\n');

    const results = {
        valid: 0,
        invalid: 0,
        missing: 0,
        details: []
    };

    TEST_CONFIG.testFiles.forEach(filePath => {
        if (!fs.existsSync(filePath)) {
            console.log(`❌ Missing: ${filePath}`);
            results.missing++;
            results.details.push({ file: filePath, status: 'missing' });
            return;
        }

        try {
            const content = fs.readFileSync(filePath, 'utf8');

            // Basic validation checks
            const hasDescribe = content.includes('describe(');
            const hasTest = content.includes('test(') || content.includes('it(');
            const hasExpect = content.includes('expect(');
            const hasMocks = content.includes('jest.mock(');

            if (hasDescribe && hasTest && hasExpect) {
                console.log(`✅ Valid: ${filePath}`);
                results.valid++;
                results.details.push({
                    file: filePath,
                    status: 'valid',
                    hasMocks,
                    testCount: (content.match(/test\(|it\(/g) || []).length
                });
            } else {
                console.log(`⚠️  Invalid structure: ${filePath}`);
                results.invalid++;
                results.details.push({
                    file: filePath,
                    status: 'invalid',
                    issues: {
                        missingDescribe: !hasDescribe,
                        missingTests: !hasTest,
                        missingExpectations: !hasExpect
                    }
                });
            }
        } catch (error) {
            console.log(`❌ Error reading: ${filePath} - ${error.message}`);
            results.invalid++;
            results.details.push({ file: filePath, status: 'error', error: error.message });
        }
    });

    console.log(`\n📊 Test File Validation Summary:`);
    console.log(`   Valid: ${results.valid}`);
    console.log(`   Invalid: ${results.invalid}`);
    console.log(`   Missing: ${results.missing}`);
    console.log(`   Total: ${TEST_CONFIG.testFiles.length}\n`);

    return results;
}

/**
 * Run Jest tests with coverage reporting
 */
function runTests() {
    console.log('🧪 Running integration tests...\n');

    try {
        // Create a temporary Jest config for integration tests
        const jestConfig = {
            testMatch: TEST_CONFIG.testFiles.map(file => `<rootDir>/${file}`),
            collectCoverage: true,
            coverageDirectory: 'coverage/integration',
            coverageReporters: ['text', 'lcov', 'html'],
            collectCoverageFrom: [
                'src/services/firebase/collectionPaths.js',
                'src/redux/features/metaBusinessChatSlice.js',
                'src/services/comments/index.js',
                'src/utils/firebase/dataMigration.js'
            ],
            coverageThreshold: {
                global: {
                    branches: TEST_CONFIG.coverageThreshold,
                    functions: TEST_CONFIG.coverageThreshold,
                    lines: TEST_CONFIG.coverageThreshold,
                    statements: TEST_CONFIG.coverageThreshold
                }
            },
            testEnvironment: 'jsdom',
            setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
            moduleNameMapping: {
                '^@/(.*)$': '<rootDir>/src/$1'
            }
        };

        // Write temporary Jest config
        fs.writeFileSync('jest.integration.config.js',
            `module.exports = ${JSON.stringify(jestConfig, null, 2)};`
        );

        // Run Jest with the integration config
        const jestCommand = `npx jest --config=jest.integration.config.js --verbose --no-cache`;

        console.log(`Running: ${jestCommand}\n`);

        const output = execSync(jestCommand, {
            encoding: 'utf8',
            stdio: 'pipe'
        });

        console.log(output);

        // Clean up temporary config
        if (fs.existsSync('jest.integration.config.js')) {
            fs.unlinkSync('jest.integration.config.js');
        }

        return {
            success: true,
            output
        };

    } catch (error) {
        console.error('❌ Test execution failed:');
        console.error(error.stdout || error.message);

        // Clean up temporary config
        if (fs.existsSync('jest.integration.config.js')) {
            fs.unlinkSync('jest.integration.config.js');
        }

        return {
            success: false,
            error: error.stdout || error.message
        };
    }
}

/**
 * Validate implementation against requirements
 */
function validateRequirements() {
    console.log('📋 Validating requirements coverage...\n');

    const implementationFiles = [
        'src/services/firebase/collectionPaths.js',
        'src/redux/features/metaBusinessChatSlice.js',
        'src/services/comments/index.js'
    ];

    const requirementValidation = {
        covered: [],
        missing: [],
        details: {}
    };

    // Check if key implementation files exist
    implementationFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`✅ Implementation file exists: ${file}`);

            const content = fs.readFileSync(file, 'utf8');

            // Check for key implementation patterns
            const patterns = {
                'getChatIdentifier': /getChatIdentifier/g,
                'getCollectionPaths': /getCollectionPaths/g,
                'getDualWritePaths': /getDualWritePaths/g,
                'determineChatType': /determineChatType/g,
                'dual write logic': /dual.*write|DUAL_WRITE/gi,
                'fallback read': /fallback.*read|legacy.*path/gi,
                'chat ID consistency': /chat.*id|chatId/gi
            };

            Object.entries(patterns).forEach(([pattern, regex]) => {
                const matches = content.match(regex);
                if (matches) {
                    console.log(`   ✅ ${pattern}: ${matches.length} occurrences`);
                } else {
                    console.log(`   ⚠️  ${pattern}: not found`);
                }
            });
        } else {
            console.log(`❌ Missing implementation file: ${file}`);
        }
    });

    // Map requirements to implementation evidence
    TEST_CONFIG.requirements.forEach(requirement => {
        const reqId = requirement.split(' - ')[0];
        const reqDescription = requirement.split(' - ')[1];

        // This is a simplified validation - in a real scenario, you'd have more sophisticated checks
        const covered = implementationFiles.some(file => fs.existsSync(file));

        if (covered) {
            requirementValidation.covered.push(requirement);
            console.log(`✅ ${reqId}: ${reqDescription}`);
        } else {
            requirementValidation.missing.push(requirement);
            console.log(`❌ ${reqId}: ${reqDescription}`);
        }
    });

    console.log(`\n📊 Requirements Coverage:`);
    console.log(`   Covered: ${requirementValidation.covered.length}/${TEST_CONFIG.requirements.length}`);
    console.log(`   Coverage: ${Math.round((requirementValidation.covered.length / TEST_CONFIG.requirements.length) * 100)}%\n`);

    return requirementValidation;
}

/**
 * Generate integration test report
 */
function generateReport(testFileValidation, testResults, requirementValidation) {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            testFiles: {
                total: TEST_CONFIG.testFiles.length,
                valid: testFileValidation.valid,
                invalid: testFileValidation.invalid,
                missing: testFileValidation.missing
            },
            testExecution: {
                success: testResults.success,
                hasOutput: !!testResults.output
            },
            requirements: {
                total: TEST_CONFIG.requirements.length,
                covered: requirementValidation.covered.length,
                missing: requirementValidation.missing.length,
                coveragePercentage: Math.round((requirementValidation.covered.length / TEST_CONFIG.requirements.length) * 100)
            }
        },
        details: {
            testFiles: testFileValidation.details,
            requirements: {
                covered: requirementValidation.covered,
                missing: requirementValidation.missing
            }
        },
        recommendations: []
    };

    // Generate recommendations
    if (testFileValidation.missing > 0) {
        report.recommendations.push('Create missing test files to improve coverage');
    }

    if (testFileValidation.invalid > 0) {
        report.recommendations.push('Fix invalid test file structures');
    }

    if (!testResults.success) {
        report.recommendations.push('Fix failing tests before deployment');
    }

    if (requirementValidation.missing.length > 0) {
        report.recommendations.push('Implement missing requirements');
    }

    // Write report to file
    const reportPath = 'integration-test-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`📄 Integration test report saved to: ${reportPath}\n`);

    return report;
}

/**
 * Main execution function
 */
function main() {
    console.log('🚀 Firebase Collection Consistency - Integration Test Runner\n');
    console.log('='.repeat(70) + '\n');

    try {
        // Step 1: Validate test files
        const testFileValidation = validateTestFiles();

        // Step 2: Run tests
        const testResults = runTests();

        // Step 3: Validate requirements
        const requirementValidation = validateRequirements();

        // Step 4: Generate report
        const report = generateReport(testFileValidation, testResults, requirementValidation);

        // Step 5: Final summary
        console.log('🎯 Final Integration Test Summary:');
        console.log('='.repeat(40));
        console.log(`Test Files: ${report.summary.testFiles.valid}/${report.summary.testFiles.total} valid`);
        console.log(`Test Execution: ${testResults.success ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`Requirements: ${report.summary.requirements.coveragePercentage}% covered`);

        if (report.recommendations.length > 0) {
            console.log('\n📝 Recommendations:');
            report.recommendations.forEach(rec => console.log(`   • ${rec}`));
        }

        // Determine overall success
        const overallSuccess = testFileValidation.missing === 0 &&
            testFileValidation.invalid === 0 &&
            testResults.success &&
            requirementValidation.missing.length === 0;

        console.log(`\n🏆 Overall Status: ${overallSuccess ? '✅ SUCCESS' : '❌ NEEDS ATTENTION'}\n`);

        // Exit with appropriate code
        process.exit(overallSuccess ? 0 : 1);

    } catch (error) {
        console.error('💥 Integration test runner failed:', error.message);
        process.exit(1);
    }
}

// Run if this script is executed directly
if (require.main === module) {
    main();
}

module.exports = {
    validateTestFiles,
    runTests,
    validateRequirements,
    generateReport,
    TEST_CONFIG
};
