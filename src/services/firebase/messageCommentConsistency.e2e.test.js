/**
 * End-to-end tests for message and comment consistency
 * Tests the integration between message and comment systems using shared chat ID
 */

import { configureStore } from '@reduxjs/toolkit';
import metaBusinessChatSlice from '../../redux/features/metaBusinessChatSlice';

// Mock Firebase Firestore
jest.mock('firebase/firestore', () => ({
    collection: jest.fn(),
    doc: jest.fn(),
    setDoc: jest.fn(),
    getDocs: jest.fn(),
    onSnapshot: jest.fn(),
    query: jest.fn(),
    orderBy: jest.fn(),
    where: jest.fn(),
    addDoc: jest.fn(),
    updateDoc: jest.fn(),
    deleteDoc: jest.fn(),
    getDoc: jest.fn()
}));

// Mock Firebase config
jest.mock('../../utils/firebase.config', () => ({
    db: {}
}));

// Mock meta service
jest.mock('../../services/integrations/meta', () => ({
    default: {
        sendMessageApi: jest.fn(),
        getMessagesForChatApi: jest.fn()
    }
}));

// Mock comment services
jest.mock('../comments/index', () => ({
    addComment: jest.fn(),
    getComments: jest.fn(),
    updateComment: jest.fn(),
    deleteComment: jest.fn()
}));

// Mock collection paths service
jest.mock('./collectionPaths', () => ({
    getDualWritePaths: jest.fn(),
    determineChatType: jest.fn(),
    getChatIdentifier: jest.fn(),
    getCollectionPaths: jest.fn()
}));

import {
    collection,
    doc,
    setDoc,
    getDocs,
    onSnapshot,
    query,
    orderBy,
    addDoc
} from 'firebase/firestore';

import metaService from '../../services/integrations/meta';
import { addComment, getComments } from '../comments/index';
import {
    getDualWritePaths,
    determineChatType,
    getChatIdentifier,
    getCollectionPaths
} from './collectionPaths';

import { sendMessage } from '../../redux/features/metaBusinessChatSlice';

describe('Message and Comment Consistency E2E Tests', () => {
    let store;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                metaBusinessSuite: metaBusinessChatSlice
            }
        });
        jest.clearAllMocks();
    });

    describe('Shared Chat ID Usage', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockSelectedPage = {
            id: 'page_123',
            page_token: 'token_123'
        };

        const sharedChatId = 'backend_chat_123';

        beforeEach(() => {
            // Mock collection path resolution to return consistent paths
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });
            getDualWritePaths.mockReturnValue({
                current: {
                    messages: `chats/${sharedChatId}/messages`,
                    comments: `chats/${sharedChatId}/comments`
                },
                legacy: {
                    messages: 'chats/participant_456/messages',
                    comments: `chats/${sharedChatId}/comments`
                },
                chatId: sharedChatId,
                requiresMigration: true
            });
        });

        test('messages and comments use the same chat ID for collection paths', async () => {
            // Send a message
            metaService.sendMessageApi.mockResolvedValue({
                message_id: 'api_msg_123'
            });
            setDoc.mockResolvedValue();

            const messageAction = sendMessage({
                message: 'Hello World',
                selectedChat: mockSelectedChat,
                selectedPage: mockSelectedPage
            });

            await store.dispatch(messageAction);

            // Add a comment
            addComment.mockResolvedValue({
                id: 'comment_123',
                text: 'This is a comment',
                chatId: sharedChatId
            });

            await addComment({
                chatId: sharedChatId,
                text: 'This is a comment',
                userId: 'user_123'
            });

            // Verify both operations used the same chat ID
            expect(getChatIdentifier).toHaveBeenCalledWith(mockSelectedChat, 'messenger');
            expect(getCollectionPaths).toHaveBeenCalledWith(
                sharedChatId,
                'messenger',
                mockSelectedChat
            );

            // Verify message was written to correct path
            expect(doc).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                sharedChatId,
                'messages',
                expect.any(String)
            );

            // Verify comment service was called with the same chat ID
            expect(addComment).toHaveBeenCalledWith(
                expect.objectContaining({
                    chatId: sharedChatId
                })
            );
        });

        test('real-time listeners for messages and comments use consistent paths', async () => {
            const mockMessageDocs = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello',
                        sender: sharedChatId,
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            const mockCommentDocs = [
                {
                    id: 'comment1',
                    data: () => ({
                        text: 'Comment text',
                        chatId: sharedChatId,
                        created_at: '2024-01-01T10:01:00Z'
                    })
                }
            ];

            // Mock message listener
            onSnapshot.mockImplementation((query, callback) => {
                callback({
                    docs: mockMessageDocs,
                    docChanges: () => [{ type: 'added', doc: mockMessageDocs[0] }]
                });
                return jest.fn();
            });

            // Mock comment listener
            getComments.mockImplementation((chatId, callback) => {
                callback(mockCommentDocs.map(doc => ({ id: doc.id, ...doc.data() })));
                return jest.fn();
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Set up message listener
            metaService.getMessagesForChatApi.mockResolvedValue([]);
            getDocs.mockResolvedValue({ docs: [] });

            // Set up state for message fetching
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const { fetchMessages } = require('../../redux/features/metaBusinessChatSlice');
            await store.dispatch(fetchMessages({ thread: mockSelectedChat }));

            // Set up comment listener
            await getComments(sharedChatId, jest.fn());

            // Verify both listeners used the same chat ID in their collection paths
            expect(collection).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                sharedChatId,
                'messages'
            );

            expect(getComments).toHaveBeenCalledWith(
                sharedChatId,
                expect.any(Function)
            );
        });

        test('cross-system queries work with shared chat ID', async () => {
            // Mock data from both systems
            const mockMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello',
                        sender: sharedChatId,
                        created_time: '2024-01-01T10:00:00Z',
                        type: 'text'
                    })
                }
            ];

            const mockComments = [
                {
                    id: 'comment1',
                    text: 'This is a comment',
                    chatId: sharedChatId,
                    created_at: '2024-01-01T10:01:00Z',
                    userId: 'user_123'
                }
            ];

            getDocs.mockResolvedValue({ docs: mockMessages });
            getComments.mockResolvedValue(mockComments);

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Fetch messages
            metaService.getMessagesForChatApi.mockResolvedValue([]);
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const { fetchMessages } = require('../../redux/features/metaBusinessChatSlice');
            const messageResult = await store.dispatch(fetchMessages({ thread: mockSelectedChat }));

            // Fetch comments
            const commentResult = await getComments(sharedChatId);

            // Verify both operations used the same chat ID
            expect(messageResult.payload.activeCollectionPath).toContain(sharedChatId);
            expect(getComments).toHaveBeenCalledWith(sharedChatId);

            // Verify data consistency
            expect(messageResult.payload.messages).toHaveLength(1);
            expect(commentResult).toHaveLength(1);
            expect(commentResult[0].chatId).toBe(sharedChatId);
        });
    });

    describe('WhatsApp Consistency', () => {
        const mockWhatsAppChat = {
            sender_phone_number: '+1234567890'
        };

        const normalizedPhoneNumber = '1234567890';

        beforeEach(() => {
            determineChatType.mockReturnValue('whatsapp');
            getChatIdentifier.mockReturnValue(normalizedPhoneNumber);
            getCollectionPaths.mockReturnValue({
                messages: `whatsApp/${normalizedPhoneNumber}/messages`,
                comments: `whatsApp/${normalizedPhoneNumber}/comments`
            });
        });

        test('WhatsApp messages and comments use consistent phone number normalization', async () => {
            // Test message storage
            metaService.sendMessageApi.mockResolvedValue({
                message_id: 'whatsapp_msg_123'
            });
            setDoc.mockResolvedValue();

            // Mock WhatsApp message sending (would be implemented in actual WhatsApp slice)
            const mockWhatsAppSendMessage = async (message, chat) => {
                const chatId = getChatIdentifier(chat, 'whatsapp');
                const { messages: messagesPath } = getCollectionPaths(chatId, 'whatsapp', chat);

                // Simulate writing to Firebase
                const messageDoc = doc({}, ...messagesPath.split('/'), 'msg_id');
                await setDoc(messageDoc, {
                    message: message,
                    sender: chatId,
                    created_time: new Date().toISOString(),
                    type: 'text'
                });

                return { success: true, path: messagesPath };
            };

            const messageResult = await mockWhatsAppSendMessage('Hello WhatsApp', mockWhatsAppChat);

            // Test comment addition
            addComment.mockResolvedValue({
                id: 'whatsapp_comment_123',
                text: 'WhatsApp comment',
                chatId: normalizedPhoneNumber
            });

            await addComment({
                chatId: normalizedPhoneNumber,
                text: 'WhatsApp comment',
                userId: 'user_123'
            });

            // Verify both used the same normalized phone number
            expect(getChatIdentifier).toHaveBeenCalledWith(mockWhatsAppChat, 'whatsapp');
            expect(messageResult.path).toBe(`whatsApp/${normalizedPhoneNumber}/messages`);
            expect(addComment).toHaveBeenCalledWith(
                expect.objectContaining({
                    chatId: normalizedPhoneNumber
                })
            );
        });

        test('WhatsApp phone number normalization handles various formats consistently', async () => {
            const phoneNumberVariations = [
                '****** 567 8900',
                '1234567890',
                '******-567-8900',
                '  +1234567890  '
            ];

            const expectedNormalized = '12345678900';

            for (const phoneNumber of phoneNumberVariations) {
                const chat = { sender_phone_number: phoneNumber };

                getChatIdentifier.mockReturnValue(expectedNormalized);
                getCollectionPaths.mockReturnValue({
                    messages: `whatsApp/${expectedNormalized}/messages`,
                    comments: `whatsApp/${expectedNormalized}/comments`
                });

                const chatId = getChatIdentifier(chat, 'whatsapp');
                const paths = getCollectionPaths(chatId, 'whatsapp', chat);

                expect(chatId).toBe(expectedNormalized);
                expect(paths.messages).toBe(`whatsApp/${expectedNormalized}/messages`);
                expect(paths.comments).toBe(`whatsApp/${expectedNormalized}/comments`);
            }
        });
    });

    describe('Migration Consistency', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const sharedChatId = 'backend_chat_123';

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getDualWritePaths.mockReturnValue({
                current: {
                    messages: `chats/${sharedChatId}/messages`,
                    comments: `chats/${sharedChatId}/comments`
                },
                legacy: {
                    messages: 'chats/participant_456/messages',
                    comments: `chats/${sharedChatId}/comments`
                },
                chatId: sharedChatId,
                requiresMigration: true
            });
        });

        test('during migration, messages and comments maintain consistency', async () => {
            // Mock dual write scenario
            metaService.sendMessageApi.mockResolvedValue({
                message_id: 'api_msg_123'
            });
            setDoc.mockResolvedValue();

            // Send message during migration (should write to both paths)
            const messageAction = sendMessage({
                message: 'Migration test message',
                selectedChat: mockSelectedChat,
                selectedPage: { id: 'page_123', page_token: 'token_123' }
            });

            await store.dispatch(messageAction);

            // Add comment (should use new consistent path)
            addComment.mockResolvedValue({
                id: 'comment_123',
                text: 'Migration test comment',
                chatId: sharedChatId
            });

            await addComment({
                chatId: sharedChatId,
                text: 'Migration test comment',
                userId: 'user_123'
            });

            // Verify message was written to both legacy and current paths
            expect(setDoc).toHaveBeenCalledTimes(2);

            // Verify comment used the consistent chat ID
            expect(addComment).toHaveBeenCalledWith(
                expect.objectContaining({
                    chatId: sharedChatId
                })
            );

            // Both should reference the same logical chat
            const messageDocCalls = doc.mock.calls;
            const currentPathCall = messageDocCalls.find(call =>
                call[1] === 'chats' && call[2] === sharedChatId && call[3] === 'messages'
            );
            const legacyPathCall = messageDocCalls.find(call =>
                call[1] === 'chats' && call[2] === 'participant_456' && call[3] === 'messages'
            );

            expect(currentPathCall).toBeDefined();
            expect(legacyPathCall).toBeDefined();
        });

        test('fallback reads maintain consistency with comment system', async () => {
            // Mock scenario where new path is empty, legacy path has messages
            const mockLegacyMessages = [
                {
                    id: 'legacy_msg1',
                    data: () => ({
                        message: 'Legacy message',
                        sender: 'participant_456',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            const mockComments = [
                {
                    id: 'comment1',
                    text: 'Comment on legacy message',
                    chatId: sharedChatId,
                    created_at: '2024-01-01T10:01:00Z'
                }
            ];

            // Mock message fallback read
            getDocs.mockResolvedValueOnce({ docs: [] }) // New path empty
                .mockResolvedValueOnce({ docs: mockLegacyMessages }); // Legacy path has data

            // Mock comment read
            getComments.mockResolvedValue(mockComments);

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Fetch messages (should fall back to legacy path)
            metaService.getMessagesForChatApi.mockResolvedValue([]);
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const { fetchMessages } = require('../../redux/features/metaBusinessChatSlice');
            const messageResult = await store.dispatch(fetchMessages({ thread: mockSelectedChat }));

            // Fetch comments (should use consistent chat ID)
            const commentResult = await getComments(sharedChatId);

            // Verify messages were read from legacy path
            expect(messageResult.payload.activeCollectionPath).toBe('chats/participant_456/messages');
            expect(messageResult.payload.messages).toHaveLength(1);

            // Verify comments were read using consistent chat ID
            expect(getComments).toHaveBeenCalledWith(sharedChatId);
            expect(commentResult).toHaveLength(1);
            expect(commentResult[0].chatId).toBe(sharedChatId);

            // Both should logically refer to the same chat despite different collection paths
            expect(messageResult.payload.messages[0].from.id).toBe('participant_456');
            expect(commentResult[0].chatId).toBe(sharedChatId);
        });
    });

    describe('Error Handling Consistency', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const sharedChatId = 'backend_chat_123';

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });
        });

        test('handles Firebase errors consistently across message and comment systems', async () => {
            // Mock Firebase errors
            const firebaseError = new Error('Firebase connection failed');
            setDoc.mockRejectedValue(firebaseError);
            addComment.mockRejectedValue(firebaseError);

            // Try to send message
            metaService.sendMessageApi.mockResolvedValue({
                message_id: 'api_msg_123'
            });

            const messageAction = sendMessage({
                message: 'Test message',
                selectedChat: mockSelectedChat,
                selectedPage: { id: 'page_123', page_token: 'token_123' }
            });

            const messageResult = await store.dispatch(messageAction);

            // Try to add comment
            let commentError;
            try {
                await addComment({
                    chatId: sharedChatId,
                    text: 'Test comment',
                    userId: 'user_123'
                });
            } catch (error) {
                commentError = error;
            }

            // Both should handle errors gracefully
            expect(messageResult.type).toBe('metaBusinessSuite/sendMessage/rejected');
            expect(commentError).toBeDefined();
            expect(commentError.message).toBe('Firebase connection failed');

            // Both should have attempted to use the same chat ID
            expect(getChatIdentifier).toHaveBeenCalledWith(mockSelectedChat, 'messenger');
        });

        test('handles malformed chat objects consistently', async () => {
            const malformedChat = {}; // Missing required fields

            determineChatType.mockReturnValue(null);
            getChatIdentifier.mockReturnValue(null);

            // Try to send message with malformed chat
            const messageAction = sendMessage({
                message: 'Test message',
                selectedChat: malformedChat,
                selectedPage: { id: 'page_123', page_token: 'token_123' }
            });

            const messageResult = await store.dispatch(messageAction);

            // Try to add comment with null chat ID
            let commentError;
            try {
                await addComment({
                    chatId: null,
                    text: 'Test comment',
                    userId: 'user_123'
                });
            } catch (error) {
                commentError = error;
            }

            // Both should handle malformed input gracefully
            expect(messageResult.type).toBe('metaBusinessSuite/sendMessage/rejected');
            expect(commentError).toBeDefined();

            // Both should have attempted to determine chat type/ID
            expect(determineChatType).toHaveBeenCalledWith(malformedChat);
            expect(getChatIdentifier).toHaveBeenCalledWith(malformedChat, null);
        });
    });

    describe('Performance and Scalability', () => {
        test('concurrent operations on same chat maintain consistency', async () => {
            const mockSelectedChat = {
                id: 'backend_chat_123',
                participants: {
                    data: [{ id: 'participant_456' }]
                }
            };

            const sharedChatId = 'backend_chat_123';

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });
            getDualWritePaths.mockReturnValue({
                current: {
                    messages: `chats/${sharedChatId}/messages`,
                    comments: `chats/${sharedChatId}/comments`
                },
                legacy: {
                    messages: 'chats/participant_456/messages',
                    comments: `chats/${sharedChatId}/comments`
                },
                chatId: sharedChatId,
                requiresMigration: true
            });

            metaService.sendMessageApi.mockResolvedValue({
                message_id: 'api_msg_123'
            });
            setDoc.mockResolvedValue();
            addComment.mockResolvedValue({
                id: 'comment_123',
                text: 'Concurrent comment',
                chatId: sharedChatId
            });

            // Perform concurrent operations
            const operations = [
                store.dispatch(sendMessage({
                    message: 'Message 1',
                    selectedChat: mockSelectedChat,
                    selectedPage: { id: 'page_123', page_token: 'token_123' }
                })),
                store.dispatch(sendMessage({
                    message: 'Message 2',
                    selectedChat: mockSelectedChat,
                    selectedPage: { id: 'page_123', page_token: 'token_123' }
                })),
                addComment({
                    chatId: sharedChatId,
                    text: 'Comment 1',
                    userId: 'user_123'
                }),
                addComment({
                    chatId: sharedChatId,
                    text: 'Comment 2',
                    userId: 'user_123'
                })
            ];

            const results = await Promise.all(operations);

            // All operations should succeed
            expect(results[0].type).toBe('metaBusinessSuite/sendMessage/fulfilled');
            expect(results[1].type).toBe('metaBusinessSuite/sendMessage/fulfilled');
            expect(results[2].id).toBe('comment_123');
            expect(results[3].id).toBe('comment_123');

            // All should have used the same chat ID
            expect(getChatIdentifier).toHaveBeenCalledWith(mockSelectedChat, 'messenger');
            expect(addComment).toHaveBeenCalledWith(
                expect.objectContaining({ chatId: sharedChatId })
            );

            // Should have made appropriate Firebase calls
            expect(setDoc).toHaveBeenCalledTimes(4); // 2 messages × 2 paths each
            expect(addComment).toHaveBeenCalledTimes(2);
        });
    });
});
