/**
 * Hybrid Comments Service
 * Combines backend API (for persistence/history) with Firebase (for real-time)
 */

import {
    getCommentsFromBackend,
    sendCommentToBackend,
    markCommentsAsReadInBackend
} from './backendApi';

import {
    addComment as addCommentToFirebase,
    listenToComments as listenToFirebaseComments
} from './index';

/**
 * Clear Firebase comments for a specific chat (like messages do)
 * This ensures a clean slate when switching chats or opening modals
 * @param {string} firebaseSenderId - The Firebase sender identifier for the path
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier
 */
export const clearFirebaseComments = async (firebaseSenderId, chatType, pageId = null) => {
    try {
        console.log('[CLEAR_FIREBASE_COMMENTS] Clearing Firebase comments for:', {
            firebaseSenderId,
            chatType,
            pageId
        });

        const { getCollectionPaths } = await import('../firebase/collectionPaths');

        // Create a mock selectedChat object with the sender ID
        const mockSelectedChat = {
            id: firebaseSenderId,
            participants: {
                data: [{ id: firebaseSenderId }]
            }
        };

        const paths = getCollectionPaths(mockSelectedChat, chatType, pageId);
        if (!paths.comments) {
            console.warn('[CLEAR_FIREBASE_COMMENTS] Could not determine comments path');
            return;
        }

        const { collection, getDocs, deleteDoc } = await import('firebase/firestore');
        const { db } = await import('../../utils/firebase.config');

        // Get all comments in the collection
        const commentsRef = collection(db, ...paths.comments.split('/'));
        const snapshot = await getDocs(commentsRef);

        console.log(`[CLEAR_FIREBASE_COMMENTS] Found ${snapshot.docs.length} Firebase comments to clear`);

        // Delete all Firebase comments
        const deletePromises = snapshot.docs.map(doc => deleteDoc(doc.ref));
        await Promise.all(deletePromises);

        console.log(`[CLEAR_FIREBASE_COMMENTS] Successfully cleared ${snapshot.docs.length} Firebase comments`);
    } catch (error) {
        console.error('[CLEAR_FIREBASE_COMMENTS] Error clearing Firebase comments:', error);
        // Don't throw - this is a cleanup operation and shouldn't break the flow
    }
};

/**
 * Clear only Firebase messages for a chat (keep comments for persistence)
 * @param {Object} thread - The chat thread object
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier
 */
export const clearFirebaseMessages = async (thread, chatType, pageId = null) => {
    try {
        console.log('🔥 [CLEAR_FIREBASE_MESSAGES] Starting Firebase messages cleanup for:', {
            threadId: thread?.id,
            senderPhone: thread?.sender_phone_number,
            chatType,
            pageId,
            threadKeys: Object.keys(thread || {}),
            thread: thread
        });

        // Separate imports to avoid circular dependency issues
        const { collection, getDocs, deleteDoc } = await import('firebase/firestore');
        const { db } = await import('../../utils/firebase.config');
        const { getSenderId } = await import('../firebase/collectionPaths');

        // Get sender ID from thread
        const senderId = getSenderId(thread, chatType);

        if (!senderId) {
            console.warn('🚫 [CLEAR_FIREBASE_MESSAGES] No sender ID found, skipping cleanup');
            return;
        }

        if (!pageId) {
            console.warn('🚫 [CLEAR_FIREBASE_MESSAGES] No page ID provided, skipping cleanup');
            return;
        }

        console.log('🔍 [CLEAR_FIREBASE_MESSAGES] Clearing messages for:', {
            senderId,
            chatType,
            pageId
        });

        // Clear Firebase messages only
        const messagesPath = `/pages/${pageId}/chats/${senderId}/messages`;
        console.log('🗂️ [CLEAR_FIREBASE_MESSAGES] Messages collection path:', messagesPath);

        const messagesCollection = collection(db, `pages/${pageId}/chats/${senderId}/messages`);
        const messagesSnapshot = await getDocs(messagesCollection);

        console.log(`📊 [CLEAR_FIREBASE_MESSAGES] Found ${messagesSnapshot.size} messages to delete`);

        // Delete all messages
        const messageDeletePromises = messagesSnapshot.docs.map(doc => {
            console.log(`🗑️ [CLEAR_FIREBASE_MESSAGES] Deleting message: ${doc.id}`);
            return deleteDoc(doc.ref);
        });

        await Promise.all(messageDeletePromises);
        console.log(`✅ [CLEAR_FIREBASE_MESSAGES] Successfully deleted ${messagesSnapshot.size} messages`);

    } catch (error) {
        console.error('❌ [CLEAR_FIREBASE_MESSAGES] Error clearing Firebase messages:', error);
        throw error;
    }
};

/**
 * Clear both Firebase messages and comments for a specific chat
 * This is used when switching chats to ensure a clean slate for both messages and comments
 * @param {Object} thread - The chat thread object
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier
 */
export const clearFirebaseMessagesAndComments = async (thread, chatType, pageId = null) => {
    try {
        console.log('🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Starting Firebase cleanup for:', {
            threadId: thread?.id,
            senderPhone: thread?.sender_phone_number,
            chatType,
            pageId,
            threadKeys: Object.keys(thread || {}),
            thread: thread
        });

        const { getCollectionPaths, determineChatType } = await import('../firebase/collectionPaths');
        const { collection, getDocs, deleteDoc } = await import('firebase/firestore');
        const { db } = await import('../../utils/firebase.config');

        // Determine chat type if not provided
        const actualChatType = chatType || determineChatType(thread);
        console.log('🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Determined chat type:', actualChatType);

        const paths = getCollectionPaths(thread, actualChatType, pageId);
        console.log('🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Generated paths:', paths);

        // Clear messages
        if (paths.messages) {
            try {
                console.log('🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Clearing messages from path:', paths.messages);
                const pathSegments = paths.messages.split('/');
                console.log('🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Path segments for messages:', pathSegments);
                const messagesRef = collection(db, ...pathSegments);
                const messagesSnapshot = await getDocs(messagesRef);

                console.log(`🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Found ${messagesSnapshot.docs.length} messages to clear`);

                const messageDeletePromises = messagesSnapshot.docs.map(doc => deleteDoc(doc.ref));
                if (messageDeletePromises.length > 0) {
                    await Promise.all(messageDeletePromises);
                    console.log(`✅ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Successfully cleared ${messageDeletePromises.length} messages`);
                } else {
                    console.log('ℹ️ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] No messages to clear');
                }
            } catch (error) {
                console.error('❌ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Error clearing messages:', error);
                console.error('❌ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] DB object:', db);
                console.error('❌ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Path segments:', paths.messages.split('/'));
            }
        } else {
            console.log('⚠️ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] No messages path generated');
        }

        // Clear comments
        if (paths.comments) {
            try {
                console.log('🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Clearing comments from path:', paths.comments);
                const pathSegments = paths.comments.split('/');
                console.log('🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Path segments for comments:', pathSegments);
                const commentsRef = collection(db, ...pathSegments);
                const commentsSnapshot = await getDocs(commentsRef);

                console.log(`🔥 [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Found ${commentsSnapshot.docs.length} comments to clear`);

                const commentDeletePromises = commentsSnapshot.docs.map(doc => deleteDoc(doc.ref));
                if (commentDeletePromises.length > 0) {
                    await Promise.all(commentDeletePromises);
                    console.log(`✅ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Successfully cleared ${commentDeletePromises.length} comments`);
                } else {
                    console.log('ℹ️ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] No comments to clear');
                }
            } catch (error) {
                console.error('❌ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Error clearing comments:', error);
                console.error('❌ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] DB object:', db);
                console.error('❌ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Path segments:', paths.comments.split('/'));
            }
        } else {
            console.log('⚠️ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] No comments path generated');
        }

        console.log('✅ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Firebase cleanup completed');
    } catch (error) {
        console.error('❌ [CLEAR_FIREBASE_MESSAGES_AND_COMMENTS] Error during Firebase cleanup:', error);
        // Don't throw - this is a cleanup operation and shouldn't break the flow
    }
};

/**
 * Load comments for a chat (hybrid approach)
 * 1. Load history from backend using chat ID
 * 2. Set up Firebase listener for new real-time comments using sender ID path
 * @param {string} backendChatId - The chat ID from backend/chats endpoint
 * @param {string} firebaseSenderId - The Firebase sender identifier for real-time path
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {Function} onCommentsUpdate - Callback for comment updates
 * @param {string} pageId - The page identifier
 * @param {Object} selectedChat - The selected chat object for Firebase paths
 * @returns {Promise<{comments: Array, unsubscribe: Function}>}
 */
export const loadChatComments = async (backendChatId, firebaseSenderId, chatType, onCommentsUpdate, pageId = null, selectedChat = null) => {
    try {
        // Step 1: Load comment history from backend using chat ID
        console.log('Loading comments for backendChatId:', backendChatId);
        const backendComments = await getCommentsFromBackend(backendChatId);
        console.log('Backend comments loaded:', backendComments);

        // Step 2: Set up Firebase listener for new real-time comments using sender ID path
        // This uses the same path structure as messages: ${pageId}/${sender}/comments
        const unsubscribe = listenToFirebaseComments(firebaseSenderId, chatType, (firebaseComments) => {
            console.log('Firebase comments received:', firebaseComments);

            // Create a map of Firebase comments by ID for quick lookup
            const firebaseCommentsMap = new Map();
            firebaseComments.forEach(fc => {
                firebaseCommentsMap.set(String(fc.id), fc);
            });

            // Merge backend comments with Firebase read receipts and new comments
            const mergedComments = backendComments.map(backendComment => {
                const firebaseComment = firebaseCommentsMap.get(String(backendComment.id));

                if (firebaseComment && firebaseComment.readBy) {
                    // Merge read receipts from Firebase (real-time) with backend comment
                    return {
                        ...backendComment,
                        readBy: firebaseComment.readBy || backendComment.readBy
                    };
                }

                return backendComment;
            });

            // Add new Firebase comments that don't exist in backend
            const backendCommentIds = new Set(backendComments.map(c => String(c.id)));
            const newFirebaseComments = firebaseComments.filter(fc =>
                !backendCommentIds.has(String(fc.id)) && fc.chatType !== 'backend'
            );

            console.log('[HYBRID_COMMENTS] Merging comments:', {
                backendCount: backendComments.length,
                firebaseCount: firebaseComments.length,
                newFirebaseCount: newFirebaseComments.length,
                backendCommentIds: Array.from(backendCommentIds),
                newFirebaseCommentIds: newFirebaseComments.map(fc => fc.id)
            });

            // Combine merged backend comments with new Firebase comments
            const allComments = [...mergedComments, ...newFirebaseComments];

            // Sort by creation time
            allComments.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

            console.log('All comments merged and sorted:', allComments);
            onCommentsUpdate(allComments);
        }, null, pageId, selectedChat);

        // Initial callback with backend comments
        console.log('Initial callback with backend comments:', backendComments);
        onCommentsUpdate(backendComments);

        return {
            comments: backendComments,
            unsubscribe
        };
    } catch (error) {
        console.error('Error loading chat comments:', error);

        // Fallback: just set up Firebase listener
        const unsubscribe = listenToFirebaseComments(firebaseSenderId, chatType, onCommentsUpdate, null, pageId, selectedChat);
        onCommentsUpdate([]);

        return {
            comments: [],
            unsubscribe
        };
    }
};

/**
 * Send a new comment (hybrid approach)
 * 1. Send to backend for persistence using chat ID
 * 2. Send to Firebase for real-time updates using sender ID path
 * @param {string} backendChatId - The chat ID from backend/chats endpoint
 * @param {string} firebaseSenderId - The Firebase sender identifier for real-time path
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {string} text - The comment text
 * @param {Object} author - The author information
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Promise<Object>} - The created comment
 */
export const sendComment = async (backendChatId, firebaseSenderId, chatType, text, author, pageId = null) => {
    console.log('[SEND_COMMENT] Called with parameters:', {
        backendChatId,
        firebaseSenderId,
        chatType,
        text: text?.substring(0, 50),
        author: author?.id,
        pageId,
        firebaseSenderIdType: typeof firebaseSenderId,
        isFirebaseSenderIdUndefined: firebaseSenderId === undefined
    });

    try {
        // Send to backend first (primary storage) using chat ID
        const backendComment = await sendCommentToBackend(backendChatId, text, author);

        console.log('[SEND_COMMENT] Backend comment created:', {
            backendCommentId: backendComment.id,
            aboutToCallFirebase: true,
            firebaseSenderId,
            chatType,
            pageId
        });

        // Send to Firebase for real-time updates using sender ID path
        try {
            const { addComment: addCommentToFirebase } = await import('./index');

            // Create Firebase comment with same ID as backend comment
            // This uses the sender ID path: ${pageId}/${sender}/comments
            console.log('[SEND_COMMENT] About to call addCommentToFirebase with:', {
                chatId: firebaseSenderId,
                chatType,
                customId: String(backendComment.id),
                pageId
            });

            await addCommentToFirebase(firebaseSenderId, chatType, text, author, null, String(backendComment.id), pageId);

            console.log('[SEND_COMMENT] Firebase comment created successfully');
        } catch (firebaseError) {
            console.warn('Failed to send comment to Firebase (real-time may not work):', firebaseError);
            // Don't throw - backend success is more important
        }

        return backendComment;
    } catch (error) {
        console.error('Error sending comment:', error);
        throw error;
    }
};

/**
 * Mark comments as read (hybrid approach)
 * 1. Mark as read in backend using chat ID
 * 2. Mark as read in Firebase using sender ID path for real-time sync
 * @param {string} backendChatId - The chat ID from backend/chats endpoint
 * @param {string} firebaseSenderId - The Firebase sender identifier for real-time path
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {Array} commentIds - Array of comment IDs (backend IDs)
 * @param {Object} user - User information
 * @returns {Promise<void>}
 */
export const markCommentsAsRead = async (backendChatId, firebaseSenderId, chatType, commentIds, user, pageId = null) => {
    try {
        // Mark as read in backend (primary) using chat ID
        await markCommentsAsReadInBackend(backendChatId, commentIds, user);

        // Also update Firebase for real-time synchronization using sender ID path
        // This ensures other users see updated read receipts immediately
        try {
            const { markCommentsAsRead: markCommentsAsReadInFirebase } = await import('./index');
            await markCommentsAsReadInFirebase(firebaseSenderId, chatType, commentIds, user, null, pageId);
            console.log(`Updated read receipts in Firebase for real-time sync`);
        } catch (firebaseError) {
            console.warn('Failed to update read receipts in Firebase (real-time sync may not work):', firebaseError);
            // Don't throw - backend success is more important
        }

        console.log(`Marked ${commentIds.length} comments as read for chat ${backendChatId}`);
    } catch (error) {
        console.error('Error marking comments as read:', error);
        throw error;
    }
};

/**
 * Get chat IDs for hybrid operations
 * - Backend uses chat ID for API calls
 * - Firebase uses participant/sender ID for real-time listeners (same path as messages)
 *
 * @param {Object} selectedChat - The selected chat object (Messenger/Instagram)
 * @param {Object} selectedWhatsappChat - The selected WhatsApp chat object
 * @param {string} activeFilter - The active filter ('whatsapp' or other)
 * @returns {Object|null} - Object with backendChatId, firebaseSenderId, and chatType
 */
export const getChatIds = (selectedChat, selectedWhatsappChat, activeFilter) => {
    // Import the collection paths service for sender ID resolution
    const { getSenderId, determineChatType } = require('../../services/firebase/collectionPaths');

    console.log('[GET_CHAT_IDS] Called with:', {
        activeFilter,
        hasSelectedChat: !!selectedChat,
        hasSelectedWhatsappChat: !!selectedWhatsappChat,
        selectedChatId: selectedChat?.id,
        selectedChatStructure: selectedChat ? {
            id: selectedChat.id,
            flage: selectedChat.flage,
            participants: selectedChat.participants,
            participantsData: selectedChat.participants?.data
        } : null
    });

    if (activeFilter === "whatsapp" && selectedWhatsappChat) {
        const chatType = 'whatsapp';
        const firebaseSenderId = getSenderId(selectedWhatsappChat, chatType); // Normalized phone number

        console.log('[GET_CHAT_IDS] WhatsApp result:', {
            backendChatId: selectedWhatsappChat.id,
            firebaseSenderId,
            chatType
        });

        return {
            backendChatId: selectedWhatsappChat.id, // Backend chat ID for API calls
            firebaseSenderId: firebaseSenderId, // Firebase sender ID for real-time listeners
            chatType: chatType
        };
    } else if (selectedChat) {
        const chatType = determineChatType(selectedChat);
        const firebaseSenderId = getSenderId(selectedChat, chatType); // Participant ID for Firebase

        console.log('[GET_CHAT_IDS] Messenger/Instagram result:', {
            backendChatId: selectedChat.id,
            firebaseSenderId,
            chatType,
            isFirebaseSenderIdUndefined: firebaseSenderId === undefined
        });

        return {
            backendChatId: selectedChat.id, // Backend chat ID for API calls
            firebaseSenderId: firebaseSenderId, // Firebase sender ID for real-time listeners (participant ID)
            chatType: chatType
        };
    }

    console.log('[GET_CHAT_IDS] No valid chat found, returning null');
    return null;
};
