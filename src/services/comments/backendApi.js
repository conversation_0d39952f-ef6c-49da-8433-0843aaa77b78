/**
 * Backend API service for comments
 * Handles persistent comment storage and history
 */

import apiRequest from '../../utils/apiRequest';

/**
 * Get all comments for a specific chat from backend
 * @param {string} chatId - The chat ID from the chats endpoint
 * @returns {Promise<Array>} - Array of comments from backend
 */
export const getCommentsFromBackend = async (chatId) => {
    try {
        console.log('Fetching comments for chatId:', chatId);
        const response = await apiRequest(
            `chat-commints/${chatId}`,
            'get',
            null,
            {},
            null,
            {},
            null, // No success message
            null  // No error message (will be handled by caller)
        );

        console.log('Backend API response:', response);
        console.log('Response type:', typeof response);
        console.log('Response.data:', response?.data);
        console.log('Is response an array?', Array.isArray(response));

        // Since apiRequest returns response.data, the response itself might be the data
        let commentsData = response;

        // If response has a data property, use that instead
        if (response && response.data) {
            commentsData = response.data;
        }

        // Check if commentsData exists and is an array
        if (!commentsData) {
            console.warn('No comments data found:', response);
            return [];
        }

        if (!Array.isArray(commentsData)) {
            console.warn('Comments data is not an array:', commentsData);
            return [];
        }

        // Transform backend response to match frontend format
        const transformedComments = commentsData.map(comment => ({
            id: comment.id,
            text: comment.comment,
            image: comment.image || null, // Support for image comments
            type: comment.type || 'text', // Support for different comment types
            author: {
                id: comment.user_id ? String(comment.user_id) : null,
                name: comment.user_name || comment.user?.name || (comment.user_id ? 'Unknown User' : 'Anonymous'),
                email: comment.user?.email || null,
                photo: comment.user_photo || comment.user?.photo || null
            },
            createdAt: comment.created_at,
            updatedAt: comment.updated_at,
            readBy: (() => {
                if (!comment.read_by || !Array.isArray(comment.read_by)) return [];

                // Remove duplicates based on user_id
                const uniqueReadBy = [];
                const seenUserIds = new Set();

                comment.read_by.forEach(readStatus => {
                    const userId = readStatus.user_id ? String(readStatus.user_id) : null;
                    if (userId && !seenUserIds.has(userId)) {
                        seenUserIds.add(userId);
                        uniqueReadBy.push({
                            userId,
                            userName: readStatus.user_name || 'Unknown User',
                            userPhoto: readStatus.user_photo || null,
                            readAt: readStatus.created_at
                        });
                    }
                });

                return uniqueReadBy;
            })(),
            chatType: 'backend', // Mark as backend comment
            chatId: comment.chat_id
        }));

        console.log('Backend comments transformed:', transformedComments);
        return transformedComments;
    } catch (error) {
        console.error('Error fetching comments from backend:', error);
        throw error;
    }
};

/**
 * Send a new comment to backend
 * @param {string} chatId - The chat ID from the chats endpoint
 * @param {string} text - The comment text
 * @param {Object} author - The author information
 * @returns {Promise<Object>} - The created comment from backend
 */
export const sendCommentToBackend = async (chatId, text, author) => {
    try {
        const payload = {
            comment: text,
            user_id: author.id,
            user_name: author.name,
            chat_id: chatId
        };

        const response = await apiRequest(
            `chat-commints`,
            'post',
            payload,
            {},
            null,
            {},
            'Comment sent successfully',
            'Failed to send comment'
        );

        // Transform response to match frontend format
        const comment = response.data;
        return {
            id: comment.id,
            text: comment.comment,
            author: {
                id: comment.user_id ? String(comment.user_id) : null,
                name: comment.user_name || 'Unknown User',
                email: null,
                photo: comment.user_photo || null
            },
            createdAt: comment.created_at,
            updatedAt: comment.updated_at,
            readBy: comment.read_by?.map(readStatus => ({
                userId: readStatus.user_id ? String(readStatus.user_id) : null,
                userName: readStatus.user_name || 'Unknown User',
                userPhoto: readStatus.user_photo || null,
                readAt: readStatus.created_at
            })) || [],
            chatType: 'backend',
            chatId: comment.chat_id
        };
    } catch (error) {
        console.error('Error sending comment to backend:', error);
        throw error;
    }
};

/**
 * Mark comments as read in backend
 * @param {string} chatId - The chat ID from the chats endpoint
 * @param {Array<number>} commentIds - Array of comment IDs to mark as read
 * @param {Object} user - User information
 * @returns {Promise<void>}
 */
export const markCommentsAsReadInBackend = async (chatId, commentIds, user) => {
    try {
        console.log('Marking comments as read - User info:', user);
        console.log('Chat ID:', chatId);
        console.log('Comment IDs:', commentIds);

        // Include user information in the payload to ensure proper user name resolution
        const payload = {
            user_id: user.id,
            user_name: user.name,
            comment_ids: commentIds // Include comment IDs if needed by backend
        };

        console.log('Sending payload to update-status:', payload);

        await apiRequest(
            `update-status/${chatId}`,
            'put',
            payload, // Send user data to ensure proper name resolution
            {},
            null,
            {},
            null, // No success message for read receipts
            'Failed to mark comments as read'
        );

        console.log('Successfully marked comments as read');
    } catch (error) {
        console.error('Error marking comments as read in backend:', error);
        throw error;
    }
};

/**
 * Update a comment in backend (if needed for future features)
 * @param {string} chatId - The chat ID from the chats endpoint
 * @param {number} commentId - The comment ID to update
 * @param {string} text - The new comment text
 * @returns {Promise<Object>} - The updated comment
 */
export const updateCommentInBackend = async (chatId, commentId, text) => {
    try {
        const payload = {
            comment: text
        };

        const response = await apiRequest(
            `chat-commints/${chatId}/${commentId}`,
            'put',
            payload,
            {},
            null,
            {},
            'Comment updated successfully',
            'Failed to update comment'
        );

        // Transform response to match frontend format
        const comment = response.data;
        return {
            id: comment.id,
            text: comment.comment,
            author: {
                id: comment.user_id ? String(comment.user_id) : null,
                name: comment.user_name || 'Unknown User',
                email: null,
                photo: comment.user_photo || null
            },
            createdAt: comment.created_at,
            updatedAt: comment.updated_at,
            readBy: comment.read_by?.map(readStatus => ({
                userId: readStatus.user_id ? String(readStatus.user_id) : null,
                userName: readStatus.user_name || 'Unknown User',
                userPhoto: readStatus.user_photo || null,
                readAt: readStatus.created_at
            })) || [],
            chatType: 'backend',
            chatId: comment.chat_id
        };
    } catch (error) {
        console.error('Error updating comment in backend:', error);
        throw error;
    }
};

/**
 * Delete a comment from backend (if needed for future features)
 * @param {string} chatId - The chat ID from the chats endpoint
 * @param {number} commentId - The comment ID to delete
 * @returns {Promise<void>}
 */
export const deleteCommentFromBackend = async (chatId, commentId) => {
    try {
        await apiRequest(
            `chat-commints/${chatId}/${commentId}`,
            'delete',
            null,
            {},
            null,
            {},
            'Comment deleted successfully',
            'Failed to delete comment'
        );
    } catch (error) {
        console.error('Error deleting comment from backend:', error);
        throw error;
    }
};
