import apiRequest from "../../utils/apiRequest";

// Get all ads
const getAllAdsApi = async (records = 10, currentPage = 1) => {
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    return await apiRequest(`data/lead/ads?${params.toString()}`, "get");
};

// Get leads for a specific ad
const getAdLeadsApi = async (adId, records = 10, currentPage = 1) => {
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    return await apiRequest(`data/ads/details/${adId}?${params.toString()}`, "get");
};

export default {
    getAllAdsApi,
    getAdLeadsApi,
};
