import apiRequest from "../../utils/apiRequest";

export const getTotalLeadsStatusAPI = async ({ signal }) => {
  return await apiRequest("reports/leads/totals", "get", null, {}, signal);
};

export const getLeadsSourceAPI = async ({ signal }) => {
  return await apiRequest("reports/leads/source", "get", null, {}, signal);
};

export const getTeamCountAPI = async ({ signal }) => {
  return await apiRequest("reports/leads/teams", "get", null, {}, signal);
};

export const getLeadsFilterTableAPI = async ({ signal, params }) => {
  const { url, recordsPerPage, currentPage, from, to, assigned, source, status, service } = params;

  const buildQueryParams = () => {
    const queryParams = new URLSearchParams();
    if (recordsPerPage) queryParams.append("number_of_records", recordsPerPage);
    if (currentPage) queryParams.append("page", currentPage);
    if (from) queryParams.append("from", from);
    if (to) queryParams.append("to", to);
    if (assigned) queryParams.append("assigned", assigned);
    if (source) queryParams.append("source", source);
    if (status !== null && status !== undefined && status !== "") queryParams.append("status", status);
    if (service) queryParams.append("service", service);

    return queryParams.toString();
  };

  const queryParams = buildQueryParams();
  const endpoint = url ? `${url}&${queryParams}` : `reports/leads/filter?${queryParams}`;

  return await apiRequest(endpoint, "get", null, {}, signal);
};

export const getTeamMembersFilterTableAPI = async ({ signal, params }) => {
  const { url, recordsPerPage, currentPage, from, to, member, role } = params;

  const buildQueryParams = () => {
    const queryParams = new URLSearchParams();
    if (recordsPerPage) queryParams.append("number_of_records", recordsPerPage);
    if (currentPage) queryParams.append("page", currentPage);
    if (from) queryParams.append("from", from);
    if (to) queryParams.append("to", to);
    if (member) queryParams.append("member", member);
    if (role && !member) queryParams.append("role", role);
    if (role && member) queryParams.delete(role);

    return queryParams.toString();
  };

  const queryParams = buildQueryParams();
  const endpoint = url ? `${url}&${queryParams}` : `reports/team/leads?${queryParams}`;

  return await apiRequest(endpoint, "get", null, {}, signal);
};

export const getSalesFilterTableAPI = async ({ signal, params }) => {
  const { url, recordsPerPage, currentPage, from, to, member, status, service } = params;

  const buildQueryParams = () => {
    const queryParams = new URLSearchParams();
    if (recordsPerPage) queryParams.append("number_of_records", recordsPerPage);
    if (currentPage) queryParams.append("page", currentPage);
    if (from) queryParams.append("from", from);
    if (to) queryParams.append("to", to);
    if (member) queryParams.append("member", member);
    if (status !== null && status !== undefined) queryParams.append("status", status);
    if (service) queryParams.append("service", service);

    return queryParams.toString();
  };

  const queryParams = buildQueryParams();
  const endpoint = url ? `${url}&${queryParams}` : `reports/sales/performance?${queryParams}`;

  return await apiRequest(endpoint, "get", null, {}, signal);
};

export const getLeadsPlatformAPI = async ({ signal, params }) => {
  return await apiRequest("reports/leads/platform", "get", null, {}, signal);
};

export const getLeadsServiceAPI = async ({ signal, params }) => {
  return await apiRequest("reports/leads/service", "get", null, {}, signal);
};

export const getTeamMemberStatistics = async ({ signal, params }) => {
  const { url, recordsPerPage, currentPage, from, to, member } = params;

  const buildQueryParams = () => {
    const queryParams = new URLSearchParams();
    if (recordsPerPage) queryParams.append("number_of_records", recordsPerPage);
    if (currentPage) queryParams.append("page", currentPage);
    if (from) queryParams.append("from", from);
    if (to) queryParams.append("to", to);
    if (member) queryParams.append("member", member);

    return queryParams.toString();
  };

  const queryParams = buildQueryParams();
  const endpoint = url ? `${url}&${queryParams}` : `reports/team/statistics?${queryParams}`;

  return await apiRequest(endpoint, "get", null, {}, signal);
};
