import apiRequest from "../../utils/apiRequest";
import { createFormDataWithAttachments, hasAttachments } from "../../utils/handle-attachments";

const getAllTickets = async (url) => {
    // Log the URL to debug
    return await apiRequest(url, "get");
};

const getSingleTicket = async (id) => {
    return await apiRequest(`ticket/${id}`, "get");
};

const createTicket = async (data) => {
    // Check if there are attachments in the data
    if (hasAttachments(data)) {
        // Create FormData with attachments
        const formData = createFormDataWithAttachments(data);

        // Send request with FormData and appropriate headers
        return await apiRequest(
            "ticket",
            "post",
            formData,
            {
                "Content-Type": "multipart/form-data"
            }
        );
    }

    // If no attachments, proceed with regular JSON request
    return await apiRequest("ticket", "post", data);
};

const replyToTicket = async (id, data) => {
    // Check if data is already FormData
    if (data instanceof FormData) {
        // Send request with FormData and appropriate headers
        return await apiRequest(
            `ticket/${id}`,
            "post",
            data,
            {
                "Content-Type": "multipart/form-data"
            }
        );
    }

    // Check if there are attachments in the data
    if (hasAttachments(data)) {
        // Create FormData with attachments
        const formData = createFormDataWithAttachments(data);
        // Send request with FormData and appropriate headers
        return await apiRequest(
            `ticket/${id}`,
            "post",
            formData,
            {
                "Content-Type": "multipart/form-data"
            }
        );
    }

    // If no attachments, proceed with regular JSON request
    return await apiRequest(`ticket/${id}`, "post", data);
};

const searchTickets = async (searchTerm, page = 1, number_of_records = 10, status = null, priority = null) => {
    let url = `ticket?page=${page}&number_of_records=${number_of_records}`;

    if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}`;
    }

    if (status && status !== "All") {
        url += `&status=${encodeURIComponent(status)}`;
    }

    if (priority && priority !== "All") {
        url += `&priority=${encodeURIComponent(priority)}`;
    }

    return await apiRequest(url, "get");
};

export { getAllTickets, getSingleTicket, createTicket, replyToTicket, searchTickets };
