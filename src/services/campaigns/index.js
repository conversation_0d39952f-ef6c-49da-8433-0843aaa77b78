import apiRequest from "../../utils/apiRequest";

// Get all campaigns
const getAllCampaignsApi = async (records = 10, currentPage = 1, filters = {}) => {
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    // Add date filters if provided
    if (filters.startDate) {
        params.append('start_date', filters.startDate);
    }
    if (filters.endDate) {
        params.append('end_date', filters.endDate);
    }

    // Add search filter if provided
    if (filters.search) {
        params.append('search', filters.search);
    }

    return await apiRequest(`campaigns?${params.toString()}`, "get");
};

// Get ads for a specific campaign
const getCampaignAdsApi = async (campaignId, records = 10, currentPage = 1, filters = {}) => {
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    // Add date filters if provided
    if (filters.startDate) {
        params.append('start_date', filters.startDate);
    }
    if (filters.endDate) {
        params.append('end_date', filters.endDate);
    }

    // Add search filter if provided
    if (filters.search) {
        params.append('search', filters.search);
    }

    return await apiRequest(`campaigns/${campaignId}/ads?${params.toString()}`, "get");
};

export default {
    getAllCampaignsApi,
    getCampaignAdsApi,
};
