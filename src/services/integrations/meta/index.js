import apiRequest from "../../../utils/apiRequest";

import { api } from "../../config";

const getChatsForPageApi = async (data) => {
    const url = "chats";
    return await apiRequest(url, "post", data, {
        "Content-Type": "application/json",
    }, data?.signal);
};

const getMessengerChatsForPageApi = async (data) => {
    const url = "facbook-chats";
    return await apiRequest(url, "post", data, {
        "Content-Type": "application/json",
    }, data?.signal);
}

const getInstagramChatsForPageApi = async (data) => {
    const url = "instagram-chats";
    return await apiRequest(url, "post", data, {
        "Content-Type": "application/json",
    }, data?.signal);
}

const getWhatsAppChatsForPageApi = async (data) => {
    const url = `whatsApp-chat-list/${data?.id}`;
    return await apiRequest(url, "get", data?.signal);
}

const getAccessTokenApi = async () => {
    const url = "get/access_token";
    return await apiRequest(url, "post");
};

const getAccountsApi = async (token) => {
    const url = "accounts";
    const data = { token };
    return await apiRequest(url, "post", data);
};

const getFormsApi = async (data) => {
    const url = "forms";
    return await apiRequest(url, "post", data);
};

const getFormsLeadsApi = async (data) => {
    const url = "leads";
    return await apiRequest(url, "post", data);
};

const getPageImgAPI = async (data) => {
    const url = "page_image";
    return await apiRequest(url, "post", data);
};

const getLeadsByPageApi = async (data) => {
    const url = "get_leads_by_page";
    return await apiRequest(url, "post", data);
};

const getMessagesForChatApi = async (data) => {
    const { signal, ...requestData } = data;
    return await apiRequest("messages", "post", requestData, { signal });
};

const getMessagesForWhatsappChatApi = async (data) => {
    const { signal, ...requestData } = data;
    return await apiRequest(`chat-messages/${requestData?.id}`, "get", null, { signal });
}

const getMoreChatsAPI = async (data) => {
    const { signal, ...requestData } = data;
    return await apiRequest("chats_pagination", "post", requestData, { signal });
};

const deleteAccessToken = async () => {
    return await apiRequest("remove-access-token", "post");
};

export const sendMessageApi = async (data) => {
    try {
        // Check if data is already a FormData object
        if (data instanceof FormData) {
            const response = await apiRequest("send-message", "post", data, {
                "Content-Type": "multipart/form-data",
            });

            // Check if the response contains an error
            if (response && response.error) {
                return response;
            }

            return response;
        } else {
            // Handle legacy object format (fallback)
            const formData = new FormData();

            // Add all fields from the data object
            if (data.page_id) formData.append("page_id", data.page_id);
            if (data.access_token) formData.append("access_token", data.access_token);
            if (data.id) formData.append("id", data.id);
            if (data.message) formData.append("message", data.message);
            if (data.type) formData.append("type", data.type);

            // Handle attachment properly
            if (data.attachment && data.attachment instanceof File) {
                formData.append("file", data.attachment);
            }

            const response = await apiRequest("send-message", "post", formData, {
                "Content-Type": "multipart/form-data",
            });

            // Check if the response contains an error
            if (response && response.error) {
                console.log("Error detected in sendMessageApi:", response.error);
                return response;
            }

            return response;
        }
    } catch (error) {
        console.error("Error in sendMessageApi:", error);

        // If the error has a response, return it so it can be handled by the caller
        if (error.response && error.response.data) {
            console.log("Error response data:", error.response.data);
            return error.response.data;
        }

        throw error;
    }
};

const sendWhatsMessageApi = async (data) => {
    // Check if we have both text and file to send them separately
    const hasText = data.get('text');
    const hasFile = data.get('media');
    const id = data.get('id');
    const to = data.get('to');
    const accessToken = data.get('access_token');
    const type = data.get('type');

    // Special handling for audio files from mic-recorder-to-mp3
    if (type === 'audio' && hasFile) {
        // The blob from mic-recorder-to-mp3 is already in MP3 format
        // Just ensure it has the correct filename and MIME type
        const audioFile = new File(
            [hasFile],
            'audio.mp3',
            { type: 'audio/mp3' }
        );

        // Create a new FormData with the properly named MP3 file
        const updatedData = new FormData();
        updatedData.append('id', id);
        updatedData.append('to', to);
        updatedData.append('access_token', accessToken);
        updatedData.append('type', type);
        updatedData.append('media', audioFile);

        // Use the updated FormData for the request
        return await apiRequest(
            "whatsApp-send",
            "post",
            updatedData,
            {
                "Content-Type": "multipart/form-data"
            },
            null,
            {},
            null,
            "Error sending WhatsApp audio message"
        );
    }

    // Handle stickers the same way as other media types
    if (type === 'sticker' && hasFile) {
        // Ensure we're using the correct key names as required
        const formData = new FormData();
        formData.append('id', id);
        formData.append('to', to);
        formData.append('access_token', accessToken);
        formData.append('type', type);
        formData.append('media', hasFile);

        // Use the standard WhatsApp send endpoint
        return await apiRequest(
            "whatsApp-send",
            "post",
            formData,
            {
                "Content-Type": "multipart/form-data"
            },
            null,
            {},
            null,
            "Error sending WhatsApp sticker"
        );
    }

    // Rest of the existing function for non-audio messages
    if (hasText && hasFile) {
        // Step 1: Send text message first
        const textFormData = new FormData();
        textFormData.append('id', id);
        textFormData.append('to', to);
        textFormData.append('text', hasText);
        textFormData.append('access_token', accessToken);
        textFormData.append('type', type);

        // Send text message
        await apiRequest(
            "whatsApp-send",
            "post",
            textFormData,
            {
                "Content-Type": "multipart/form-data"
            },
            null,
            {},
            null,
            "Error sending WhatsApp text message"
        );

        // Step 2: Send file message
        const fileFormData = new FormData();
        fileFormData.append('id', id);
        fileFormData.append('to', to);
        fileFormData.append('access_token', accessToken);
        fileFormData.append('type', type);
        fileFormData.append('media', hasFile);

        // Send file message
        return await apiRequest(
            "whatsApp-send",
            "post",
            fileFormData,
            {
                "Content-Type": "multipart/form-data"
            },
            null,
            {},
            null,
            "Error sending WhatsApp file message"
        );
    } else {
        // If we only have text or file, send a single message
        // Ensure we're using the correct key names as required
        const formData = new FormData();
        formData.append('id', id);
        formData.append('to', to);
        formData.append('access_token', accessToken);
        formData.append('type', type);

        if (hasText) {
            formData.append('text', hasText);
        }

        if (hasFile) {
            formData.append('media', hasFile);
        }

        // Pass custom error message to apiRequest
        return await apiRequest(
            "whatsApp-send",
            "post",
            formData,
            {
                "Content-Type": "multipart/form-data"
            },
            null,
            {},
            null, // No specific success message needed here, rely on API response or default
            "Error sending WhatsApp message" // Custom error message
        );
    }
};

const updateAccessTokenApi = async (leadData) => {
    return await apiRequest("access_token", "post", leadData);
};

const getUserPagesApi = async ({ signal }) => {
    return await apiRequest("userPages", "post", null, null, signal);
};

const getWhatsAppAccountDetails = async (params) => {
    const data = {
        access_token: params.accessToken,
        id: params.id,
    };

    // Pass custom error message to apiRequest
    return await apiRequest(
        "get_accounts_datails",
        "post",
        data,
        {},
        null,
        {},
        null, // No specific success message needed here
        "Failed to get WhatsApp account details" // Custom error message
    );
};

const getWhatsAppAccounts = async (accessToken) => {
    const data = {
        access_token: accessToken,
    };

    // Pass custom error message to apiRequest
    return await apiRequest(
        "get_All_accounts",
        "post",
        data,
        {},
        null,
        {},
        null, // No specific success message needed here
        "Failed to get WhatsApp accounts" // Custom error message
    );
};

const formatDateString = (dateString) => dateString.replace("T", " ");

const getWhatsAppFilterConversations = async (params) => {
    const data = {
        access_token: params.accessToken,
        id: params.id,
        from: formatDateString(params.startDate),
        to: formatDateString(params.endDate),
    };

    // Pass custom error message to apiRequest
    return await apiRequest(
        "filter_details",
        "post",
        data,
        {},
        null,
        {},
        null, // No specific success message needed here
        "Failed to filter WhatsApp conversations" // Custom error message
    );
};

export default {
    getChatsForPageApi,
    getAccessTokenApi,
    getAccountsApi,
    getFormsApi,
    getFormsLeadsApi,
    getPageImgAPI,
    getLeadsByPageApi,
    getMessagesForChatApi,
    getMoreChatsAPI,
    deleteAccessToken,
    sendMessageApi,
    sendWhatsMessageApi,
    updateAccessTokenApi,
    getUserPagesApi,
    getWhatsAppAccountDetails,
    getWhatsAppAccounts,
    getWhatsAppFilterConversations,
    getMessengerChatsForPageApi,
    getInstagramChatsForPageApi,
    getWhatsAppChatsForPageApi,
    getMessagesForWhatsappChatApi
};



