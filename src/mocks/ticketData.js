// Sample ticket data that mimics a backend response
import samplePdf from "../assets/sample.pdf";
import sampleWord from "../assets/file-sample_100kB.doc";
import sampleTxt from "../assets/simple_ticket.txt";
import samplePhoto from "../assets/media/sticker_23.png";
export const sampleTicket = {
  id: 12345,
  title: "Unable to access dashboard after recent update",
  description: "After the latest update (v2.4.3), I'm unable to access my dashboard. The page loads indefinitely and eventually times out with a 504 error. This is happening on both Chrome and Firefox browsers. I've tried clearing cache and cookies but the issue persists.",
  status: "In Progress",
  priority: "High",
  category: "Technical Issue",
  created_at: "2023-11-15T09:23:45Z",
  updated_at: "2023-11-17T14:30:22Z",
  resolved_at: null,
  due_date: "2023-11-20T00:00:00Z",

  // Client information
  client: {
    id: 5678,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Acme Corporation",
    account_type: "Premium"
  },

  // Assigned agent
  assigned_to: {
    id: 91,
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Technical Support",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg"
  },

  // Attachments
  attachments: [
    {
      id: 1001,
      filename: "error_screenshot.png",
      path: "uploads/tickets/12345/error_screenshot.png",
      mime_type: "image/png",
      size: 245678, // bytes
      uploaded_at: "2023-11-15T09:25:12Z",
      uploaded_by: "client",
      preview: samplePhoto,
    },
    {
      id: 1002,
      filename: "browser_console_log.txt",
      path: "uploads/tickets/12345/browser_console_log.txt",
      mime_type: "text/plain",
      size: 12340, // bytes
      uploaded_at: "2023-11-15T09:26:30Z",
      uploaded_by: "client",
      preview: sampleTxt,
    },
    {
      id: 1003,
      filename: "system_info.pdf",
      path: "uploads/tickets/12345/system_info.pdf",
      mime_type: "application/pdf",
      size: 567890, // bytes
      uploaded_at: "2023-11-16T11:42:15Z",
      uploaded_by: "agent"
    },
    {
      id: 1004,
      filename: "sample.pdf",
      path: "uploads/tickets/12345/sample.pdf",
      mime_type: "application/pdf",
      size: 567890, // bytes
      uploaded_at: "2023-11-16T11:42:15Z",
      uploaded_by: "agent",
      preview: samplePdf,
    },
    {
      id: 1005,
      filename: "sample.doc",
      path: "uploads/tickets/12345/sample.doc",
      mime_type: "application/msword",
      size: 567890, // bytes
      uploaded_at: "2023-11-16T11:42:15Z",
      uploaded_by: "agent",
      preview: sampleWord,
    }
  ],

  // Ticket history/timeline
  history: [
    {
      id: 101,
      type: "creation",
      timestamp: "2023-11-15T09:23:45Z",
      user: {
        id: 5678,
        name: "John Smith",
        role: "Client"
      },
      content: "Ticket #12345 created: Unable to access dashboard after recent update",
      metadata: {
        ip_address: "*************",
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      }
    },
    {
      id: 102,
      type: "attachment",
      timestamp: "2023-11-15T09:25:12Z",
      user: {
        id: 5678,
        name: "John Smith",
        role: "Client"
      },
      content: "Added attachment: error_screenshot.png",
      metadata: {
        attachment_id: 1001,
        file_size: "240 KB"
      }
    },
    {
      id: 103,
      type: "attachment",
      timestamp: "2023-11-15T09:26:30Z",
      user: {
        id: 5678,
        name: "John Smith",
        role: "Client"
      },
      content: "Added attachment: browser_console_log.txt",
      metadata: {
        attachment_id: 1002,
        file_size: "12 KB"
      }
    },
    {
      id: 104,
      type: "assignment",
      timestamp: "2023-11-15T10:15:00Z",
      user: {
        id: 45,
        name: "Robert Chen",
        role: "Support Manager"
      },
      content: "Ticket assigned to Sarah Johnson (Technical Support)",
      metadata: {
        previous_assignee: null,
        new_assignee: 91
      }
    },
    {
      id: 105,
      type: "status_change",
      timestamp: "2023-11-15T10:16:22Z",
      user: {
        id: 45,
        name: "Robert Chen",
        role: "Support Manager"
      },
      content: "Status changed from Open to In Progress",
      metadata: {
        previous_status: "Open",
        new_status: "In Progress"
      }
    },
    {
      id: 106,
      type: "priority_change",
      timestamp: "2023-11-15T10:17:05Z",
      user: {
        id: 45,
        name: "Robert Chen",
        role: "Support Manager"
      },
      content: "Priority set to High",
      metadata: {
        previous_priority: "Medium",
        new_priority: "High"
      }
    },
    {
      id: 107,
      type: "comment",
      timestamp: "2023-11-15T14:32:10Z",
      user: {
        id: 91,
        name: "Sarah Johnson",
        role: "Support Agent"
      },
      content: "Hi John, I'm looking into your dashboard access issue. Could you please provide some additional information? What specific error message are you seeing on the screen? Also, are you able to access other parts of the application or is it just the dashboard that's affected?",
      metadata: {
        internal_note: false
      }
    },
    {
      id: 108,
      type: "comment",
      timestamp: "2023-11-15T15:45:22Z",
      user: {
        id: 5678,
        name: "John Smith",
        role: "Client"
      },
      content: "Hi Sarah, thanks for your quick response. The error message is 'Gateway Timeout (504)'. I can access other parts of the application like my profile and settings, but the main dashboard and reports sections both fail to load.",
      metadata: {
        internal_note: false
      }
    },
    {
      id: 109,
      type: "internal_note",
      timestamp: "2023-11-16T09:10:45Z",
      user: {
        id: 91,
        name: "Sarah Johnson",
        role: "Support Agent"
      },
      content: "Checked with the dev team - there appears to be an issue with the database queries for the dashboard in the latest release. They're working on a hotfix (ticket DEV-2023-456).",
      metadata: {
        internal_note: true,
        visible_to_client: false
      }
    },
    {
      id: 110,
      type: "attachment",
      timestamp: "2023-11-16T11:42:15Z",
      user: {
        id: 91,
        name: "Sarah Johnson",
        role: "Support Agent"
      },
      content: "Added attachment: system_info.pdf",
      metadata: {
        attachment_id: 1003,
        file_size: "555 KB"
      }
    },
    {
      id: 111,
      type: "comment",
      timestamp: "2023-11-16T11:45:30Z",
      user: {
        id: 91,
        name: "Sarah Johnson",
        role: "Support Agent"
      },
      content: "Hello John, thank you for the additional information. Our development team has identified the issue with the dashboard loading in the latest update. They're currently working on a fix which should be deployed within the next 24 hours. In the meantime, I've attached a document with instructions for accessing your key dashboard data through an alternative method. Please let me know if this workaround helps you.",
      metadata: {
        internal_note: false
      }
    },
    {
      id: 112,
      type: "comment",
      timestamp: "2023-11-16T13:22:18Z",
      user: {
        id: 5678,
        name: "John Smith",
        role: "Client"
      },
      content: "Thanks Sarah, I'll try the workaround. Looking forward to the fix!",
      metadata: {
        internal_note: false
      }
    },
    {
      id: 113,
      type: "internal_note",
      timestamp: "2023-11-17T08:45:12Z",
      user: {
        id: 91,
        name: "Sarah Johnson",
        role: "Support Agent"
      },
      content: "Hotfix has been deployed to staging. Will be in production by 14:00 today.",
      metadata: {
        internal_note: true,
        visible_to_client: false
      }
    },
    {
      id: 114,
      type: "comment",
      timestamp: "2023-11-17T14:30:22Z",
      user: {
        id: 91,
        name: "Sarah Johnson",
        role: "Support Agent"
      },
      content: "Good news, John! The fix for the dashboard issue has been deployed. Please try accessing your dashboard now and let me know if you're still experiencing any problems. You may need to clear your browser cache or do a hard refresh (Ctrl+F5) to see the changes.",
      metadata: {
        internal_note: false
      }
    }
  ],

  // SLA information
  sla: {
    first_response_target: "2023-11-15T11:23:45Z", // 2 hours from creation
    first_response_actual: "2023-11-15T10:15:00Z",
    first_response_met: true,
    resolution_target: "2023-11-17T17:23:45Z", // 2 business days from creation
    resolution_actual: null,
    resolution_met: null,
    paused_duration: 0 // in seconds
  },

  // Tags
  tags: ["dashboard", "timeout", "v2.4.3", "critical"],

  // Related tickets
  related_tickets: [
    {
      id: 12340,
      title: "Dashboard loading slowly after update",
      status: "Resolved"
    },
    {
      id: 12342,
      title: "Reports section showing incorrect data",
      status: "In Progress"
    }
  ],

  // Custom fields
  custom_fields: {
    browser_version: "Chrome 108.0.5359.124",
    operating_system: "Windows 11",
    account_level: "Enterprise",
    subscription_status: "Active",
    last_successful_login: "2023-11-14T16:42:10Z"
  }
};

// Export a function to get a ticket by ID (for API mocking)
export const getTicketById = (id) => {
  if (id === 12345) {
    return { success: true, data: sampleTicket };
  }
  return { success: false, error: "Ticket not found" };
};

// Export a function to get all tickets (for API mocking)
export const getAllTickets = (page = 1, limit = 10) => {
  const tickets = [
    {
      id: 12345,
      title: "Unable to access dashboard after recent update",
      status: "In Progress",
      priority: "High",
      client: { name: "John Smith", id: 5678 },
      created_at: "2023-11-15T09:23:45Z",
      updated_at: "2023-11-17T14:30:22Z"
    },
    {
      id: 12346,
      title: "Payment method declined",
      status: "Open",
      priority: "Medium",
      client: { name: "Emily Johnson", id: 5679 },
      created_at: "2023-11-16T11:42:30Z",
      updated_at: "2023-11-16T11:42:30Z"
    },
    {
      id: 12347,
      title: "Feature request: Dark mode",
      status: "Open",
      priority: "Low",
      client: { name: "Michael Brown", id: 5680 },
      created_at: "2023-11-14T15:30:00Z",
      updated_at: "2023-11-14T16:45:22Z"
    }
  ];

  return {
    success: true,
    data: {
      tickets: tickets,
      pagination: {
        total: 42,
        page: page,
        limit: limit,
        pages: Math.ceil(42 / limit)
      }
    }
  };
};
