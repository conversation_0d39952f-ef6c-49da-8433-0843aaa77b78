/**
 * Lead Status Configuration
 *
 * This module manages lead status definitions and visibility based on user roles.
 * Special admin users (defined in packageVisibility.js) will see an extended set of statuses.
 */

import { isUserExcluded } from './packageVisibility';

// Basic lead statuses (visible to all users)
const BASIC_LEAD_STATUSES = {
    0: 'Pending',
    1: 'In Progress',
    2: 'Completed',
    3: 'Rejected',
    4: 'Wrong Lead',
    5: 'Not Qualified',
    6: 'No Communication',
    7: 'Booked',
    8: 'Booked and Reserved',
    9: 'Canceled',
    10: 'Quotation',
    11: 'Assigned'
};

// Extended lead statuses (only visible to special admin users)
// Initial status options (before Follow Up selection)
const INITIAL_STATUS_OPTIONS = {
    0: 'Pending',
    11: 'Assigned',
    6: 'No Answer',
    7: 'Booked',
    12: 'Undefined',
    13: 'Advance Paid',
    14: 'Follow Up',
    15: 'Not Interested',
    16: 'Junk',
    17: 'Complaints',
    18: 'Urgent Call',
    19: 'Call Back'
};

// Follow-up specific status options (after Follow Up selection)
const FOLLOW_UP_STATUS_OPTIONS = {
    21: 'Sent SMS',
    25: 'Follow up',
    20: 'Booked',
    23: 'Not interested',
    26: 'Complain',
    22: 'No Action',
    24: 'Whatapp-NAL'
};

// Combined extended statuses for special admin users (no quotation)
const EXTENDED_LEAD_STATUSES = {
    ...INITIAL_STATUS_OPTIONS,
    ...FOLLOW_UP_STATUS_OPTIONS
};

// Statuses that should always be visible regardless of user role
const ALWAYS_VISIBLE_STATUSES = [0, 11]; // Pending and Assigned

/**
 * Get the appropriate lead statuses based on user role
 * @param {number|string} userId - The user ID to check
 * @returns {Object} - Object mapping status codes to status names
 */
export const getLeadStatuses = (userId) => {
    // Special admin users get the extended set of statuses
    if (isUserExcluded(userId)) {
        return EXTENDED_LEAD_STATUSES;
    }

    // Regular users get the basic set of statuses
    return BASIC_LEAD_STATUSES;
};

/**
 * Get the visible status options for a user (for dropdowns, filters, etc.)
 * @param {number|string} userId - The user ID to check
 * @returns {Object} - Object mapping status codes to status names
 */
export const getVisibleStatusOptions = (userId) => {
    // For special admin users, show extended statuses (no quotation)
    if (isUserExcluded(userId)) {
        return EXTENDED_LEAD_STATUSES;
    }

    // Regular users get all basic statuses
    return BASIC_LEAD_STATUSES;
};

/**
 * Check if a status should be visible to a specific user
 * @param {number|string} userId - The user ID to check
 * @param {number|string} statusCode - The status code to check
 * @returns {boolean} - True if the status should be visible to the user
 */
export const isStatusVisibleToUser = (userId, statusCode) => {
    const numericStatusCode = parseInt(statusCode);

    // For special admin users
    if (isUserExcluded(userId)) {
        // Always show statuses 0 and 11 (Pending and Assigned)
        if (ALWAYS_VISIBLE_STATUSES.includes(numericStatusCode)) {
            return true;
        }

        // Show extended statuses (12-26)
        return numericStatusCode >= 12 && numericStatusCode <= 26;
    }

    // For regular users, show all basic statuses
    return numericStatusCode in BASIC_LEAD_STATUSES;
};

/**
 * Get all lead statuses (for reference purposes)
 * @returns {Object} - Object containing all defined lead statuses
 */
export const getAllLeadStatuses = () => {
    return EXTENDED_LEAD_STATUSES;
};

// Export the status option sets
export { INITIAL_STATUS_OPTIONS, FOLLOW_UP_STATUS_OPTIONS };

// Re-export isUserExcluded for convenience
export { isUserExcluded };
