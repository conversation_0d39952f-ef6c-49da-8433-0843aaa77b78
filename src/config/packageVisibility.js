/**
 * Package Visibility Configuration
 *
 * This module manages user-specific visibility control for package/subscription-related functionality.
 * Users in the PACKAGE_EXCLUDED_USERS array will not see package-related features.
 */

// Internal configuration for users who should not see package-related features
let PACKAGE_EXCLUDED_USERS = [423, 1];

// Configuration validation settings
const CONFIG_VALIDATION = {
    MAX_USER_ID: 999999999, // Maximum allowed user ID
    MIN_USER_ID: 1, // Minimum allowed user ID
    MAX_EXCLUDED_USERS: 1000 // Maximum number of excluded users
};

/**
 * Determines if a user should see package-related features
 * @param {number|string} userId - The user ID to check
 * @returns {boolean} - True if user should see package features, false otherwise
 */
export const shouldShowPackageFeatures = (userId) => {
    // Handle null, undefined, or empty userId by defaulting to show features
    if (userId === null || userId === undefined || userId === '') {
        return true;
    }

    // Convert to number for consistent comparison
    const numericUserId = Number(userId);

    // If conversion fails (NaN), default to show features
    if (isNaN(numericUserId)) {
        return true;
    }

    // Return false if user is in excluded list, true otherwise
    return !PACKAGE_EXCLUDED_USERS.includes(numericUserId);
};

/**
 * Checks if a specific user ID is in the exclusion list
 * @param {number|string} userId - The user ID to check
 * @returns {boolean} - True if user is excluded, false otherwise
 */
export const isUserExcluded = (userId) => {
    return !shouldShowPackageFeatures(userId);
};

/**
 * Gets the list of excluded user IDs
 * @returns {number[]} - Array of excluded user IDs
 */
export const getExcludedUsers = () => {
    return [...PACKAGE_EXCLUDED_USERS];
};

/**
 * Validates a user ID for proper format and type
 * @param {any} userId - The user ID to validate
 * @returns {object} - Validation result with isValid boolean and error message
 */
export const validateUserId = (userId) => {
    // Check for null or undefined
    if (userId === null || userId === undefined) {
        return {
            isValid: false,
            error: 'User ID cannot be null or undefined'
        };
    }

    // Check for empty string
    if (userId === '') {
        return {
            isValid: false,
            error: 'User ID cannot be empty'
        };
    }

    // Convert to number
    const numericUserId = Number(userId);

    // Check if conversion resulted in NaN
    if (isNaN(numericUserId)) {
        return {
            isValid: false,
            error: 'User ID must be a valid number'
        };
    }

    // Check if it's an integer
    if (!Number.isInteger(numericUserId)) {
        return {
            isValid: false,
            error: 'User ID must be an integer'
        };
    }

    // Check range constraints
    if (numericUserId < CONFIG_VALIDATION.MIN_USER_ID) {
        return {
            isValid: false,
            error: `User ID must be at least ${CONFIG_VALIDATION.MIN_USER_ID}`
        };
    }

    if (numericUserId > CONFIG_VALIDATION.MAX_USER_ID) {
        return {
            isValid: false,
            error: `User ID cannot exceed ${CONFIG_VALIDATION.MAX_USER_ID}`
        };
    }

    return {
        isValid: true,
        error: null
    };
};

/**
 * Adds a user ID to the exclusion list
 * @param {number|string} userId - The user ID to add
 * @returns {object} - Operation result with success boolean and message
 */
export const addExcludedUser = (userId) => {
    // Validate the user ID
    const validation = validateUserId(userId);
    if (!validation.isValid) {
        return {
            success: false,
            message: `Cannot add user: ${validation.error}`
        };
    }

    const numericUserId = Number(userId);

    // Check if user is already excluded
    if (PACKAGE_EXCLUDED_USERS.includes(numericUserId)) {
        return {
            success: false,
            message: `User ID ${numericUserId} is already in the exclusion list`
        };
    }

    // Check maximum excluded users limit
    if (PACKAGE_EXCLUDED_USERS.length >= CONFIG_VALIDATION.MAX_EXCLUDED_USERS) {
        return {
            success: false,
            message: `Cannot add user: Maximum of ${CONFIG_VALIDATION.MAX_EXCLUDED_USERS} excluded users allowed`
        };
    }

    // Add user to exclusion list
    PACKAGE_EXCLUDED_USERS.push(numericUserId);

    return {
        success: true,
        message: `User ID ${numericUserId} added to exclusion list`
    };
};

/**
 * Removes a user ID from the exclusion list
 * @param {number|string} userId - The user ID to remove
 * @returns {object} - Operation result with success boolean and message
 */
export const removeExcludedUser = (userId) => {
    // Validate the user ID
    const validation = validateUserId(userId);
    if (!validation.isValid) {
        return {
            success: false,
            message: `Cannot remove user: ${validation.error}`
        };
    }

    const numericUserId = Number(userId);

    // Check if user is in the exclusion list
    const userIndex = PACKAGE_EXCLUDED_USERS.indexOf(numericUserId);
    if (userIndex === -1) {
        return {
            success: false,
            message: `User ID ${numericUserId} is not in the exclusion list`
        };
    }

    // Remove user from exclusion list
    PACKAGE_EXCLUDED_USERS.splice(userIndex, 1);

    return {
        success: true,
        message: `User ID ${numericUserId} removed from exclusion list`
    };
};

/**
 * Bulk adds multiple user IDs to the exclusion list
 * @param {Array<number|string>} userIds - Array of user IDs to add
 * @returns {object} - Operation result with success counts and error details
 */
export const addMultipleExcludedUsers = (userIds) => {
    if (!Array.isArray(userIds)) {
        return {
            success: false,
            message: 'Input must be an array of user IDs',
            results: []
        };
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const userId of userIds) {
        const result = addExcludedUser(userId);
        results.push({
            userId,
            success: result.success,
            message: result.message
        });

        if (result.success) {
            successCount++;
        } else {
            errorCount++;
        }
    }

    return {
        success: errorCount === 0,
        message: `Added ${successCount} users, ${errorCount} errors`,
        successCount,
        errorCount,
        results
    };
};

/**
 * Bulk removes multiple user IDs from the exclusion list
 * @param {Array<number|string>} userIds - Array of user IDs to remove
 * @returns {object} - Operation result with success counts and error details
 */
export const removeMultipleExcludedUsers = (userIds) => {
    if (!Array.isArray(userIds)) {
        return {
            success: false,
            message: 'Input must be an array of user IDs',
            results: []
        };
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const userId of userIds) {
        const result = removeExcludedUser(userId);
        results.push({
            userId,
            success: result.success,
            message: result.message
        });

        if (result.success) {
            successCount++;
        } else {
            errorCount++;
        }
    }

    return {
        success: errorCount === 0,
        message: `Removed ${successCount} users, ${errorCount} errors`,
        successCount,
        errorCount,
        results
    };
};

/**
 * Replaces the entire exclusion list with a new list
 * @param {Array<number|string>} userIds - New array of user IDs to exclude
 * @returns {object} - Operation result with success boolean and message
 */
export const setExcludedUsers = (userIds) => {
    if (!Array.isArray(userIds)) {
        return {
            success: false,
            message: 'Input must be an array of user IDs'
        };
    }

    // Check maximum excluded users limit
    if (userIds.length > CONFIG_VALIDATION.MAX_EXCLUDED_USERS) {
        return {
            success: false,
            message: `Cannot set exclusion list: Maximum of ${CONFIG_VALIDATION.MAX_EXCLUDED_USERS} excluded users allowed`
        };
    }

    // Validate all user IDs first
    const validationErrors = [];
    const validatedUserIds = [];

    for (const userId of userIds) {
        const validation = validateUserId(userId);
        if (!validation.isValid) {
            validationErrors.push(`User ID ${userId}: ${validation.error}`);
        } else {
            const numericUserId = Number(userId);
            if (!validatedUserIds.includes(numericUserId)) {
                validatedUserIds.push(numericUserId);
            }
        }
    }

    if (validationErrors.length > 0) {
        return {
            success: false,
            message: `Validation errors: ${validationErrors.join(', ')}`
        };
    }

    // Replace the exclusion list
    PACKAGE_EXCLUDED_USERS = [...validatedUserIds];

    return {
        success: true,
        message: `Exclusion list updated with ${validatedUserIds.length} users`
    };
};

/**
 * Clears all users from the exclusion list
 * @returns {object} - Operation result with success boolean and message
 */
export const clearExcludedUsers = () => {
    const previousCount = PACKAGE_EXCLUDED_USERS.length;
    PACKAGE_EXCLUDED_USERS = [];

    return {
        success: true,
        message: `Cleared ${previousCount} users from exclusion list`
    };
};

/**
 * Gets configuration statistics
 * @returns {object} - Configuration statistics and limits
 */
export const getConfigurationStats = () => {
    return {
        excludedUsersCount: PACKAGE_EXCLUDED_USERS.length,
        maxExcludedUsers: CONFIG_VALIDATION.MAX_EXCLUDED_USERS,
        remainingSlots: CONFIG_VALIDATION.MAX_EXCLUDED_USERS - PACKAGE_EXCLUDED_USERS.length,
        userIdRange: {
            min: CONFIG_VALIDATION.MIN_USER_ID,
            max: CONFIG_VALIDATION.MAX_USER_ID
        },
        excludedUsers: [...PACKAGE_EXCLUDED_USERS]
    };
};
