{"name": "frontend", "version": "0.1.0", "private": true, "homepage": ".", "dependencies": {"@reduxjs/toolkit": "^2.7.0", "@stripe/react-stripe-js": "^2.7.0", "@stripe/stripe-js": "^3.3.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "bootstrap": "^5.3.2", "chart.js": "^4.5.0", "classnames": "^2.5.1", "date-fns": "^3.2.0", "emoji-picker-react": "^4.12.2", "firebase": "^10.7.1", "formik": "^2.4.5", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "lottie-react": "^2.4.0", "mic-recorder-to-mp3": "^2.2.2", "moment": "^2.29.4", "otp-input-react": "^0.3.0", "react": "^18.2.0", "react-big-calendar": "^1.8.5", "react-bootstrap": "^2.9.2", "react-chartjs-2": "^5.3.0", "react-datepicker": "^4.25.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.11", "react-firebase-hooks": "^5.1.1", "react-i18next": "^15.4.1", "react-icons": "^4.12.0", "react-loading-skeleton": "^3.4.0", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^6.19.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-svg": "^16.1.30", "react-table": "^7.8.0", "react-toastify": "^9.1.3", "react-tooltip": "^5.25.0", "reselect": "^5.1.1", "styled-components": "^6.1.1", "swiper": "^11.0.5", "victory": "^36.7.0", "web-vitals": "^2.1.4", "yup": "^1.3.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start:prod": "node dist/app.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@redux-devtools/extension": "^3.3.0", "@types/react": "^18.0.0", "@wixc3/react-board": "^2.3.0", "typescript": "^5.0.0"}}