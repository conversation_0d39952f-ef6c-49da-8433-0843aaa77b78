# Implementation Plan

- [x] 1. Create collection path resolution service
  - Create `src/services/firebase/collectionPaths.js` with centralized path logic
  - Implement `getCollectionPaths()` function that returns consistent paths using chat ID for both messages and comments
  - Add helper function `getChatIdentifier()` to extract correct chat ID based on chat type (not sender ID)
  - Ensure both messages and comments use the same chat ID as document identifier
  - Write unit tests for path resolution logic covering all chat types
  - _Requirements: 1.1, 1.2, 4.1_

- [x] 2. Update message storage logic for dual write support
  - Modify message sending logic in `metaBusinessChatSlice.js` to write to both old (sender ID) and new (chat ID) collection paths
  - Update `sendMessage` thunk to use chat ID as primary identifier, matching comment system
  - Replace sender ID usage with chat ID for message collection document identifiers
  - Implement error handling for dual write scenarios with fallback to old path
  - Add logging to track dual write success/failure rates
  - _Requirements: 1.1, 2.1, 3.2_

- [x] 3. Implement fallback read logic for messages
  - Update `fetchMessages` thunk to read from new chat ID path first, fallback to old sender ID path
  - Modify Firebase listeners in `fetchMessages` to use chat ID as primary collection identifier
  - Update `fetchWhatsAppMessages` to ensure it uses consistent chat ID resolution (phone number)
  - Ensure message retrieval uses same chat ID pattern as comment system
  - Add error handling for read path failures with graceful degradation
  - _Requirements: 1.1, 2.3, 3.4_

- [x] 4. Update Firebase listeners for real-time message updates


  - Modify `onSnapshot` listeners in `fetchMessages` to use chat ID as collection identifier
  - Update `listenToAllWhatsappMessages` to use consistent chat ID resolution (phone number)
  - Ensure all real-time listeners use the same chat ID pattern as comment listeners
  - Replace any remaining sender ID usage with chat ID in listener setup
  - Test listener functionality with new chat ID-based collection paths
  - _Requirements: 1.4, 3.1_


- [ ] 5. Update message deletion and cleanup logic
  - Modify `selectChatAsync` message deletion logic to clean both old (sender ID) and new (chat ID) paths
  - Update nested message collection cleanup to use chat ID as primary identifier
  - Ensure cleanup operations target the same chat ID used by comment system
  - Replace sender ID references with chat ID in deletion logic
  - Add error handling for cleanup operations
  - _Requirements: 1.3, 2.1_


- [x] 6. Create data migration utilities

  - Create `src/utils/firebase/dataMigration.js` with migration helper functions
  - Implement `migrateMessagesForChat()` function to move messages from sender ID to chat ID collection paths
  - Add verification function to compare data integrity before and after migration
  - Create rollback function to revert from chat ID back to sender ID structure if needed
  - Ensure migration preserves all message data while changing collection identifier from sender ID to chat ID
  - _Requirements: 2.1, 2.2_

- [x] 7. Implement migration monitoring and logging


  - Add comprehensive logging to track migration progress and errors
  - Create migration status tracking to monitor which chats have been migrated
  - Implement error reporting for failed migration attempts
  - Add performance monitoring for migration operations
  - _Requirements: 2.1, 2.3_

- [x] 8. Update comment system integration


  - Verify `hybridService.js` uses consistent chat ID extraction logic (no changes needed if already using chat ID)
  - Update `getChatIds()` function to align with new message collection paths using chat ID
  - Ensure comment and message systems use identical chat ID resolution logic
  - Remove any sender ID dependencies and standardize on chat ID for both systems
  - Test cross-system integration between messages and comments using shared chat ID
  - _Requirements: 1.2, 1.3, 4.1_

- [x] 9. Create comprehensive test suite
  - Write unit tests for collection path resolution service
  - Create integration tests for dual write message storage
  - Add tests for fallback read logic and error scenarios
  - Implement end-to-end tests for message and comment consistency
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 10. Update Firebase query optimization

  - Review and optimize Firebase queries to use chat ID-based collection paths consistently
  - Update compound queries that involve both messages and comments to use shared chat ID
  - Replace any remaining sender ID queries with chat ID equivalents
  - Ensure query performance is maintained or improved with new chat ID structure
  - Add query performance monitoring and logging
  - _Requirements: 1.1, 1.4_

- [x] 11. Implement production migration script
  - Create production-ready migration script to move messages from sender ID to chat ID collections
  - Add batch processing to handle large datasets without performance impact
  - Implement progress tracking and resumable migration process
  - Add safety checks and validation before executing sender ID to chat ID migration
  - Include verification that chat ID mapping is correct for each message batch
  - _Requirements: 2.1, 2.2, 3.1_


- [x] 12. Create rollback and cleanup procedures
  - Implement rollback functionality to revert from chat ID back to sender ID collection structure if needed
  - Create cleanup script to remove old sender ID collection paths after successful migration to chat ID
  - Add verification steps to ensure data integrity before cleanup of sender ID collections
  - Document rollback procedures for emergency reversion from chat ID to sender ID structure
  - _Requirements: 2.1, 2.2_

- [x] 13. Update documentation and code comments


  - Update code comments to reflect new chat ID-based collection structure
  - Create developer documentation explaining the new chat ID collection path patterns
  - Add troubleshooting guide for chat ID vs sender ID collection issues
  - Update any existing documentation that references old sender ID collection paths
  - Document the standardized chat ID usage for both messages and comments
  - _Requirements: 4.2, 4.3, 4.4_


- [x] 14. Final integration testing and validation


  - Perform comprehensive testing of message sending and receiving with new chat ID paths
  - Test comment functionality to ensure continued consistency with shared chat ID usage
  - Validate real-time listeners work correctly with new chat ID-based collection structure
  - Verify backward compatibility during transition from sender ID to chat ID
  - Test that both messages and comments use identical chat ID resolution logic
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 3.1, 3.2, 3.3, 3.4_
