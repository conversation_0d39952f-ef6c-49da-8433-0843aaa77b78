# Design Document

## Overview

This design addresses the Firebase collection structure inconsistency between messages and comments by standardizing both to use chat ID as the document identifier. The solution involves updating the message storage logic to align with the existing comment structure, ensuring consistent data access patterns across the application.

## Architecture

### Current State Analysis

**Messages Structure (Inconsistent):**
- Path: `chats/${senderId}/messages`
- Document ID: Uses sender ID (participant ID)
- Used in: `metaBusinessChatSlice.js`, message sending/receiving logic

**Comments Structure (Target Pattern):**
- Path: `chats/${chatId}/comments`
- Document ID: Uses chat ID (backend chat ID)
- Used in: `services/comments/index.js`, comment system

**WhatsApp Collections (Separate but Consistent):**
- Messages: `whatsApp/${phoneNumber}/messages`
- Comments: `whatsApp/${phoneNumber}/comments`
- Both use phone number as identifier (already consistent)

### Target Architecture

**Standardized Structure:**
- Messages: `chats/${chatId}/messages` (changed from senderId to chatId)
- Comments: `chats/${chatId}/comments` (no change - already correct)
- WhatsApp Messages: `whatsApp/${phoneNumber}/messages` (no change)
- WhatsApp Comments: `whatsApp/${phoneNumber}/comments` (no change)

## Components and Interfaces

### 1. Collection Path Resolution Service

**Purpose:** Centralize collection path logic for consistency

```javascript
// services/firebase/collectionPaths.js
export const getCollectionPaths = (chatId, chatType, selectedChat) => {
  if (chatType === 'whatsapp') {
    return {
      messages: `whatsApp/${chatId}/messages`,
      comments: `whatsApp/${chatId}/comments`
    };
  }

  // For messenger/instagram, use backend chat ID consistently
  const backendChatId = selectedChat?.id || chatId;
  return {
    messages: `chats/${backendChatId}/messages`,
    comments: `chats/${backendChatId}/comments`
  };
};
```

### 2. Message Storage Updates

**Files to Update:**
- `src/redux/features/metaBusinessChatSlice.js`
- Message sending logic in chat components

**Key Changes:**
- Replace `senderId` with `chatId` in message collection paths
- Update Firebase listeners to use consistent identifiers
- Modify message retrieval logic to use backend chat ID

### 3. Migration Strategy

**Phase 1: Dual Write (Backward Compatibility)**
- Write messages to both old and new collection paths
- Read from new path first, fallback to old path
- Maintain existing functionality during transition

**Phase 2: Data Migration**
- Background process to move existing messages from old to new structure
- Verification process to ensure data integrity
- Cleanup of old collection paths after verification

**Phase 3: Single Write (New Structure Only)**
- Remove dual write logic
- Use only new consistent collection paths
- Update all Firebase listeners and queries

## Data Models

### Message Document Structure

```javascript
// No changes to message document structure
{
  id: string,
  message: string,
  sender: string,
  recipient: string,
  created_time: timestamp,
  type: string,
  message_id: string
}
```

### Collection Identifier Mapping

```javascript
// Helper function to get correct chat identifier
const getChatIdentifier = (selectedChat, chatType) => {
  if (chatType === 'whatsapp') {
    return selectedChat.sender_phone_number?.trim().replace(/^\+|\s+/g, "");
  }

  // For messenger/instagram, use backend chat ID
  return selectedChat.id;
};
```

## Error Handling

### Migration Error Scenarios

1. **Dual Write Failures**
   - If new path write fails, continue with old path
   - Log errors for monitoring and retry logic
   - Ensure user experience is not disrupted

2. **Data Migration Failures**
   - Implement retry logic with exponential backoff
   - Maintain detailed logs of migration progress
   - Rollback capability if critical errors occur

3. **Read Path Fallbacks**
   - Primary read from new collection path
   - Fallback to old path if new path is empty
   - Graceful handling of missing documents

### Runtime Error Handling

```javascript
const getMessagesWithFallback = async (chatId, senderId) => {
  try {
    // Try new consistent path first
    const newPath = `chats/${chatId}/messages`;
    const messages = await getMessagesFromPath(newPath);

    if (messages.length > 0) {
      return messages;
    }

    // Fallback to old path during migration
    const oldPath = `chats/${senderId}/messages`;
    return await getMessagesFromPath(oldPath);
  } catch (error) {
    console.error('Error fetching messages:', error);
    return [];
  }
};
```

## Testing Strategy

### Unit Tests

1. **Collection Path Resolution**
   - Test path generation for different chat types
   - Verify correct identifier usage
   - Test edge cases and invalid inputs

2. **Message Storage Logic**
   - Test dual write functionality during migration
   - Verify fallback read logic
   - Test error handling scenarios

### Integration Tests

1. **End-to-End Message Flow**
   - Send message and verify storage in correct collection
   - Retrieve messages and verify correct path usage
   - Test real-time listeners with new paths

2. **Comments Integration**
   - Verify messages and comments use same chat identifier
   - Test cross-collection queries and operations
   - Validate data consistency between collections

### Migration Tests

1. **Data Migration Verification**
   - Compare data before and after migration
   - Verify no data loss during migration process
   - Test rollback procedures

2. **Backward Compatibility**
   - Test application with mixed old/new data
   - Verify graceful handling of legacy collection paths
   - Test user experience during migration period

## Implementation Phases

### Phase 1: Foundation (Dual Write Support)
- Create collection path resolution service
- Update message sending to write to both paths
- Implement fallback read logic
- Add comprehensive logging

### Phase 2: Migration Preparation
- Create data migration scripts
- Implement migration monitoring
- Add rollback capabilities
- Test migration process in development

### Phase 3: Production Migration
- Execute data migration in production
- Monitor system performance and errors
- Verify data integrity post-migration
- Clean up old collection paths

### Phase 4: Cleanup
- Remove dual write logic
- Update all code to use new paths only
- Remove fallback read logic
- Update documentation and comments
